# JharkhandAI Backend

This is the backend for the JharkhandAI platform, providing APIs for various services including language processing, speech recognition, computer vision, agriculture, healthcare, jobs, education, government services, and tourism.

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Initialize the database:
```bash
python initialize_database.py
```

3. Run the server:
```bash
uvicorn main:app --reload
```

## API Documentation

Once the server is running, you can access the API documentation at:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Authentication

The API uses JWT-based authentication. To authenticate:

1. Register a user:
```
POST /api/auth/register
```

2. Get an access token:
```
POST /api/auth/token
```

3. Use the token in the Authorization header:
```
Authorization: Bearer <your_token>
```

## Available Routes

- `/api/language` - Language processing APIs
- `/api/speech` - Speech recognition APIs
- `/api/vision` - Computer vision APIs
- `/api/agriculture` - Agriculture-related APIs
- `/api/healthcare` - Healthcare-related APIs
- `/api/jobs` - Job search and application APIs
- `/api/chatbot` - Chatbot APIs
- `/api/education` - Education-related APIs
- `/api/government` - Government services APIs
- `/api/tourism` - Tourism-related APIs
