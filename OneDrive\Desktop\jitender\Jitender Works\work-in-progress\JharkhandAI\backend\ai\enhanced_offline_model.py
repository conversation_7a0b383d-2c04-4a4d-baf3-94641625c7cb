"""
Enhanced Offline Model Manager for Jharkhand AI.
This module provides functionality for managing AI models in offline mode,
ensuring the application works in low-connectivity areas.
"""

import os
import json
import logging
import shutil
import hashlib
from typing import Dict, List, Optional, Union, Any
from pathlib import Path
import torch
import numpy as np
import requests
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OfflineModelManager:
    """
    Manager for handling offline AI models.
    Provides functionality for downloading, syncing, and managing models for offline use.
    """
    
    def __init__(
        self, 
        base_dir: str = "models",
        model_registry_url: Optional[str] = None,
        check_updates_on_init: bool = False
    ):
        """
        Initialize the Offline Model Manager.
        
        Args:
            base_dir: Base directory for storing models
            model_registry_url: URL to the model registry for updates
            check_updates_on_init: Whether to check for updates on initialization
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self.model_registry_url = model_registry_url
        self.model_registry = self._load_model_registry()
        
        # Create subdirectories for different model types
        self.language_dir = self.base_dir / "language"
        self.speech_dir = self.base_dir / "speech"
        self.vision_dir = self.base_dir / "vision"
        
        for directory in [self.language_dir, self.speech_dir, self.vision_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        if check_updates_on_init and model_registry_url:
            self.check_for_updates()
    
    def _load_model_registry(self) -> Dict[str, Any]:
        """Load the local model registry."""
        registry_path = self.base_dir / "model_registry.json"
        
        if not registry_path.exists():
            # Create default registry if it doesn't exist
            default_registry = {
                "version": "1.0.0",
                "last_updated": "",
                "models": {
                    "language": {},
                    "speech": {},
                    "vision": {}
                }
            }
            
            with open(registry_path, "w") as f:
                json.dump(default_registry, f, indent=2)
                
            logger.info("Created default model registry")
            return default_registry
        
        # Load existing registry
        try:
            with open(registry_path, "r") as f:
                registry = json.load(f)
            logger.info("Loaded existing model registry")
            return registry
        except Exception as e:
            logger.error(f"Error loading model registry: {str(e)}")
            # Return default registry if loading fails
            return {
                "version": "1.0.0",
                "last_updated": "",
                "models": {
                    "language": {},
                    "speech": {},
                    "vision": {}
                }
            }
    
    def _save_model_registry(self):
        """Save the model registry to disk."""
        registry_path = self.base_dir / "model_registry.json"
        
        try:
            with open(registry_path, "w") as f:
                json.dump(self.model_registry, f, indent=2)
            logger.info("Saved model registry")
        except Exception as e:
            logger.error(f"Error saving model registry: {str(e)}")
    
    def check_for_updates(self) -> bool:
        """
        Check for updates to the model registry.
        
        Returns:
            True if updates are available, False otherwise
        """
        if not self.model_registry_url:
            logger.warning("No model registry URL provided, cannot check for updates")
            return False
        
        try:
            # Fetch remote registry
            response = requests.get(self.model_registry_url)
            response.raise_for_status()
            remote_registry = response.json()
            
            # Compare versions
            local_version = self.model_registry.get("version", "0.0.0")
            remote_version = remote_registry.get("version", "0.0.0")
            
            if self._compare_versions(remote_version, local_version) > 0:
                logger.info(f"Updates available: local version {local_version}, remote version {remote_version}")
                return True
            else:
                logger.info(f"No updates available: local version {local_version}, remote version {remote_version}")
                return False
        except Exception as e:
            logger.error(f"Error checking for updates: {str(e)}")
            return False
    
    def _compare_versions(self, version1: str, version2: str) -> int:
        """
        Compare two version strings.
        
        Args:
            version1: First version string
            version2: Second version string
            
        Returns:
            1 if version1 > version2, -1 if version1 < version2, 0 if equal
        """
        v1_parts = [int(x) for x in version1.split(".")]
        v2_parts = [int(x) for x in version2.split(".")]
        
        for i in range(max(len(v1_parts), len(v2_parts))):
            v1 = v1_parts[i] if i < len(v1_parts) else 0
            v2 = v2_parts[i] if i < len(v2_parts) else 0
            
            if v1 > v2:
                return 1
            elif v1 < v2:
                return -1
                
        return 0
    
    def update_models(self, model_type: Optional[str] = None) -> bool:
        """
        Update models from the remote registry.
        
        Args:
            model_type: Type of models to update ('language', 'speech', 'vision', or None for all)
            
        Returns:
            True if update was successful, False otherwise
        """
        if not self.model_registry_url:
            logger.warning("No model registry URL provided, cannot update models")
            return False
        
        try:
            # Fetch remote registry
            response = requests.get(self.model_registry_url)
            response.raise_for_status()
            remote_registry = response.json()
            
            # Determine which model types to update
            model_types = [model_type] if model_type else ["language", "speech", "vision"]
            
            # Update each model type
            for mtype in model_types:
                if mtype not in remote_registry["models"]:
                    logger.warning(f"Model type {mtype} not found in remote registry")
                    continue
                
                remote_models = remote_registry["models"][mtype]
                local_models = self.model_registry["models"].get(mtype, {})
                
                # Check each model
                for model_id, model_info in remote_models.items():
                    # Check if model exists locally and has the same version
                    if (model_id not in local_models or 
                        self._compare_versions(model_info["version"], local_models[model_id]["version"]) > 0):
                        # Download the model
                        success = self._download_model(model_id, model_info, mtype)
                        if success:
                            # Update local registry
                            self.model_registry["models"].setdefault(mtype, {})[model_id] = model_info
                
            # Update registry version and save
            self.model_registry["version"] = remote_registry["version"]
            self.model_registry["last_updated"] = remote_registry["last_updated"]
            self._save_model_registry()
            
            logger.info("Model update complete")
            return True
        except Exception as e:
            logger.error(f"Error updating models: {str(e)}")
            return False
    
    def _download_model(self, model_id: str, model_info: Dict[str, Any], model_type: str) -> bool:
        """
        Download a model from the remote URL.
        
        Args:
            model_id: ID of the model
            model_info: Information about the model
            model_type: Type of the model ('language', 'speech', 'vision')
            
        Returns:
            True if download was successful, False otherwise
        """
        if "download_url" not in model_info:
            logger.warning(f"No download URL for model {model_id}")
            return False
        
        download_url = model_info["download_url"]
        model_dir = self._get_model_dir(model_type) / model_id
        
        try:
            # Create model directory
            model_dir.mkdir(parents=True, exist_ok=True)
            
            # Download the model file
            logger.info(f"Downloading model {model_id} from {download_url}")
            
            # Stream the download with progress bar
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            # Get file size if available
            total_size = int(response.headers.get("content-length", 0))
            block_size = 1024  # 1 KB
            
            # Download path
            download_path = model_dir / f"{model_id}.zip"
            
            with open(download_path, "wb") as f, tqdm(
                desc=f"Downloading {model_id}",
                total=total_size,
                unit="B",
                unit_scale=True,
                unit_divisor=1024,
            ) as progress_bar:
                for data in response.iter_content(block_size):
                    f.write(data)
                    progress_bar.update(len(data))
            
            # Verify download
            if "checksum" in model_info:
                if not self._verify_checksum(download_path, model_info["checksum"]):
                    logger.error(f"Checksum verification failed for {model_id}")
                    return False
            
            # Extract the model if it's a zip file
            if download_path.suffix == ".zip":
                logger.info(f"Extracting model {model_id}")
                shutil.unpack_archive(download_path, model_dir)
                
                # Remove the zip file after extraction
                download_path.unlink()
            
            logger.info(f"Successfully downloaded and installed model {model_id}")
            return True
        except Exception as e:
            logger.error(f"Error downloading model {model_id}: {str(e)}")
            return False
    
    def _verify_checksum(self, file_path: Path, expected_checksum: str) -> bool:
        """
        Verify the checksum of a downloaded file.
        
        Args:
            file_path: Path to the file
            expected_checksum: Expected checksum
            
        Returns:
            True if checksum matches, False otherwise
        """
        try:
            # Calculate SHA-256 checksum
            sha256_hash = hashlib.sha256()
            
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            
            calculated_checksum = sha256_hash.hexdigest()
            
            # Compare checksums
            if calculated_checksum == expected_checksum:
                logger.info(f"Checksum verification successful for {file_path}")
                return True
            else:
                logger.error(f"Checksum verification failed for {file_path}")
                logger.error(f"Expected: {expected_checksum}")
                logger.error(f"Calculated: {calculated_checksum}")
                return False
        except Exception as e:
            logger.error(f"Error verifying checksum: {str(e)}")
            return False
    
    def _get_model_dir(self, model_type: str) -> Path:
        """Get the directory for a specific model type."""
        if model_type == "language":
            return self.language_dir
        elif model_type == "speech":
            return self.speech_dir
        elif model_type == "vision":
            return self.vision_dir
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    def list_available_models(self, model_type: Optional[str] = None) -> Dict[str, List[str]]:
        """
        List available models.
        
        Args:
            model_type: Type of models to list ('language', 'speech', 'vision', or None for all)
            
        Returns:
            Dictionary mapping model types to lists of model IDs
        """
        result = {}
        
        # Determine which model types to list
        model_types = [model_type] if model_type else ["language", "speech", "vision"]
        
        for mtype in model_types:
            if mtype in self.model_registry["models"]:
                result[mtype] = list(self.model_registry["models"][mtype].keys())
            else:
                result[mtype] = []
                
        return result
    
    def get_model_info(self, model_id: str, model_type: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific model.
        
        Args:
            model_id: ID of the model
            model_type: Type of the model ('language', 'speech', 'vision')
            
        Returns:
            Dictionary with model information, or None if not found
        """
        if model_type not in self.model_registry["models"]:
            return None
            
        return self.model_registry["models"][model_type].get(model_id)
    
    def is_model_available(self, model_id: str, model_type: str) -> bool:
        """
        Check if a model is available locally.
        
        Args:
            model_id: ID of the model
            model_type: Type of the model ('language', 'speech', 'vision')
            
        Returns:
            True if the model is available, False otherwise
        """
        model_dir = self._get_model_dir(model_type) / model_id
        return model_dir.exists() and any(model_dir.iterdir())
    
    def get_model_path(self, model_id: str, model_type: str) -> Optional[Path]:
        """
        Get the path to a model.
        
        Args:
            model_id: ID of the model
            model_type: Type of the model ('language', 'speech', 'vision')
            
        Returns:
            Path to the model directory, or None if not found
        """
        model_dir = self._get_model_dir(model_type) / model_id
        
        if not model_dir.exists():
            return None
            
        return model_dir
    
    def create_model_package(
        self, 
        model_id: str, 
        model_type: str,
        model_path: str,
        version: str,
        description: str,
        output_path: Optional[str] = None
    ) -> Optional[str]:
        """
        Create a model package for distribution.
        
        Args:
            model_id: ID of the model
            model_type: Type of the model ('language', 'speech', 'vision')
            model_path: Path to the model files
            version: Version of the model
            description: Description of the model
            output_path: Path to save the package (optional)
            
        Returns:
            Path to the created package, or None if failed
        """
        if model_type not in ["language", "speech", "vision"]:
            logger.error(f"Invalid model type: {model_type}")
            return None
            
        try:
            # Create temporary directory for packaging
            temp_dir = self.base_dir / "temp" / model_id
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy model files
            source_path = Path(model_path)
            if not source_path.exists():
                logger.error(f"Model path does not exist: {model_path}")
                return None
                
            # Copy files
            if source_path.is_dir():
                shutil.copytree(source_path, temp_dir / "model", dirs_exist_ok=True)
            else:
                shutil.copy2(source_path, temp_dir / "model")
            
            # Create metadata file
            metadata = {
                "id": model_id,
                "type": model_type,
                "version": version,
                "description": description,
                "created_at": str(datetime.datetime.now())
            }
            
            with open(temp_dir / "metadata.json", "w") as f:
                json.dump(metadata, f, indent=2)
            
            # Create package
            if output_path is None:
                output_path = str(self.base_dir / f"{model_id}_{version}.zip")
                
            shutil.make_archive(
                output_path.rstrip(".zip"),
                "zip",
                root_dir=temp_dir
            )
            
            # Clean up temporary directory
            shutil.rmtree(temp_dir)
            
            logger.info(f"Created model package at {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Error creating model package: {str(e)}")
            return None
    
    def import_model_package(self, package_path: str) -> bool:
        """
        Import a model package.
        
        Args:
            package_path: Path to the model package
            
        Returns:
            True if import was successful, False otherwise
        """
        try:
            # Create temporary directory for extraction
            temp_dir = self.base_dir / "temp" / "import"
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            # Extract package
            shutil.unpack_archive(package_path, temp_dir)
            
            # Load metadata
            metadata_path = temp_dir / "metadata.json"
            if not metadata_path.exists():
                logger.error(f"Invalid model package: metadata.json not found")
                return False
                
            with open(metadata_path, "r") as f:
                metadata = json.load(f)
            
            # Validate metadata
            required_fields = ["id", "type", "version", "description"]
            for field in required_fields:
                if field not in metadata:
                    logger.error(f"Invalid model package: missing {field} in metadata")
                    return False
            
            model_id = metadata["id"]
            model_type = metadata["type"]
            
            # Get target directory
            target_dir = self._get_model_dir(model_type) / model_id
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy model files
            model_path = temp_dir / "model"
            if model_path.is_dir():
                for item in model_path.iterdir():
                    if item.is_dir():
                        shutil.copytree(item, target_dir / item.name, dirs_exist_ok=True)
                    else:
                        shutil.copy2(item, target_dir / item.name)
            else:
                shutil.copy2(model_path, target_dir / model_path.name)
            
            # Update registry
            self.model_registry["models"].setdefault(model_type, {})[model_id] = {
                "version": metadata["version"],
                "description": metadata["description"],
                "local_path": str(target_dir)
            }
            self._save_model_registry()
            
            # Clean up temporary directory
            shutil.rmtree(temp_dir)
            
            logger.info(f"Successfully imported model {model_id}")
            return True
        except Exception as e:
            logger.error(f"Error importing model package: {str(e)}")
            return False
    
    def delete_model(self, model_id: str, model_type: str) -> bool:
        """
        Delete a model.
        
        Args:
            model_id: ID of the model
            model_type: Type of the model ('language', 'speech', 'vision')
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            # Check if model exists
            if not self.is_model_available(model_id, model_type):
                logger.warning(f"Model {model_id} not found")
                return False
            
            # Delete model directory
            model_dir = self._get_model_dir(model_type) / model_id
            shutil.rmtree(model_dir)
            
            # Update registry
            if model_type in self.model_registry["models"] and model_id in self.model_registry["models"][model_type]:
                del self.model_registry["models"][model_type][model_id]
                self._save_model_registry()
            
            logger.info(f"Successfully deleted model {model_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting model {model_id}: {str(e)}")
            return False
