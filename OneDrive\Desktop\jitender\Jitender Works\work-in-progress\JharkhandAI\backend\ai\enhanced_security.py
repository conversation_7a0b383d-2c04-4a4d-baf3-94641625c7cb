import os
import logging
import torch
import numpy as np
from datetime import datetime
from cryptography.fernet import <PERSON><PERSON><PERSON>
from typing import Dict, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class EnhancedSecurity:
    """Enhanced security features for JharkhandAI platform
    Implements advanced encryption, federated learning, and secure model updates
    """
    
    def __init__(self, model_dir: str = "models/secure"):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize encryption key with proper key rotation
        self.key_rotation_interval = 30  # days
        self.last_key_rotation = None
        self.key = self._get_or_generate_key()
        self.cipher_suite = Fernet(self.key)
        
        # Audit logging setup
        self.audit_logger = logging.getLogger('security_audit')
        self.audit_logger.setLevel(logging.INFO)
        audit_handler = logging.FileHandler('security_audit.log')
        audit_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
        self.audit_logger.addHandler(audit_handler)
        
        # Federated learning settings
        self.min_clients = 3  # Minimum number of clients for model update
        self.aggregation_threshold = 0.7  # Required client participation
        
        # Data protection compliance settings
        self.data_retention_period = 180  # days
        self.pii_fields = ['name', 'email', 'phone', 'aadhar', 'pan']
        self.consent_required = True
        
    def _get_or_generate_key(self) -> bytes:
        """Get existing key or generate new one with rotation policy"""
        key = os.getenv('ENCRYPTION_KEY')
        if key:
            return key.encode()
        
        key = Fernet.generate_key()
        self.last_key_rotation = datetime.now()
        return key
    
    def _check_key_rotation(self):
        """Check and rotate encryption key if needed"""
        if not self.last_key_rotation:
            return
        
        days_since_rotation = (datetime.now() - self.last_key_rotation).days
        if days_since_rotation >= self.key_rotation_interval:
            self.key = Fernet.generate_key()
            self.cipher_suite = Fernet(self.key)
            self.last_key_rotation = datetime.now()
            self.audit_logger.info('Encryption key rotated')
    
    def encrypt_data(self, data: bytes, data_type: Optional[str] = None) -> bytes:
        """Encrypt sensitive data using Fernet symmetric encryption"""
        try:
            self._check_key_rotation()
            
            # Log encryption operation without sensitive data
            self.audit_logger.info(f'Data encryption performed - Type: {data_type}')
            
            # Check for PII data
            if data_type and data_type in self.pii_fields and self.consent_required:
                self.audit_logger.info(f'PII data encryption - Type: {data_type}')
            
            return self.cipher_suite.encrypt(data)
        except Exception as e:
            self.audit_logger.error(f'Encryption error: {str(e)}')
            logger.error(f'Encryption error: {str(e)}')
            raise
    
    def decrypt_data(self, encrypted_data: bytes) -> bytes:
        """Decrypt encrypted data"""
        try:
            return self.cipher_suite.decrypt(encrypted_data)
        except Exception as e:
            logger.error(f"Decryption error: {str(e)}")
            raise
    
    def aggregate_model_updates(self, client_updates: List[Dict]) -> Dict:
        """Aggregate model updates from multiple clients using federated averaging"""
        try:
            if len(client_updates) < self.min_clients:
                raise ValueError(f"Need at least {self.min_clients} clients for aggregation")
            
            # Validate client updates
            valid_updates = self._validate_client_updates(client_updates)
            if len(valid_updates) / len(client_updates) < self.aggregation_threshold:
                raise ValueError("Insufficient valid client updates")
            
            # Perform federated averaging
            aggregated_weights = {}
            for param_name in valid_updates[0]['weights'].keys():
                weights = [update['weights'][param_name] for update in valid_updates]
                aggregated_weights[param_name] = np.mean(weights, axis=0)
            
            return {
                'aggregated_weights': aggregated_weights,
                'num_clients': len(valid_updates)
            }
            
        except Exception as e:
            logger.error(f"Model aggregation error: {str(e)}")
            raise
    
    def _validate_client_updates(self, updates: List[Dict]) -> List[Dict]:
        """Validate client updates for security and quality"""
        valid_updates = []
        for update in updates:
            if self._check_update_integrity(update) and self._check_update_quality(update):
                valid_updates.append(update)
        return valid_updates
    
    def _check_update_integrity(self, update: Dict) -> bool:
        """Check integrity of client update"""
        required_keys = ['client_id', 'weights', 'metadata']
        return all(key in update for key in required_keys)
    
    def _check_update_quality(self, update: Dict) -> bool:
        """Check quality of model update"""
        try:
            weights = update['weights']
            # Check for NaN or infinite values
            return all(np.all(np.isfinite(w)) for w in weights.values())
        except Exception:
            return False
    
    def optimize_for_edge(self, model: torch.nn.Module) -> torch.nn.Module:
        """Optimize model for edge deployment"""
        try:
            # Quantize model
            quantized_model = torch.quantization.quantize_dynamic(
                model, {torch.nn.Linear, torch.nn.Conv2d}, dtype=torch.qint8
            )
            
            # Prune model
            parameters_to_prune = [
                (module, 'weight')
                for module in model.modules()
                if isinstance(module, (torch.nn.Conv2d, torch.nn.Linear))
            ]
            torch.nn.utils.prune.global_unstructured(
                parameters_to_prune,
                pruning_method=torch.nn.utils.prune.L1Unstructured,
                amount=0.2  # Prune 20% of connections
            )
            
            return quantized_model
            
        except Exception as e:
            logger.error(f"Model optimization error: {str(e)}")
            raise
    
    def implement_battery_optimization(self, model: torch.nn.Module) -> Dict:
        """Implement battery optimization techniques"""
        try:
            optimizations = {
                'batch_size_adjusted': False,
                'compute_optimized': False,
                'memory_optimized': False
            }
            
            # Adjust batch size based on device capabilities
            if self.device == 'cpu':
                optimizations['batch_size_adjusted'] = True
                
            # Optimize compute operations
            model.half()  # Convert to half precision
            optimizations['compute_optimized'] = True
            
            # Optimize memory usage
            torch.cuda.empty_cache()
            optimizations['memory_optimized'] = True
            
            return optimizations
            
        except Exception as e:
            logger.error(f"Battery optimization error: {str(e)}")
            raise