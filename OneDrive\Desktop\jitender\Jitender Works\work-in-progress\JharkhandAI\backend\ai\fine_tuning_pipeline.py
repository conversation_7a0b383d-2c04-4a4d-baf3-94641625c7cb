\"""Fine-tuning pipeline for language and speech models with Jharkhand-specific data."""

import os
import logging
from typing import Dict, Any, Optional
import torch
from transformers import (
    Trainer,
    TrainingArguments,
    DataCollatorForLanguageModeling,
    Wav2Vec2ForCTC,
    Wav2Vec2Processor,
    AutoModelForSeq2SeqLM,
    AutoTokenizer
)
from datasets import Dataset, load_dataset
import mlflow
from ..config.fine_tuning_config import (
    LANGUAGE_MODEL_CONFIG,
    SPEECH_MODEL_CONFIG,
    EVALUATION_METRICS,
    TRAINING_PIPELINE
)
from .model_versioning import ModelRegistry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FineTuningPipeline:
    """Pipeline for fine-tuning language and speech models."""
    
    def __init__(
        self,
        model_type: str,
        language: str,
        registry_dir: str = "./model_registry",
        tracking_uri: str = "sqlite:///mlflow.db"
    ):
        """Initialize the fine-tuning pipeline.
        
        Args:
            model_type: Type of model ('language' or 'speech')
            language: Language code (e.g., 'sa' for Santhali)
            registry_dir: Directory for model registry
            tracking_uri: MLflow tracking URI
        """
        self.model_type = model_type
        self.language = language
        self.config = LANGUAGE_MODEL_CONFIG if model_type == 'language' else SPEECH_MODEL_CONFIG
        self.registry = ModelRegistry(registry_dir, tracking_uri)
        
        # Set device
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Initialize model and tokenizer/processor
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize model, tokenizer/processor based on model type."""
        try:
            if self.model_type == 'language':
                self.tokenizer = AutoTokenizer.from_pretrained(self.config['base_model'])
                self.model = AutoModelForSeq2SeqLM.from_pretrained(
                    self.config['base_model']
                ).to(self.device)
            else:  # speech
                self.processor = Wav2Vec2Processor.from_pretrained(self.config['base_model'])
                self.model = Wav2Vec2ForCTC.from_pretrained(
                    self.config['base_model']
                ).to(self.device)
            
            logger.info(f"Initialized {self.model_type} model components successfully")
        except Exception as e:
            logger.error(f"Error initializing model components: {str(e)}")
            raise
    
    def prepare_data(self):
        """Prepare and load training data."""
        lang_config = self.config['languages'][self.language]
        data_path = lang_config['data_path']
        
        try:
            if self.model_type == 'language':
                # Load text data
                dataset = load_dataset('text', data_files=data_path)
                # Tokenize data
                tokenized_dataset = dataset.map(
                    lambda x: self.tokenizer(x['text'], truncation=True, padding='max_length'),
                    batched=True
                )
            else:  # speech
                # Load audio data
                dataset = load_dataset('audio', data_files=data_path)
                # Process audio data
                processed_dataset = dataset.map(
                    lambda x: self.processor(x['audio']['array'], sampling_rate=16000),
                    batched=True
                )
            
            logger.info(f"Data preparation completed for {self.language} {self.model_type} model")
            return tokenized_dataset if self.model_type == 'language' else processed_dataset
        
        except Exception as e:
            logger.error(f"Error preparing data: {str(e)}")
            raise
    
    def train(self, dataset: Dataset):
        """Train the model using prepared dataset."""
        try:
            # Set up training arguments
            training_args = TrainingArguments(
                output_dir=f"./results/{self.model_type}_{self.language}",
                **self.config['training_params'],
                **{k: v for k, v in TRAINING_PIPELINE.items() if k != 'mlflow_tracking'}
            )
            
            # Initialize trainer
            trainer = Trainer(
                model=self.model,
                args=training_args,
                train_dataset=dataset['train'],
                eval_dataset=dataset['validation'],
                data_collator=DataCollatorForLanguageModeling(self.tokenizer) if self.model_type == 'language' else None
            )
            
            # Start MLflow run
            with mlflow.start_run() as run:
                # Log parameters
                mlflow.log_params(self.config['training_params'])
                
                # Train model
                train_result = trainer.train()
                
                # Log metrics
                mlflow.log_metrics(train_result.metrics)
                
                # Save model
                trainer.save_model()
                
                # Register model version
                self.registry.register_model(
                    model_type=self.model_type,
                    model_name=f"{self.language}_model",
                    version=self._get_next_version(),
                    run_id=run.info.run_id,
                    metadata={
                        'language': self.language,
                        'metrics': train_result.metrics
                    }
                )
            
            logger.info(f"Training completed for {self.language} {self.model_type} model")
            return train_result
        
        except Exception as e:
            logger.error(f"Error during training: {str(e)}")
            raise
    
    def _get_next_version(self) -> int:
        """Get next version number for model registration."""
        model_key = f"{self.model_type}_{self.language}_model"
        if model_key in self.registry.registry_metadata['models']:
            return len(self.registry.registry_metadata['models'][model_key]['versions']) + 1
        return 1