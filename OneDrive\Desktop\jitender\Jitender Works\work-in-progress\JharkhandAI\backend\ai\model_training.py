"""
Model Training Pipeline for JharkhandAI

This module provides functionality for continuous model training and improvement.
It includes data collection, preprocessing, training, and evaluation components.
"""

import os
import json
import logging
import datetime
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
import mlflow
import mlflow.pytorch
from celery import Celery

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Celery for background tasks
celery_app = Celery('model_training', broker='redis://localhost:6379/0')

class ModelTrainingPipeline:
    """
    A pipeline for continuous model training and improvement.
    """
    
    def __init__(self, 
                 model_type: str,
                 model_name: str,
                 tracking_uri: str = "sqlite:///mlflow.db",
                 artifacts_dir: str = "./model_artifacts"):
        """
        Initialize the model training pipeline.
        
        Args:
            model_type: Type of model (language, speech, vision)
            model_name: Name of the model
            tracking_uri: URI for MLflow tracking server
            artifacts_dir: Directory to store model artifacts
        """
        self.model_type = model_type
        self.model_name = model_name
        self.tracking_uri = tracking_uri
        self.artifacts_dir = artifacts_dir
        
        # Set up MLflow
        mlflow.set_tracking_uri(tracking_uri)
        
        # Create artifacts directory if it doesn't exist
        os.makedirs(artifacts_dir, exist_ok=True)
        
        # Initialize training history
        self.training_history_path = os.path.join(
            artifacts_dir, f"{model_type}_{model_name}_training_history.json"
        )
        self._initialize_training_history()
    
    def _initialize_training_history(self):
        """Initialize or load training history."""
        if os.path.exists(self.training_history_path):
            with open(self.training_history_path, 'r') as f:
                self.training_history = json.load(f)
        else:
            self.training_history = {
                "model_type": self.model_type,
                "model_name": self.model_name,
                "versions": [],
                "current_version": None,
                "last_training": None,
                "total_training_runs": 0
            }
            self._save_training_history()
    
    def _save_training_history(self):
        """Save training history to disk."""
        with open(self.training_history_path, 'w') as f:
            json.dump(self.training_history, f, indent=2)
    
    def collect_data(self, data_source: str, data_format: str) -> pd.DataFrame:
        """
        Collect data for model training.
        
        Args:
            data_source: Path or URL to the data source
            data_format: Format of the data (csv, json, etc.)
            
        Returns:
            DataFrame containing the collected data
        """
        logger.info(f"Collecting data from {data_source} in {data_format} format")
        
        if data_format == "csv":
            data = pd.read_csv(data_source)
        elif data_format == "json":
            data = pd.read_json(data_source)
        elif data_format == "excel":
            data = pd.read_excel(data_source)
        else:
            raise ValueError(f"Unsupported data format: {data_format}")
        
        logger.info(f"Collected {len(data)} records")
        return data
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Preprocess data for model training.
        
        Args:
            data: DataFrame containing the raw data
            
        Returns:
            Tuple of preprocessed data and preprocessing metadata
        """
        logger.info("Preprocessing data")
        
        # Basic preprocessing steps
        # 1. Remove duplicates
        data = data.drop_duplicates()
        
        # 2. Handle missing values
        data = data.dropna()
        
        # 3. Record preprocessing metadata
        metadata = {
            "original_shape": data.shape,
            "preprocessed_shape": data.shape,
            "preprocessing_steps": ["remove_duplicates", "drop_na"],
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        logger.info(f"Preprocessing complete. Shape: {data.shape}")
        return data, metadata
    
    def train_model(self, 
                   data: pd.DataFrame, 
                   model_params: Dict[str, Any],
                   experiment_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Train a model with the given data and parameters.
        
        Args:
            data: DataFrame containing the preprocessed data
            model_params: Dictionary of model parameters
            experiment_name: Name of the MLflow experiment
            
        Returns:
            Dictionary with training results and metrics
        """
        # Set experiment name
        if experiment_name is None:
            experiment_name = f"{self.model_type}_{self.model_name}"
        
        mlflow.set_experiment(experiment_name)
        
        # Start MLflow run
        with mlflow.start_run() as run:
            run_id = run.info.run_id
            logger.info(f"Started training run {run_id}")
            
            # Log parameters
            for key, value in model_params.items():
                mlflow.log_param(key, value)
            
            # Simulate model training (replace with actual training code)
            logger.info("Training model...")
            
            # Example metrics (replace with actual metrics)
            metrics = {
                "accuracy": 0.85,
                "loss": 0.15,
                "f1_score": 0.84
            }
            
            # Log metrics
            for key, value in metrics.items():
                mlflow.log_metric(key, value)
            
            # Create model version
            version = len(self.training_history["versions"]) + 1
            version_info = {
                "version": version,
                "run_id": run_id,
                "timestamp": datetime.datetime.now().isoformat(),
                "parameters": model_params,
                "metrics": metrics,
                "status": "active"
            }
            
            # Update training history
            self.training_history["versions"].append(version_info)
            self.training_history["current_version"] = version
            self.training_history["last_training"] = version_info["timestamp"]
            self.training_history["total_training_runs"] += 1
            self._save_training_history()
            
            # Log model
            # mlflow.pytorch.log_model(model, "model")
            
            logger.info(f"Training complete. Model version: {version}")
            
            return {
                "run_id": run_id,
                "version": version,
                "metrics": metrics
            }
    
    def evaluate_model(self, 
                      version: Optional[int] = None, 
                      test_data: Optional[pd.DataFrame] = None) -> Dict[str, float]:
        """
        Evaluate a model version on test data.
        
        Args:
            version: Model version to evaluate (default: current version)
            test_data: Test data for evaluation
            
        Returns:
            Dictionary of evaluation metrics
        """
        if version is None:
            version = self.training_history["current_version"]
        
        # Find version info
        version_info = next(
            (v for v in self.training_history["versions"] if v["version"] == version),
            None
        )
        
        if version_info is None:
            raise ValueError(f"Model version {version} not found")
        
        run_id = version_info["run_id"]
        
        logger.info(f"Evaluating model version {version} (run_id: {run_id})")
        
        # Load model from MLflow
        # model = mlflow.pytorch.load_model(f"runs:/{run_id}/model")
        
        # Simulate evaluation (replace with actual evaluation code)
        metrics = {
            "accuracy": 0.83,
            "loss": 0.17,
            "f1_score": 0.82
        }
        
        logger.info(f"Evaluation complete. Metrics: {metrics}")
        
        return metrics
    
    def get_model_versions(self) -> List[Dict[str, Any]]:
        """
        Get all model versions.
        
        Returns:
            List of model version information
        """
        return self.training_history["versions"]
    
    def get_current_version(self) -> Dict[str, Any]:
        """
        Get current model version information.
        
        Returns:
            Dictionary with current version information
        """
        current_version = self.training_history["current_version"]
        
        if current_version is None:
            return None
        
        version_info = next(
            (v for v in self.training_history["versions"] if v["version"] == current_version),
            None
        )
        
        return version_info
    
    def set_current_version(self, version: int) -> Dict[str, Any]:
        """
        Set the current model version.
        
        Args:
            version: Version number to set as current
            
        Returns:
            Dictionary with updated version information
        """
        # Find version info
        version_info = next(
            (v for v in self.training_history["versions"] if v["version"] == version),
            None
        )
        
        if version_info is None:
            raise ValueError(f"Model version {version} not found")
        
        # Update current version
        self.training_history["current_version"] = version
        self._save_training_history()
        
        logger.info(f"Set current version to {version}")
        
        return version_info


@celery_app.task
def schedule_training_job(model_type: str, 
                         model_name: str, 
                         data_source: str,
                         data_format: str,
                         model_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Schedule a model training job as a background task.
    
    Args:
        model_type: Type of model (language, speech, vision)
        model_name: Name of the model
        data_source: Path or URL to the data source
        data_format: Format of the data (csv, json, etc.)
        model_params: Dictionary of model parameters
        
    Returns:
        Dictionary with job information
    """
    logger.info(f"Scheduling training job for {model_type}/{model_name}")
    
    # Create pipeline
    pipeline = ModelTrainingPipeline(model_type, model_name)
    
    # Collect and preprocess data
    data = pipeline.collect_data(data_source, data_format)
    preprocessed_data, _ = pipeline.preprocess_data(data)
    
    # Train model
    result = pipeline.train_model(preprocessed_data, model_params)
    
    # Evaluate model
    metrics = pipeline.evaluate_model(result["version"])
    
    return {
        "job_id": result["run_id"],
        "model_type": model_type,
        "model_name": model_name,
        "version": result["version"],
        "metrics": metrics,
        "status": "completed",
        "timestamp": datetime.datetime.now().isoformat()
    }
