"""
Model Versioning for JharkhandAI

This module provides functionality for managing different versions of AI models.
It includes a model registry, versioning system, and rollback capabilities.
"""

import os
import json
import logging
import datetime
import shutil
from typing import Dict, List, Any, Optional, Union, Tuple
import mlflow
import mlflow.pytorch

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelRegistry:
    """
    Registry for managing different versions of AI models.
    """
    
    def __init__(self, 
                 registry_dir: str = "./model_registry",
                 tracking_uri: str = "sqlite:///mlflow.db"):
        """
        Initialize the model registry.
        
        Args:
            registry_dir: Directory to store model registry data
            tracking_uri: URI for MLflow tracking server
        """
        self.registry_dir = registry_dir
        self.tracking_uri = tracking_uri
        
        # Create registry directory if it doesn't exist
        os.makedirs(registry_dir, exist_ok=True)
        
        # Set up MLflow
        mlflow.set_tracking_uri(tracking_uri)
        
        # Initialize registry metadata
        self.registry_metadata_path = os.path.join(registry_dir, "registry_metadata.json")
        self._initialize_registry_metadata()
    
    def _initialize_registry_metadata(self):
        """Initialize or load registry metadata."""
        if os.path.exists(self.registry_metadata_path):
            with open(self.registry_metadata_path, 'r') as f:
                self.registry_metadata = json.load(f)
        else:
            self.registry_metadata = {
                "models": {},
                "last_updated": datetime.datetime.now().isoformat()
            }
            self._save_registry_metadata()
    
    def _save_registry_metadata(self):
        """Save registry metadata to disk."""
        with open(self.registry_metadata_path, 'w') as f:
            json.dump(self.registry_metadata, f, indent=2)
            
        # Update last updated timestamp
        self.registry_metadata["last_updated"] = datetime.datetime.now().isoformat()
    
    def register_model(self, 
                      model_type: str,
                      model_name: str,
                      version: int,
                      run_id: str,
                      metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Register a model version in the registry.
        
        Args:
            model_type: Type of model (language, speech, vision)
            model_name: Name of the model
            version: Model version
            run_id: MLflow run ID
            metadata: Additional metadata for the model
            
        Returns:
            Dictionary with registration information
        """
        logger.info(f"Registering model {model_type}/{model_name} version {version}")
        
        # Create model key
        model_key = f"{model_type}_{model_name}"
        
        # Initialize model entry if it doesn't exist
        if model_key not in self.registry_metadata["models"]:
            self.registry_metadata["models"][model_key] = {
                "model_type": model_type,
                "model_name": model_name,
                "versions": {},
                "latest_version": None,
                "production_version": None,
                "staging_version": None,
                "created_at": datetime.datetime.now().isoformat(),
                "updated_at": datetime.datetime.now().isoformat()
            }
        
        # Add version information
        version_key = str(version)
        self.registry_metadata["models"][model_key]["versions"][version_key] = {
            "version": version,
            "run_id": run_id,
            "status": "registered",
            "created_at": datetime.datetime.now().isoformat(),
            "metadata": metadata
        }
        
        # Update latest version
        current_latest = self.registry_metadata["models"][model_key]["latest_version"]
        if current_latest is None or version > current_latest:
            self.registry_metadata["models"][model_key]["latest_version"] = version
        
        # Update model updated timestamp
        self.registry_metadata["models"][model_key]["updated_at"] = datetime.datetime.now().isoformat()
        
        # Save registry metadata
        self._save_registry_metadata()
        
        logger.info(f"Model {model_key} version {version} registered successfully")
        
        return {
            "model_key": model_key,
            "version": version,
            "status": "registered",
            "timestamp": datetime.datetime.now().isoformat()
        }
    
    def transition_model_version(self,
                               model_type: str,
                               model_name: str,
                               version: int,
                               stage: str) -> Dict[str, Any]:
        """
        Transition a model version to a different stage.
        
        Args:
            model_type: Type of model (language, speech, vision)
            model_name: Name of the model
            version: Model version
            stage: Target stage (production, staging, archived)
            
        Returns:
            Dictionary with transition information
        """
        logger.info(f"Transitioning model {model_type}/{model_name} version {version} to {stage}")
        
        # Create model key
        model_key = f"{model_type}_{model_name}"
        version_key = str(version)
        
        # Check if model exists
        if model_key not in self.registry_metadata["models"]:
            raise ValueError(f"Model {model_key} not found in registry")
        
        # Check if version exists
        if version_key not in self.registry_metadata["models"][model_key]["versions"]:
            raise ValueError(f"Version {version} of model {model_key} not found in registry")
        
        # Update version status
        self.registry_metadata["models"][model_key]["versions"][version_key]["status"] = stage
        
        # Update stage version
        if stage == "production":
            self.registry_metadata["models"][model_key]["production_version"] = version
        elif stage == "staging":
            self.registry_metadata["models"][model_key]["staging_version"] = version
        elif stage == "archived":
            # If this version was in production or staging, clear that reference
            if self.registry_metadata["models"][model_key]["production_version"] == version:
                self.registry_metadata["models"][model_key]["production_version"] = None
            if self.registry_metadata["models"][model_key]["staging_version"] == version:
                self.registry_metadata["models"][model_key]["staging_version"] = None
        
        # Update model updated timestamp
        self.registry_metadata["models"][model_key]["updated_at"] = datetime.datetime.now().isoformat()
        
        # Save registry metadata
        self._save_registry_metadata()
        
        logger.info(f"Model {model_key} version {version} transitioned to {stage}")
        
        return {
            "model_key": model_key,
            "version": version,
            "stage": stage,
            "timestamp": datetime.datetime.now().isoformat()
        }
    
    def get_model_version(self,
                        model_type: str,
                        model_name: str,
                        version: Optional[int] = None,
                        stage: Optional[str] = None) -> Dict[str, Any]:
        """
        Get information about a model version.
        
        Args:
            model_type: Type of model (language, speech, vision)
            model_name: Name of the model
            version: Model version (default: latest version)
            stage: Model stage (production, staging)
            
        Returns:
            Dictionary with model version information
        """
        # Create model key
        model_key = f"{model_type}_{model_name}"
        
        # Check if model exists
        if model_key not in self.registry_metadata["models"]:
            raise ValueError(f"Model {model_key} not found in registry")
        
        model_info = self.registry_metadata["models"][model_key]
        
        # Determine which version to return
        if stage is not None:
            if stage == "production":
                version = model_info["production_version"]
            elif stage == "staging":
                version = model_info["staging_version"]
            else:
                raise ValueError(f"Invalid stage: {stage}")
            
            if version is None:
                raise ValueError(f"No {stage} version found for model {model_key}")
        elif version is None:
            version = model_info["latest_version"]
            
            if version is None:
                raise ValueError(f"No versions found for model {model_key}")
        
        version_key = str(version)
        
        # Check if version exists
        if version_key not in model_info["versions"]:
            raise ValueError(f"Version {version} of model {model_key} not found in registry")
        
        # Return version information
        version_info = model_info["versions"][version_key]
        
        return {
            "model_key": model_key,
            "model_type": model_info["model_type"],
            "model_name": model_info["model_name"],
            "version": version,
            "run_id": version_info["run_id"],
            "status": version_info["status"],
            "created_at": version_info["created_at"],
            "metadata": version_info["metadata"]
        }
    
    def list_model_versions(self,
                          model_type: str,
                          model_name: str) -> List[Dict[str, Any]]:
        """
        List all versions of a model.
        
        Args:
            model_type: Type of model (language, speech, vision)
            model_name: Name of the model
            
        Returns:
            List of dictionaries with model version information
        """
        # Create model key
        model_key = f"{model_type}_{model_name}"
        
        # Check if model exists
        if model_key not in self.registry_metadata["models"]:
            raise ValueError(f"Model {model_key} not found in registry")
        
        model_info = self.registry_metadata["models"][model_key]
        
        # Create list of versions
        versions = []
        for version_key, version_info in model_info["versions"].items():
            versions.append({
                "model_key": model_key,
                "model_type": model_info["model_type"],
                "model_name": model_info["model_name"],
                "version": version_info["version"],
                "run_id": version_info["run_id"],
                "status": version_info["status"],
                "created_at": version_info["created_at"],
                "metadata": version_info["metadata"]
            })
        
        # Sort by version
        versions.sort(key=lambda x: x["version"], reverse=True)
        
        return versions
    
    def list_models(self) -> List[Dict[str, Any]]:
        """
        List all models in the registry.
        
        Returns:
            List of dictionaries with model information
        """
        models = []
        for model_key, model_info in self.registry_metadata["models"].items():
            models.append({
                "model_key": model_key,
                "model_type": model_info["model_type"],
                "model_name": model_info["model_name"],
                "latest_version": model_info["latest_version"],
                "production_version": model_info["production_version"],
                "staging_version": model_info["staging_version"],
                "version_count": len(model_info["versions"]),
                "created_at": model_info["created_at"],
                "updated_at": model_info["updated_at"]
            })
        
        # Sort by updated_at
        models.sort(key=lambda x: x["updated_at"], reverse=True)
        
        return models
    
    def delete_model_version(self,
                           model_type: str,
                           model_name: str,
                           version: int) -> Dict[str, Any]:
        """
        Delete a model version from the registry.
        
        Args:
            model_type: Type of model (language, speech, vision)
            model_name: Name of the model
            version: Model version
            
        Returns:
            Dictionary with deletion information
        """
        logger.info(f"Deleting model {model_type}/{model_name} version {version}")
        
        # Create model key
        model_key = f"{model_type}_{model_name}"
        version_key = str(version)
        
        # Check if model exists
        if model_key not in self.registry_metadata["models"]:
            raise ValueError(f"Model {model_key} not found in registry")
        
        # Check if version exists
        if version_key not in self.registry_metadata["models"][model_key]["versions"]:
            raise ValueError(f"Version {version} of model {model_key} not found in registry")
        
        # Get version info before deletion
        version_info = self.registry_metadata["models"][model_key]["versions"][version_key]
        
        # Delete version
        del self.registry_metadata["models"][model_key]["versions"][version_key]
        
        # Update model metadata
        model_info = self.registry_metadata["models"][model_key]
        
        # If this was the production or staging version, clear that reference
        if model_info["production_version"] == version:
            model_info["production_version"] = None
        if model_info["staging_version"] == version:
            model_info["staging_version"] = None
        
        # If this was the latest version, update latest version
        if model_info["latest_version"] == version:
            if model_info["versions"]:
                # Find new latest version
                new_latest = max(int(v) for v in model_info["versions"].keys())
                model_info["latest_version"] = new_latest
            else:
                model_info["latest_version"] = None
        
        # Update model updated timestamp
        model_info["updated_at"] = datetime.datetime.now().isoformat()
        
        # If no versions left, delete model
        if not model_info["versions"]:
            del self.registry_metadata["models"][model_key]
            logger.info(f"Model {model_key} deleted from registry (no versions left)")
        else:
            logger.info(f"Model {model_key} version {version} deleted from registry")
        
        # Save registry metadata
        self._save_registry_metadata()
        
        return {
            "model_key": model_key,
            "version": version,
            "status": "deleted",
            "timestamp": datetime.datetime.now().isoformat()
        }
    
    def load_model(self,
                 model_type: str,
                 model_name: str,
                 version: Optional[int] = None,
                 stage: Optional[str] = None) -> Any:
        """
        Load a model from the registry.
        
        Args:
            model_type: Type of model (language, speech, vision)
            model_name: Name of the model
            version: Model version (default: latest version)
            stage: Model stage (production, staging)
            
        Returns:
            Loaded model
        """
        # Get model version information
        version_info = self.get_model_version(model_type, model_name, version, stage)
        
        # Load model from MLflow
        run_id = version_info["run_id"]
        
        logger.info(f"Loading model {model_type}/{model_name} version {version_info['version']} (run_id: {run_id})")
        
        # Load model
        model = mlflow.pytorch.load_model(f"runs:/{run_id}/model")
        
        return model
