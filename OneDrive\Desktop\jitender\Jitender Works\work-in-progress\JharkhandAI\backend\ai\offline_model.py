"""
Offline Model Support for JharkhandAI

This module provides functionality for offline model usage in low-connectivity areas.
It includes model compression, caching, and synchronization mechanisms.
"""

import os
import json
import logging
import datetime
import hashlib
import shutil
from typing import Dict, List, Any, Optional, Union, Tuple
import torch
import onnx
import onnxruntime as ort
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OfflineModelManager:
    """
    Manager for offline model usage in low-connectivity areas.
    """
    
    def __init__(self, 
                 cache_dir: str = "./offline_models",
                 max_cache_size_gb: float = 2.0):
        """
        Initialize the offline model manager.
        
        Args:
            cache_dir: Directory to cache models for offline use
            max_cache_size_gb: Maximum cache size in gigabytes
        """
        self.cache_dir = cache_dir
        self.max_cache_size_bytes = max_cache_size_gb * 1024 * 1024 * 1024
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
        
        # Initialize cache metadata
        self.cache_metadata_path = os.path.join(cache_dir, "cache_metadata.json")
        self._initialize_cache_metadata()
    
    def _initialize_cache_metadata(self):
        """Initialize or load cache metadata."""
        if os.path.exists(self.cache_metadata_path):
            with open(self.cache_metadata_path, 'r') as f:
                self.cache_metadata = json.load(f)
        else:
            self.cache_metadata = {
                "models": {},
                "total_size_bytes": 0,
                "last_sync": None
            }
            self._save_cache_metadata()
    
    def _save_cache_metadata(self):
        """Save cache metadata to disk."""
        with open(self.cache_metadata_path, 'w') as f:
            json.dump(self.cache_metadata, f, indent=2)
    
    def _calculate_model_hash(self, model_path: str) -> str:
        """
        Calculate a hash for the model file.
        
        Args:
            model_path: Path to the model file
            
        Returns:
            Hash string for the model
        """
        hasher = hashlib.md5()
        with open(model_path, 'rb') as f:
            buf = f.read()
            hasher.update(buf)
        return hasher.hexdigest()
    
    def compress_model(self, 
                      model: torch.nn.Module, 
                      model_type: str,
                      model_name: str,
                      version: int,
                      input_shape: List[int],
                      quantize: bool = True) -> str:
        """
        Compress a PyTorch model to ONNX format for offline use.
        
        Args:
            model: PyTorch model to compress
            model_type: Type of model (language, speech, vision)
            model_name: Name of the model
            version: Model version
            input_shape: Shape of the input tensor
            quantize: Whether to quantize the model
            
        Returns:
            Path to the compressed model
        """
        logger.info(f"Compressing model {model_type}/{model_name} (version {version})")
        
        # Create model directory
        model_dir = os.path.join(self.cache_dir, f"{model_type}_{model_name}_v{version}")
        os.makedirs(model_dir, exist_ok=True)
        
        # Create dummy input
        dummy_input = torch.randn(*input_shape)
        
        # Export to ONNX
        onnx_path = os.path.join(model_dir, "model.onnx")
        torch.onnx.export(
            model,
            dummy_input,
            onnx_path,
            export_params=True,
            opset_version=12,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={'input': {0: 'batch_size'},
                          'output': {0: 'batch_size'}}
        )
        
        # Quantize model if requested
        if quantize:
            from onnxruntime.quantization import quantize_dynamic, QuantType
            quantized_path = os.path.join(model_dir, "model_quantized.onnx")
            quantize_dynamic(onnx_path, quantized_path, weight_type=QuantType.QUInt8)
            model_path = quantized_path
        else:
            model_path = onnx_path
        
        # Calculate model size and hash
        model_size = os.path.getsize(model_path)
        model_hash = self._calculate_model_hash(model_path)
        
        # Update cache metadata
        model_key = f"{model_type}_{model_name}_v{version}"
        self.cache_metadata["models"][model_key] = {
            "model_type": model_type,
            "model_name": model_name,
            "version": version,
            "path": model_path,
            "size_bytes": model_size,
            "hash": model_hash,
            "quantized": quantize,
            "cached_at": datetime.datetime.now().isoformat(),
            "last_used": datetime.datetime.now().isoformat()
        }
        
        # Update total cache size
        self.cache_metadata["total_size_bytes"] += model_size
        
        # Check if we need to clean up cache
        if self.cache_metadata["total_size_bytes"] > self.max_cache_size_bytes:
            self._clean_cache()
        
        self._save_cache_metadata()
        
        logger.info(f"Model compressed and saved to {model_path}")
        
        return model_path
    
    def _clean_cache(self):
        """Clean up cache by removing least recently used models."""
        logger.info("Cleaning up model cache")
        
        # Sort models by last used timestamp
        models = list(self.cache_metadata["models"].items())
        models.sort(key=lambda x: x[1]["last_used"])
        
        # Remove models until we're under the size limit
        for model_key, model_info in models:
            if self.cache_metadata["total_size_bytes"] <= self.max_cache_size_bytes:
                break
            
            logger.info(f"Removing model {model_key} from cache")
            
            # Remove model files
            model_dir = os.path.dirname(model_info["path"])
            shutil.rmtree(model_dir)
            
            # Update cache metadata
            self.cache_metadata["total_size_bytes"] -= model_info["size_bytes"]
            del self.cache_metadata["models"][model_key]
        
        self._save_cache_metadata()
    
    def load_offline_model(self, 
                         model_type: str,
                         model_name: str,
                         version: Optional[int] = None) -> Tuple[ort.InferenceSession, Dict[str, Any]]:
        """
        Load a model for offline use.
        
        Args:
            model_type: Type of model (language, speech, vision)
            model_name: Name of the model
            version: Model version (default: latest cached version)
            
        Returns:
            Tuple of ONNX Runtime session and model metadata
        """
        # Find the model in cache
        if version is None:
            # Find latest version
            matching_models = [
                (k, v) for k, v in self.cache_metadata["models"].items()
                if v["model_type"] == model_type and v["model_name"] == model_name
            ]
            
            if not matching_models:
                raise ValueError(f"No cached models found for {model_type}/{model_name}")
            
            # Sort by version
            matching_models.sort(key=lambda x: x[1]["version"], reverse=True)
            model_key, model_info = matching_models[0]
        else:
            model_key = f"{model_type}_{model_name}_v{version}"
            if model_key not in self.cache_metadata["models"]:
                raise ValueError(f"Model {model_key} not found in cache")
            
            model_info = self.cache_metadata["models"][model_key]
        
        logger.info(f"Loading offline model {model_key}")
        
        # Check if model file exists
        if not os.path.exists(model_info["path"]):
            raise FileNotFoundError(f"Model file {model_info['path']} not found")
        
        # Load model with ONNX Runtime
        session = ort.InferenceSession(model_info["path"])
        
        # Update last used timestamp
        model_info["last_used"] = datetime.datetime.now().isoformat()
        self._save_cache_metadata()
        
        return session, model_info
    
    def run_offline_inference(self, 
                            session: ort.InferenceSession, 
                            input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run inference with an offline model.
        
        Args:
            session: ONNX Runtime session
            input_data: Dictionary of input data
            
        Returns:
            Dictionary of output data
        """
        # Get input name
        input_name = session.get_inputs()[0].name
        
        # Run inference
        outputs = session.run(None, {input_name: input_data})
        
        # Get output name
        output_names = [output.name for output in session.get_outputs()]
        
        # Create output dictionary
        output_dict = {name: outputs[i] for i, name in enumerate(output_names)}
        
        return output_dict
    
    def sync_models(self, 
                   model_registry: Dict[str, Any],
                   force_sync: bool = False) -> Dict[str, Any]:
        """
        Synchronize offline models with the model registry.
        
        Args:
            model_registry: Dictionary of models in the registry
            force_sync: Whether to force synchronization
            
        Returns:
            Dictionary with synchronization results
        """
        logger.info("Synchronizing offline models with registry")
        
        # Check if we need to sync
        last_sync = self.cache_metadata.get("last_sync")
        if last_sync and not force_sync:
            last_sync_time = datetime.datetime.fromisoformat(last_sync)
            time_since_sync = datetime.datetime.now() - last_sync_time
            
            # Skip sync if less than 24 hours since last sync
            if time_since_sync.total_seconds() < 24 * 60 * 60:
                logger.info(f"Skipping sync, last sync was {time_since_sync.total_seconds() / 3600:.1f} hours ago")
                return {
                    "status": "skipped",
                    "last_sync": last_sync,
                    "models_added": 0,
                    "models_removed": 0
                }
        
        # Track changes
        models_added = 0
        models_removed = 0
        
        # Add new models from registry
        for model_key, model_info in model_registry.items():
            if model_key not in self.cache_metadata["models"]:
                # TODO: Implement actual model download and compression
                logger.info(f"Would download and compress model {model_key}")
                models_added += 1
        
        # Remove models that are no longer in registry
        for model_key in list(self.cache_metadata["models"].keys()):
            if model_key not in model_registry:
                model_info = self.cache_metadata["models"][model_key]
                
                # Remove model files
                model_dir = os.path.dirname(model_info["path"])
                if os.path.exists(model_dir):
                    shutil.rmtree(model_dir)
                
                # Update cache metadata
                self.cache_metadata["total_size_bytes"] -= model_info["size_bytes"]
                del self.cache_metadata["models"][model_key]
                
                models_removed += 1
        
        # Update last sync timestamp
        self.cache_metadata["last_sync"] = datetime.datetime.now().isoformat()
        self._save_cache_metadata()
        
        logger.info(f"Sync complete. Added: {models_added}, Removed: {models_removed}")
        
        return {
            "status": "completed",
            "last_sync": self.cache_metadata["last_sync"],
            "models_added": models_added,
            "models_removed": models_removed
        }
    
    def get_cached_models(self) -> Dict[str, Any]:
        """
        Get information about cached models.
        
        Returns:
            Dictionary with cached model information
        """
        return {
            "models": self.cache_metadata["models"],
            "total_size_bytes": self.cache_metadata["total_size_bytes"],
            "total_size_mb": self.cache_metadata["total_size_bytes"] / (1024 * 1024),
            "max_size_bytes": self.max_cache_size_bytes,
            "max_size_gb": self.max_cache_size_bytes / (1024 * 1024 * 1024),
            "last_sync": self.cache_metadata["last_sync"],
            "model_count": len(self.cache_metadata["models"])
        }
