"""Cloud configuration for Jharkhand AI platform.
Provides settings for secure, scalable cloud deployment of AI models."""

from typing import Dict, List, Optional
import os
from pathlib import Path

class CloudConfig:
    """Configuration for cloud-based deployment and scaling."""
    
    def __init__(
        self,
        cloud_provider: str = "aws",
        region: str = "ap-south-1",
        env: str = "production"
    ):
        self.cloud_provider = cloud_provider
        self.region = region
        self.env = env
        
        # Load environment variables
        self.api_key = os.getenv("CLOUD_API_KEY")
        self.secret_key = os.getenv("CLOUD_SECRET_KEY")
        
        # Configure default settings
        self.configure_defaults()
    
    def configure_defaults(self):
        """Set default configuration values."""
        self.config = {
            # Instance configurations
            "compute": {
                "instance_type": "ml.g4dn.xlarge",
                "min_instances": 1,
                "max_instances": 5,
                "scale_up_threshold": 70,  # CPU utilization %
                "scale_down_threshold": 30
            },
            
            # Storage configurations
            "storage": {
                "model_storage": {
                    "type": "s3",
                    "bucket": f"jharkhand-ai-models-{self.env}",
                    "encryption": "AES-256"
                },
                "data_storage": {
                    "type": "s3",
                    "bucket": f"jharkhand-ai-data-{self.env}",
                    "encryption": "AES-256"
                }
            },
            
            # Network configurations
            "network": {
                "vpc_enabled": True,
                "private_subnet": True,
                "ssl_enabled": True,
                "allowed_origins": ["*.jharkhand.gov.in"]
            },
            
            # Model serving configurations
            "model_serving": {
                "framework": "tensorflow-serving",
                "batch_size": 32,
                "max_batch_latency": 50,  # ms
                "gpu_memory_fraction": 0.8
            },
            
            # Monitoring configurations
            "monitoring": {
                "enable_metrics": True,
                "enable_logging": True,
                "log_retention_days": 30,
                "alert_endpoints": ["<EMAIL>"],
                "metrics": [
                    "model_latency",
                    "gpu_utilization",
                    "memory_usage",
                    "request_count",
                    "error_rate"
                ]
            },
            
            # Security configurations
            "security": {
                "enable_encryption": True,
                "enable_iam": True,
                "enable_audit_logs": True,
                "enable_ddos_protection": True,
                "allowed_ip_ranges": ["10.0.0.0/8"]
            },
            
            # Cache configurations
            "cache": {
                "enable_redis": True,
                "redis_instance_type": "cache.t3.medium",
                "max_memory_usage": 0.8
            },
            
            # Load balancer configurations
            "load_balancer": {
                "type": "application",
                "algorithm": "round_robin",
                "health_check_path": "/health",
                "ssl_cert_arn": "${SSL_CERT_ARN}"
            }
        }
    
    def update_config(self, section: str, key: str, value: any):
        """Update specific configuration value."""
        if section in self.config and key in self.config[section]:
            self.config[section][key] = value
        else:
            raise ValueError(f"Invalid section or key: {section}.{key}")
    
    def get_config(self, section: Optional[str] = None) -> Dict:
        """Get configuration values."""
        if section:
            return self.config.get(section, {})
        return self.config
    
    def validate_config(self) -> bool:
        """Validate the current configuration."""
        try:
            # Check required fields
            assert self.api_key, "Cloud API key not found"
            assert self.secret_key, "Cloud secret key not found"
            
            # Validate compute configuration
            compute = self.config["compute"]
            assert compute["min_instances"] <= compute["max_instances"]
            assert 0 <= compute["scale_up_threshold"] <= 100
            assert 0 <= compute["scale_down_threshold"] <= 100
            
            # Validate storage configuration
            for storage in self.config["storage"].values():
                assert storage["encryption"] in ["AES-256", "None"]
            
            # Validate monitoring configuration
            monitoring = self.config["monitoring"]
            assert monitoring["log_retention_days"] > 0
            assert len(monitoring["alert_endpoints"]) > 0
            
            return True
            
        except AssertionError as e:
            print(f"Configuration validation failed: {str(e)}")
            return False
        except Exception as e:
            print(f"Unexpected error in configuration validation: {str(e)}")
            return False