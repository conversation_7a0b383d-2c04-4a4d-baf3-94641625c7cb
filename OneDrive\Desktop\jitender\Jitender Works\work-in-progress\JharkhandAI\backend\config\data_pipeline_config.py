from typing import List, Dict, Optional
from pydantic import BaseModel

class DataSourceConfig(BaseModel):
    source_name: str
    language: str
    data_type: str  # text, audio, or image
    collection_method: str  # api, web_scraping, manual_upload
    storage_path: str
    preprocessing_steps: List[str]
    validation_rules: Dict[str, str]

class ModelConfig(BaseModel):
    model_name: str
    language: str
    task_type: str
    base_model: str
    fine_tuning_params: Dict[str, any]
    evaluation_metrics: List[str]
    device_optimization: Dict[str, any]

class DataPipelineConfig:
    def __init__(self):
        self.tribal_languages = [
            "Santhali",
            "Ho",
            "Mundari",
            "Kurukh",
            "Kharia"
        ]
        
        self.data_sources = [
            DataSourceConfig(
                source_name="tribal_text_corpus",
                language="all",
                data_type="text",
                collection_method="manual_upload",
                storage_path="/data/raw/text",
                preprocessing_steps=[
                    "text_normalization",
                    "language_detection",
                    "quality_check"
                ],
                validation_rules={
                    "min_length": "10",
                    "max_length": "1000",
                    "required_fields": "text,language,source"
                }
            ),
            DataSourceConfig(
                source_name="tribal_speech_corpus",
                language="all",
                data_type="audio",
                collection_method="manual_upload",
                storage_path="/data/raw/audio",
                preprocessing_steps=[
                    "audio_normalization",
                    "noise_reduction",
                    "speaker_diarization"
                ],
                validation_rules={
                    "min_duration": "1",
                    "max_duration": "300",
                    "required_fields": "audio,transcript,language,speaker_id"
                }
            )
        ]
        
        self.model_configs = [
            ModelConfig(
                model_name="tribal_language_model",
                language="all",
                task_type="translation",
                base_model="facebook/mbart-large-50",
                fine_tuning_params={
                    "learning_rate": 2e-5,
                    "batch_size": 16,
                    "epochs": 10,
                    "warmup_steps": 500
                },
                evaluation_metrics=[
                    "bleu",
                    "rouge",
                    "accuracy"
                ],
                device_optimization={
                    "quantization": "int8",
                    "pruning": True,
                    "knowledge_distillation": True
                }
            ),
            ModelConfig(
                model_name="tribal_speech_model",
                language="all",
                task_type="speech_recognition",
                base_model="facebook/wav2vec2-large-xlsr-53",
                fine_tuning_params={
                    "learning_rate": 1e-4,
                    "batch_size": 8,
                    "epochs": 20,
                    "gradient_accumulation_steps": 2
                },
                evaluation_metrics=[
                    "wer",
                    "cer"
                ],
                device_optimization={
                    "quantization": "int8",
                    "pruning": True,
                    "streaming_inference": True
                }
            )
        ]

    def get_language_config(self, language: str) -> Dict:
        """Get configuration for a specific tribal language"""
        return {
            "data_sources": [ds for ds in self.data_sources if ds.language in [language, "all"]],
            "models": [mc for mc in self.model_configs if mc.language in [language, "all"]]
        }

    def get_task_config(self, task_type: str) -> Dict:
        """Get configuration for a specific task type"""
        return {
            "models": [mc for mc in self.model_configs if mc.task_type == task_type]
        }