"""Configuration for fine-tuning language and speech models with Jharkhand-specific data."""

from typing import Dict, List

# Language model fine-tuning configuration
LANGUAGE_MODEL_CONFIG = {
    'base_model': 'google/mt5-small',
    'training_params': {
        'learning_rate': 2e-5,
        'batch_size': 8,
        'num_epochs': 3,
        'warmup_steps': 500,
        'weight_decay': 0.01,
        'gradient_accumulation_steps': 4
    },
    'languages': {
        'sa': {
            'data_path': 'data/santhali/',
            'min_samples': 1000,
            'validation_split': 0.1
        },
        'ho': {
            'data_path': 'data/ho/',
            'min_samples': 1000,
            'validation_split': 0.1
        },
        'mu': {
            'data_path': 'data/mundari/',
            'min_samples': 1000,
            'validation_split': 0.1
        },
        'ku': {
            'data_path': 'data/kurukh/',
            'min_samples': 1000,
            'validation_split': 0.1
        },
        'kh': {
            'data_path': 'data/kharia/',
            'min_samples': 1000,
            'validation_split': 0.1
        }
    }
}

# Speech model fine-tuning configuration
SPEECH_MODEL_CONFIG = {
    'base_model': 'facebook/wav2vec2-large-xlsr-53',
    'training_params': {
        'learning_rate': 1e-4,
        'batch_size': 4,
        'num_epochs': 5,
        'warmup_ratio': 0.1,
        'gradient_checkpointing': True,
        'fp16': True
    },
    'languages': {
        'sa': {
            'data_path': 'data/speech/santhali/',
            'min_audio_samples': 100,
            'validation_split': 0.1,
            'sampling_rate': 16000
        },
        'ho': {
            'data_path': 'data/speech/ho/',
            'min_audio_samples': 100,
            'validation_split': 0.1,
            'sampling_rate': 16000
        },
        'mu': {
            'data_path': 'data/speech/mundari/',
            'min_audio_samples': 100,
            'validation_split': 0.1,
            'sampling_rate': 16000
        },
        'ku': {
            'data_path': 'data/speech/kurukh/',
            'min_audio_samples': 100,
            'validation_split': 0.1,
            'sampling_rate': 16000
        },
        'kh': {
            'data_path': 'data/speech/kharia/',
            'min_audio_samples': 100,
            'validation_split': 0.1,
            'sampling_rate': 16000
        }
    }
}

# Model evaluation metrics
EVALUATION_METRICS = {
    'language': [
        'bleu',
        'rouge',
        'perplexity',
        'accuracy'
    ],
    'speech': [
        'word_error_rate',
        'character_error_rate',
        'phoneme_error_rate'
    ]
}

# Training pipeline settings
TRAINING_PIPELINE = {
    'max_gpu_memory': '12GB',
    'mixed_precision': 'fp16',
    'distributed_training': True,
    'checkpoint_frequency': 1000,
    'evaluation_frequency': 500,
    'early_stopping_patience': 3,
    'tensorboard_logging': True,
    'mlflow_tracking': True
}