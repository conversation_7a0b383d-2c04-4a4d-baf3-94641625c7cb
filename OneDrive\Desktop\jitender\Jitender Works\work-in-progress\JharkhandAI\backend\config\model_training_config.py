from typing import Dict, List, Optional
from pydantic import BaseModel

class TrainingConfig(BaseModel):
    model_name: str
    task_type: str
    base_model: str
    training_data_path: str
    validation_data_path: str
    output_dir: str
    device: str = "cuda"
    batch_size: int = 16
    learning_rate: float = 2e-5
    num_epochs: int = 10
    warmup_steps: int = 500
    evaluation_strategy: str = "steps"
    eval_steps: int = 500
    save_steps: int = 1000
    logging_steps: int = 100

class OptimizationConfig(BaseModel):
    enable_quantization: bool = True
    quantization_type: str = "int8"
    enable_pruning: bool = True
    pruning_target_sparsity: float = 0.5
    enable_knowledge_distillation: bool = True
    teacher_model_path: Optional[str] = None
    enable_model_compression: bool = True
    target_size_mb: Optional[int] = None

class DeploymentConfig(BaseModel):
    deployment_platform: str  # "cloud", "edge", or "mobile"
    serving_framework: str  # "tensorflow-serving", "torchserve", or "onnx-runtime"
    batch_inference: bool = True
    max_batch_size: int = 32
    timeout_ms: int = 5000
    enable_caching: bool = True
    cache_size_mb: int = 1024
    enable_monitoring: bool = True
    metrics_export_path: str = "/metrics"

class ModelTrainingPipeline:
    def __init__(self):
        self.training_configs = {
            "tribal_language_model": TrainingConfig(
                model_name="tribal_language_model",
                task_type="translation",
                base_model="facebook/mbart-large-50",
                training_data_path="/data/processed/text/train",
                validation_data_path="/data/processed/text/val",
                output_dir="/models/tribal_language_model"
            ),
            "tribal_speech_model": TrainingConfig(
                model_name="tribal_speech_model",
                task_type="speech_recognition",
                base_model="facebook/wav2vec2-large-xlsr-53",
                training_data_path="/data/processed/audio/train",
                validation_data_path="/data/processed/audio/val",
                output_dir="/models/tribal_speech_model",
                batch_size=8,
                learning_rate=1e-4,
                num_epochs=20
            )
        }
        
        self.optimization_configs = {
            "tribal_language_model": OptimizationConfig(
                enable_quantization=True,
                quantization_type="int8",
                enable_pruning=True,
                enable_knowledge_distillation=True,
                target_size_mb=500
            ),
            "tribal_speech_model": OptimizationConfig(
                enable_quantization=True,
                quantization_type="int8",
                enable_pruning=True,
                enable_model_compression=True,
                target_size_mb=1000
            )
        }
        
        self.deployment_configs = {
            "cloud": DeploymentConfig(
                deployment_platform="cloud",
                serving_framework="tensorflow-serving",
                batch_inference=True,
                max_batch_size=32,
                enable_caching=True,
                enable_monitoring=True
            ),
            "edge": DeploymentConfig(
                deployment_platform="edge",
                serving_framework="onnx-runtime",
                batch_inference=False,
                max_batch_size=1,
                timeout_ms=2000,
                enable_caching=True,
                cache_size_mb=512
            )
        }
    
    def get_training_config(self, model_name: str) -> TrainingConfig:
        return self.training_configs.get(model_name)
    
    def get_optimization_config(self, model_name: str) -> OptimizationConfig:
        return self.optimization_configs.get(model_name)
    
    def get_deployment_config(self, platform: str) -> DeploymentConfig:
        return self.deployment_configs.get(platform)