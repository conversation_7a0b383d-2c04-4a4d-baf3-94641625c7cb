"""Configuration for offline model management and compression.
This module provides settings for managing offline models, compression strategies,
and synchronization mechanisms for low-connectivity areas."""

from pathlib import Path
from typing import Dict, List, Optional

# Base paths for model storage
MODEL_BASE_DIR = Path("models")
LANGUAGE_MODEL_DIR = MODEL_BASE_DIR / "language"
SPEECH_MODEL_DIR = MODEL_BASE_DIR / "speech"
VISION_MODEL_DIR = MODEL_BASE_DIR / "vision"

# Model compression settings
MODEL_COMPRESSION = {
    "quantization": {
        "enabled": True,
        "dtype": "int8",
        "method": "dynamic",  # or 'static'
    },
    "pruning": {
        "enabled": True,
        "target_sparsity": 0.3,
        "pruning_schedule": "polynomial",
    },
    "knowledge_distillation": {
        "enabled": True,
        "temperature": 2.0,
    }
}

# Offline sync configuration
SYNC_CONFIG = {
    "auto_sync": {
        "enabled": True,
        "interval_hours": 24,
        "retry_attempts": 3,
        "retry_delay_minutes": 30,
    },
    "bandwidth_management": {
        "max_download_speed_mbps": 1.0,
        "max_upload_speed_mbps": 0.5,
        "chunk_size_mb": 10,
    },
    "storage_management": {
        "max_storage_gb": 10,
        "cleanup_threshold_gb": 8,
        "min_free_space_gb": 2,
    }
}

# Supported tribal languages
TRIBAL_LANGUAGES = {
    "sa": {
        "name": "Santhali",
        "code": "sat",
        "script": "Ol Chiki",
        "model_size_mb": 500,
    },
    "ho": {
        "name": "Ho",
        "code": "hoc",
        "script": "Warang Citi",
        "model_size_mb": 450,
    },
    "mu": {
        "name": "Mundari",
        "code": "unr",
        "script": "Devanagari",
        "model_size_mb": 450,
    },
    "ku": {
        "name": "Kurukh",
        "code": "kru",
        "script": "Devanagari",
        "model_size_mb": 400,
    },
    "kh": {
        "name": "Kharia",
        "code": "khr",
        "script": "Devanagari",
        "model_size_mb": 400,
    }
}

# Model versioning and updates
MODEL_VERSION_CONTROL = {
    "version_format": "v{major}.{minor}.{patch}",
    "auto_update": {
        "enabled": True,
        "check_interval_hours": 168,  # Weekly
        "update_on_startup": False,
    },
    "rollback": {
        "enabled": True,
        "max_versions_kept": 3,
    }
}

# Cache configuration
CACHE_CONFIG = {
    "enabled": True,
    "max_size_mb": 1000,
    "ttl_hours": 72,
    "cleanup_interval_hours": 24,
}

# Performance monitoring
PERFORMANCE_MONITORING = {
    "enabled": True,
    "metrics": [
        "inference_time",
        "memory_usage",
        "battery_impact",
        "accuracy",
    ],
    "log_interval_minutes": 60,
}