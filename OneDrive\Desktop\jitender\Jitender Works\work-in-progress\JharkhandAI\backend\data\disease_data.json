{"symptoms": ["fever", "cough", "sore_throat", "headache", "nausea", "fatigue", "body_ache", "shortness_of_breath", "loss_of_taste", "loss_of_smell", "diarrhea", "chest_pain", "dizziness", "rash", "joint_pain"], "diseases": ["common_cold", "influenza", "covid_19", "migraine", "gastroenteritis", "allergic_reaction", "bronchitis", "sinusitis", "food_poisoning", "dengue_fever"], "descriptions": {"common_cold": "A viral infection of the upper respiratory tract causing nasal congestion, sore throat, and cough.", "influenza": "A viral infection causing fever, body aches, fatigue, and respiratory symptoms.", "covid_19": "A viral respiratory illness caused by the SARS-CoV-2 virus with varying symptoms.", "migraine": "A severe headache often accompanied by nausea, sensitivity to light and sound.", "gastroenteritis": "Inflammation of the stomach and intestines causing diarrhea, vomiting, and abdominal pain.", "allergic_reaction": "An immune system response to allergens causing rash, itching, and other symptoms.", "bronchitis": "Inflammation of the bronchial tubes causing cough, mucus production, and chest discomfort.", "sinusitis": "Inflammation of the sinuses causing facial pain, nasal congestion, and headache.", "food_poisoning": "Illness caused by consuming contaminated food, leading to nausea, vomiting, and diarrhea.", "dengue_fever": "A mosquito-borne viral infection causing high fever, severe joint pain, and fatigue."}, "precautions": {"common_cold": ["Rest and stay hydrated", "Use over-the-counter cold medications", "Practice good hand hygiene", "Avoid close contact with others"], "influenza": ["Get plenty of rest", "Stay hydrated", "Take fever-reducing medication", "Consider antiviral medications if prescribed"], "covid_19": ["Isolate immediately", "Contact healthcare provider", "Monitor oxygen levels", "Follow local health guidelines"], "migraine": ["Rest in a quiet, dark room", "Apply cold or warm compress", "Take prescribed medications", "Identify and avoid triggers"], "gastroenteritis": ["Stay hydrated", "Follow BRAT diet", "Rest", "Avoid dairy and spicy foods"], "allergic_reaction": ["Identify and avoid allergens", "Take antihistamines if prescribed", "Seek emergency care if severe", "Keep emergency medications handy"], "bronchitis": ["Rest and stay hydrated", "Use humidifier", "Avoid smoking and secondhand smoke", "Take prescribed medications"], "sinusitis": ["Use nasal irrigation", "Apply warm compress", "Stay hydrated", "Use decongestants if recommended"], "food_poisoning": ["Stay hydrated", "Rest", "Avoid solid foods initially", "Gradually reintroduce bland foods"], "dengue_fever": ["Rest and stay hydrated", "Take fever-reducing medication", "Monitor for warning signs", "Follow up with healthcare provider"]}}