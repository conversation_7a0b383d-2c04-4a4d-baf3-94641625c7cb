"""
Dependency injection for FastAPI

This module provides dependency injection functions for FastAPI routes
"""

from fastapi import Depends
from typing import Optional

# Import models
from .models.tribal_nlu_model import TribalNLUModel

# Singleton instances
_tribal_nlu_model: Optional[TribalNLUModel] = None

def get_tribal_nlu_model() -> TribalNLUModel:
    """
    Get or create the Tribal NLU model instance
    
    Returns:
        TribalNLUModel: The tribal NLU model instance
    """
    global _tribal_nlu_model
    
    if _tribal_nlu_model is None:
        _tribal_nlu_model = TribalNLUModel()
        
    return _tribal_nlu_model
