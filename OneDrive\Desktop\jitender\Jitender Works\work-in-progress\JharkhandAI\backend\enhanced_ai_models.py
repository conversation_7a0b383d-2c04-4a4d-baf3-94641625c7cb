"""
Enhanced AI Models Integration for Jharkhand AI.
This module integrates all enhanced AI models and provides a unified interface
for the application to interact with them.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Union, Any
from pathlib import Path

# Import enhanced models
from models.enhanced_language_model import JharkhandLanguageModel
from models.enhanced_speech_model import JharkhandSpeechModel
from models.enhanced_vision_model import JharkhandVisionModel
from ai.enhanced_offline_model import OfflineModelManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class JharkhandAIModels:
    """
    Integrated AI models for Jharkhand AI.
    Provides a unified interface for all AI capabilities.
    """
    
    def __init__(
        self, 
        offline_mode: bool = False,
        model_dir: str = "models",
        device: Optional[str] = None,
        model_registry_url: Optional[str] = None
    ):
        """
        Initialize the Jharkhand AI Models.
        
        Args:
            offline_mode: Whether to use offline models only
            model_dir: Directory to store and load models
            device: Device to run the models on ('cpu', 'cuda', or None for auto-detection)
            model_registry_url: URL to the model registry for updates
        """
        self.offline_mode = offline_mode
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # Auto-detect device if not specified
        if device is None:
            import torch
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
            
        logger.info(f"Initializing JharkhandAIModels on {self.device}")
        logger.info(f"Offline mode: {offline_mode}")
        
        # Initialize offline model manager
        self.offline_manager = OfflineModelManager(
            base_dir=model_dir,
            model_registry_url=model_registry_url,
            check_updates_on_init=not offline_mode
        )
        
        # Initialize language model
        self.language_model = JharkhandLanguageModel(
            model_name="google/mt5-small",
            device=self.device,
            offline_mode=offline_mode,
            model_dir=str(self.model_dir / "language")
        )
        
        # Initialize speech model
        self.speech_model = JharkhandSpeechModel(
            stt_model_name="facebook/wav2vec2-large-xlsr-53",
            tts_model_name="microsoft/speecht5_tts",
            device=self.device,
            offline_mode=offline_mode,
            model_dir=str(self.model_dir / "speech")
        )
        
        # Initialize vision model
        self.vision_model = JharkhandVisionModel(
            classification_model_name="google/vit-base-patch16-224",
            detection_model_name="facebook/detr-resnet-50",
            segmentation_model_name="nvidia/segformer-b0-finetuned-ade-512-512",
            device=self.device,
            offline_mode=offline_mode,
            model_dir=str(self.model_dir / "vision")
        )
        
        logger.info("All models initialized successfully")
    
    def translate_text(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str,
        use_fine_tuned: bool = True
    ) -> str:
        """
        Translate text from source language to target language.
        
        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code
            use_fine_tuned: Whether to use fine-tuned models if available
            
        Returns:
            Translated text
        """
        return self.language_model.translate(
            text=text,
            source_lang=source_lang,
            target_lang=target_lang,
            use_fine_tuned=use_fine_tuned
        )
    
    def generate_text(
        self, 
        prompt: str, 
        language_code: str = 'en',
        max_length: int = 100,
        use_fine_tuned: bool = True
    ) -> str:
        """
        Generate text based on a prompt in the specified language.
        
        Args:
            prompt: Text prompt to generate from
            language_code: Language code to generate in
            max_length: Maximum length of generated text
            use_fine_tuned: Whether to use fine-tuned models if available
            
        Returns:
            Generated text
        """
        return self.language_model.generate_text(
            prompt=prompt,
            language_code=language_code,
            max_length=max_length,
            use_fine_tuned=use_fine_tuned
        )
    
    def speech_to_text(
        self, 
        audio_file: str, 
        language_code: str = 'en',
        use_fine_tuned: bool = True
    ) -> str:
        """
        Convert speech to text.
        
        Args:
            audio_file: Path to audio file
            language_code: Language code of the audio
            use_fine_tuned: Whether to use fine-tuned models if available
            
        Returns:
            Transcribed text
        """
        return self.speech_model.speech_to_text(
            audio_file=audio_file,
            language_code=language_code,
            use_fine_tuned=use_fine_tuned
        )
    
    def text_to_speech(
        self, 
        text: str, 
        language_code: str = 'en',
        output_file: Optional[str] = None,
        use_fine_tuned: bool = True
    ) -> Union[str, Any]:
        """
        Convert text to speech.
        
        Args:
            text: Text to convert to speech
            language_code: Language code of the text
            output_file: Path to save the audio file (optional)
            use_fine_tuned: Whether to use fine-tuned models if available
            
        Returns:
            Path to the output file if output_file is provided, otherwise the audio array
        """
        return self.speech_model.text_to_speech(
            text=text,
            language_code=language_code,
            output_file=output_file,
            use_fine_tuned=use_fine_tuned
        )
    
    def classify_image(
        self, 
        image_path: str, 
        task_name: str = "forest_monitoring",
        use_fine_tuned: bool = True,
        top_k: int = 3
    ) -> List[Dict[str, Union[str, float]]]:
        """
        Classify an image for a specific task.
        
        Args:
            image_path: Path to the image file
            task_name: Name of the task (e.g., 'forest_monitoring', 'tribal_art')
            use_fine_tuned: Whether to use fine-tuned models if available
            top_k: Number of top predictions to return
            
        Returns:
            List of dictionaries with class labels and confidence scores
        """
        return self.vision_model.classify_image(
            image_path=image_path,
            task_name=task_name,
            use_fine_tuned=use_fine_tuned,
            top_k=top_k
        )
    
    def detect_objects(
        self, 
        image_path: str, 
        task_name: str = "forest_monitoring",
        use_fine_tuned: bool = True,
        threshold: float = 0.5
    ) -> List[Dict[str, Union[str, float, List[float]]]]:
        """
        Detect objects in an image for a specific task.
        
        Args:
            image_path: Path to the image file
            task_name: Name of the task (e.g., 'forest_monitoring', 'wildlife')
            use_fine_tuned: Whether to use fine-tuned models if available
            threshold: Confidence threshold for detections
            
        Returns:
            List of dictionaries with class labels, confidence scores, and bounding boxes
        """
        return self.vision_model.detect_objects(
            image_path=image_path,
            task_name=task_name,
            use_fine_tuned=use_fine_tuned,
            threshold=threshold
        )
    
    def segment_image(
        self, 
        image_path: str, 
        task_name: str = "forest_monitoring",
        use_fine_tuned: bool = True,
        output_path: Optional[str] = None
    ) -> Union[str, Any]:
        """
        Perform semantic segmentation on an image for a specific task.
        
        Args:
            image_path: Path to the image file
            task_name: Name of the task (e.g., 'forest_monitoring', 'agriculture')
            use_fine_tuned: Whether to use fine-tuned models if available
            output_path: Path to save the segmentation mask (optional)
            
        Returns:
            Path to the output file if output_path is provided, otherwise the segmentation mask
        """
        return self.vision_model.segment_image(
            image_path=image_path,
            task_name=task_name,
            use_fine_tuned=use_fine_tuned,
            output_path=output_path
        )
    
    def monitor_forest_changes(
        self, 
        before_image_path: str,
        after_image_path: str,
        output_path: Optional[str] = None
    ) -> Union[str, Dict[str, Any]]:
        """
        Monitor forest changes between two images taken at different times.
        
        Args:
            before_image_path: Path to the 'before' image
            after_image_path: Path to the 'after' image
            output_path: Path to save the visualization (optional)
            
        Returns:
            Path to the output file if output_path is provided, otherwise a dictionary with change metrics
        """
        return self.vision_model.monitor_forest_changes(
            before_image_path=before_image_path,
            after_image_path=after_image_path,
            output_path=output_path
        )
    
    def recognize_tribal_art(
        self, 
        image_path: str,
        top_k: int = 3
    ) -> List[Dict[str, Union[str, float]]]:
        """
        Recognize tribal art style in an image.
        
        Args:
            image_path: Path to the image file
            top_k: Number of top predictions to return
            
        Returns:
            List of dictionaries with art style labels and confidence scores
        """
        return self.vision_model.recognize_tribal_art(
            image_path=image_path,
            top_k=top_k
        )
    
    def fine_tune_language_model(
        self, 
        dataset_path: str, 
        language_code: str,
        output_dir: Optional[str] = None,
        num_train_epochs: int = 3,
        batch_size: int = 8,
        learning_rate: float = 5e-5
    ) -> None:
        """
        Fine-tune the language model on Jharkhand-specific data for a particular language.
        
        Args:
            dataset_path: Path to the dataset (CSV or JSON)
            language_code: Language code to fine-tune for
            output_dir: Directory to save the fine-tuned model
            num_train_epochs: Number of training epochs
            batch_size: Training batch size
            learning_rate: Learning rate for training
        """
        self.language_model.fine_tune(
            dataset_path=dataset_path,
            language_code=language_code,
            output_dir=output_dir,
            num_train_epochs=num_train_epochs,
            batch_size=batch_size,
            learning_rate=learning_rate
        )
    
    def fine_tune_speech_model(
        self, 
        dataset_path: str, 
        language_code: str,
        model_type: str = "stt",  # "stt" or "tts"
        output_dir: Optional[str] = None,
        num_train_epochs: int = 3,
        batch_size: int = 8,
        learning_rate: float = 5e-5
    ) -> None:
        """
        Fine-tune the speech model on Jharkhand-specific data for a particular language.
        
        Args:
            dataset_path: Path to the dataset (CSV or JSON)
            language_code: Language code to fine-tune for
            model_type: Type of speech model to fine-tune ("stt" or "tts")
            output_dir: Directory to save the fine-tuned model
            num_train_epochs: Number of training epochs
            batch_size: Training batch size
            learning_rate: Learning rate for training
        """
        if model_type == "stt":
            self.speech_model.fine_tune_stt(
                dataset_path=dataset_path,
                language_code=language_code,
                output_dir=output_dir,
                num_train_epochs=num_train_epochs,
                batch_size=batch_size,
                learning_rate=learning_rate
            )
        elif model_type == "tts":
            self.speech_model.fine_tune_tts(
                dataset_path=dataset_path,
                language_code=language_code,
                output_dir=output_dir,
                num_train_epochs=num_train_epochs,
                batch_size=batch_size,
                learning_rate=learning_rate
            )
        else:
            raise ValueError(f"Invalid speech model type: {model_type}")
    
    def fine_tune_vision_model(
        self, 
        dataset_path: str, 
        task_name: str,
        task_type: str,
        output_dir: Optional[str] = None,
        num_train_epochs: int = 3,
        batch_size: int = 8,
        learning_rate: float = 5e-5
    ) -> None:
        """
        Fine-tune a vision model for a specific task and task type.
        
        Args:
            dataset_path: Path to the dataset (CSV or JSON)
            task_name: Name of the task (e.g., 'forest_monitoring', 'tribal_art')
            task_type: Type of task ('classification', 'object_detection', 'segmentation')
            output_dir: Directory to save the fine-tuned model
            num_train_epochs: Number of training epochs
            batch_size: Training batch size
            learning_rate: Learning rate for training
        """
        self.vision_model.fine_tune(
            dataset_path=dataset_path,
            task_name=task_name,
            task_type=task_type,
            output_dir=output_dir,
            num_train_epochs=num_train_epochs,
            batch_size=batch_size,
            learning_rate=learning_rate
        )
    
    def check_for_model_updates(self) -> bool:
        """
        Check for updates to the models.
        
        Returns:
            True if updates are available, False otherwise
        """
        return self.offline_manager.check_for_updates()
    
    def update_models(self, model_type: Optional[str] = None) -> bool:
        """
        Update models from the remote registry.
        
        Args:
            model_type: Type of models to update ('language', 'speech', 'vision', or None for all)
            
        Returns:
            True if update was successful, False otherwise
        """
        return self.offline_manager.update_models(model_type=model_type)
    
    def list_available_models(self, model_type: Optional[str] = None) -> Dict[str, List[str]]:
        """
        List available models.
        
        Args:
            model_type: Type of models to list ('language', 'speech', 'vision', or None for all)
            
        Returns:
            Dictionary mapping model types to lists of model IDs
        """
        return self.offline_manager.list_available_models(model_type=model_type)
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Return a dictionary of supported languages."""
        return self.language_model.get_supported_languages()
    
    def get_fine_tuned_languages(self) -> Dict[str, List[str]]:
        """Return a dictionary of languages with fine-tuned models."""
        return {
            "language": self.language_model.get_fine_tuned_languages(),
            "speech": self.speech_model.get_fine_tuned_languages()
        }
    
    def get_supported_vision_tasks(self) -> List[str]:
        """Return a list of supported vision tasks."""
        return self.vision_model.get_supported_tasks()
    
    def save_model_metadata(self):
        """Save metadata about all models."""
        self.language_model.save_model_metadata()
        self.speech_model.save_model_metadata()
        self.vision_model.save_model_metadata()
        
        # Save integrated metadata
        metadata = {
            "version": "1.0.0",
            "device": self.device,
            "offline_mode": self.offline_mode,
            "supported_languages": self.get_supported_languages(),
            "fine_tuned_languages": self.get_fine_tuned_languages(),
            "supported_vision_tasks": self.get_supported_vision_tasks(),
            "available_models": self.list_available_models()
        }
        
        with open(self.model_dir / "integrated_metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)
            
        logger.info(f"Saved integrated model metadata")
    
    def shutdown(self):
        """Clean up resources and save metadata before shutdown."""
        self.save_model_metadata()
        logger.info("JharkhandAIModels shutdown complete")
