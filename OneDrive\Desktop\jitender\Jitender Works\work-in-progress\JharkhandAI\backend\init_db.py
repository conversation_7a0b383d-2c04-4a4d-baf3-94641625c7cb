import logging
from database import create_tables, SessionLocal, User
from utils.auth import get_password_hash
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_db():
    """
    Initialize the database with tables and default data.
    """
    logger.info("Creating database tables...")
    create_tables()
    logger.info("Database tables created successfully.")
    
    # Create admin user if it doesn't exist
    db = SessionLocal()
    try:
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            logger.info("Creating admin user...")
            admin_password = os.getenv("ADMIN_PASSWORD", "admin123")  # Default password for development
            hashed_password = get_password_hash(admin_password)
            
            admin = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=hashed_password,
                full_name="Admin User",
                is_admin=True
            )
            
            db.add(admin)
            db.commit()
            logger.info("Admin user created successfully.")
        else:
            logger.info("Admin user already exists.")
    except Exception as e:
        logger.error(f"Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    logger.info("Initializing database...")
    init_db()
    logger.info("Database initialization completed.")
