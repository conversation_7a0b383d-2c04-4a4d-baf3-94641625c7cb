from database_enhanced import engine, SessionLocal
from models.database_models import Base, User, Job, Course, GovernmentScheme, TourismDestination
from utils.auth_enhanced import get_password_hash
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_db():
    # Create tables
    Base.metadata.create_all(bind=engine)
    logger.info("Created database tables")
    
    # Create a database session
    db = SessionLocal()
    
    try:
        # Check if we already have users
        user_count = db.query(User).count()
        if user_count == 0:
            logger.info("Creating sample users")
            
            # Create admin user
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                full_name="Admin User",
                hashed_password=get_password_hash("adminpassword"),
                is_active=True,
                is_admin=True
            )
            db.add(admin_user)
            
            # Create regular user
            regular_user = User(
                username="user",
                email="<EMAIL>",
                full_name="Regular User",
                district="Ranchi",
                hashed_password=get_password_hash("userpassword"),
                is_active=True,
                is_admin=False
            )
            db.add(regular_user)
            
            db.commit()
            logger.info("Sample users created")
        
        # Check if we already have jobs
        job_count = db.query(Job).count()
        if job_count == 0:
            logger.info("Creating sample jobs")
            
            jobs = [
                Job(
                    title="Software Developer",
                    company="Tech Solutions Ltd",
                    location="Ranchi",
                    description="Developing web applications using modern frameworks",
                    requirements="Python, JavaScript, React",
                    salary_range="₹5,00,000 - ₹8,00,000",
                    job_type="Full-time"
                ),
                Job(
                    title="Data Analyst",
                    company="Data Insights",
                    location="Jamshedpur",
                    description="Analyzing data and creating reports",
                    requirements="SQL, Python, Data Visualization",
                    salary_range="₹4,50,000 - ₹7,00,000",
                    job_type="Full-time"
                ),
                Job(
                    title="Agriculture Extension Officer",
                    company="Department of Agriculture",
                    location="Dhanbad",
                    description="Providing technical assistance to farmers",
                    requirements="B.Sc Agriculture, 2 years experience",
                    salary_range="₹3,50,000 - ₹5,00,000",
                    job_type="Government"
                )
            ]
            
            for job in jobs:
                db.add(job)
            
            db.commit()
            logger.info("Sample jobs created")
        
        # Check if we already have courses
        course_count = db.query(Course).count()
        if course_count == 0:
            logger.info("Creating sample courses")
            
            courses = [
                Course(
                    title="Introduction to Computer Science",
                    description="Learn the basics of computer science and programming",
                    instructor="Dr. Rajesh Kumar",
                    duration="8 weeks",
                    level="Beginner",
                    category="Computer Science"
                ),
                Course(
                    title="Sustainable Agriculture Practices",
                    description="Learn modern sustainable farming techniques",
                    instructor="Dr. Priya Singh",
                    duration="6 weeks",
                    level="Intermediate",
                    category="Agriculture"
                ),
                Course(
                    title="Healthcare Management",
                    description="Learn to manage healthcare facilities efficiently",
                    instructor="Dr. Amit Sharma",
                    duration="10 weeks",
                    level="Advanced",
                    category="Healthcare"
                )
            ]
            
            for course in courses:
                db.add(course)
            
            db.commit()
            logger.info("Sample courses created")
        
        # Check if we already have government schemes
        scheme_count = db.query(GovernmentScheme).count()
        if scheme_count == 0:
            logger.info("Creating sample government schemes")
            
            schemes = [
                GovernmentScheme(
                    title="Pradhan Mantri Kisan Samman Nidhi",
                    description="Income support scheme for farmers",
                    eligibility="All small and marginal farmers",
                    benefits="₹6,000 per year in three equal installments",
                    application_process="Apply online through the PM-KISAN portal",
                    department="Agriculture"
                ),
                GovernmentScheme(
                    title="Ayushman Bharat",
                    description="Health insurance scheme",
                    eligibility="Economically vulnerable families",
                    benefits="Health coverage up to ₹5 lakhs per family per year",
                    application_process="Apply through Common Service Centers",
                    department="Health"
                ),
                GovernmentScheme(
                    title="Skill India Mission",
                    description="Skill development program",
                    eligibility="All citizens",
                    benefits="Free skill training and certification",
                    application_process="Apply through nearest skill development center",
                    department="Education"
                )
            ]
            
            for scheme in schemes:
                db.add(scheme)
            
            db.commit()
            logger.info("Sample government schemes created")
        
        # Check if we already have tourism destinations
        destination_count = db.query(TourismDestination).count()
        if destination_count == 0:
            logger.info("Creating sample tourism destinations")
            
            destinations = [
                TourismDestination(
                    name="Hundru Falls",
                    description="One of the highest waterfalls in Jharkhand",
                    location="Ranchi",
                    image_url="https://example.com/hundru_falls.jpg",
                    category="Nature"
                ),
                TourismDestination(
                    name="Jagannath Temple",
                    description="Ancient temple dedicated to Lord Jagannath",
                    location="Ranchi",
                    image_url="https://example.com/jagannath_temple.jpg",
                    category="Heritage"
                ),
                TourismDestination(
                    name="Betla National Park",
                    description="Wildlife sanctuary with diverse flora and fauna",
                    location="Latehar",
                    image_url="https://example.com/betla_park.jpg",
                    category="Wildlife"
                )
            ]
            
            for destination in destinations:
                db.add(destination)
            
            db.commit()
            logger.info("Sample tourism destinations created")
        
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    logger.info("Initializing database")
    init_db()
    logger.info("Database initialization completed")
