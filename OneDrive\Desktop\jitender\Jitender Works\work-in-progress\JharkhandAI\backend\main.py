"""
Main FastAPI application for JharkhandAI

This module initializes the FastAPI application and includes all routes
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging

# Import routes
from routes import translation
from routes import language_model
from routes import tribal_nlu  # Import the new tribal NLU routes

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="JharkhandAI API",
    description="API for JharkhandAI - Advanced AI services for Jharkhand's tribal languages",
    version="1.0.0",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(translation.router)
app.include_router(language_model.router)
app.include_router(tribal_nlu.router)  # Include the new tribal NLU router

@app.get("/")
async def root():
    """Root endpoint that returns API information"""
    return {
        "message": "Welcome to JharkhandAI API",
        "version": "1.0.0",
        "docs_url": "/docs"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}