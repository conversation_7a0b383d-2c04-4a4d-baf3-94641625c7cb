from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
import os
import logging
from datetime import timedelta

# Import routes
from routes.language_model import router as language_router
from routes.speech import router as speech_router
from routes.vision import router as vision_router
from routes.agriculture import router as agriculture_router
from routes.healthcare import router as healthcare_router
from routes.jobs import router as jobs_router
from routes.chatbot import router as chatbot_router
from routes.education import router as education_router
from routes.government import router as government_router
from routes.tourism import router as tourism_router
from routes.integration import router as integration_router

# Import database and authentication
from database_enhanced import get_db, User
from utils.auth_enhanced import (
    authenticate_user,
    create_access_token,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    get_current_active_user,
    get_current_admin_user,
    get_password_hash
)
from schemas.user_schemas import Token, User<PERSON>reate, UserResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Jharkhand AI Platform",
    description="AI-powered platform for various services in Jharkhand",
    version="1.0.0"
)

# Configure CORS
origins = [
    "http://localhost",
    "http://localhost:3000",
    "http://localhost:8000",
    "https://jharkhandai.org",
    "https://www.jharkhandai.org",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Root endpoint
@app.get("/")
def read_root():
    return {"message": "Welcome to Jharkhand AI Platform API"}

# Authentication endpoints
@app.post("/api/auth/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.post("/api/auth/register", response_model=UserResponse)
async def register_user(user: UserCreate, db: Session = Depends(get_db)):
    # Check if username already exists
    db_user = db.query(User).filter(User.username == user.username).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    
    # Check if email already exists
    db_email = db.query(User).filter(User.email == user.email).first()
    if db_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create new user
    hashed_password = get_password_hash(user.password)
    new_user = User(
        username=user.username,
        email=user.email,
        hashed_password=hashed_password,
        full_name=user.full_name,
        district=user.district,
        is_active=True,
        is_admin=False
    )
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    
    return UserResponse(
        id=new_user.id,
        username=new_user.username,
        email=new_user.email,
        full_name=new_user.full_name,
        district=new_user.district,
        is_active=new_user.is_active,
        is_admin=new_user.is_admin
    )

@app.get("/api/auth/me", response_model=UserResponse)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        district=current_user.district,
        is_active=current_user.is_active,
        is_admin=current_user.is_admin
    )

# Include routers
app.include_router(language_router, prefix="/api/language", tags=["Language Model"])
app.include_router(speech_router, prefix="/api/speech", tags=["Speech"])
app.include_router(vision_router, prefix="/api/vision", tags=["Vision"])
app.include_router(agriculture_router, prefix="/api/agriculture", tags=["Agriculture"])
app.include_router(healthcare_router, prefix="/api/healthcare", tags=["Healthcare"])
app.include_router(jobs_router, prefix="/api/jobs", tags=["Jobs"])
app.include_router(chatbot_router, prefix="/api/chatbot", tags=["Chatbot"])
app.include_router(education_router, prefix="/api/education", tags=["Education"])
app.include_router(government_router, prefix="/api/government", tags=["Government"])
app.include_router(tourism_router, prefix="/api/tourism", tags=["Tourism"])
app.include_router(integration_router, prefix="/api/integration", tags=["Integration"])

# Health check endpoint
@app.get("/health")
def health_check():
    return {"status": "healthy"}
