import pandas as pd
import numpy as np
import pickle
import os
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

class AgricultureModel:
    def __init__(self):
        self.crop_recommendation_model = None
        self.scaler = None
        self.crop_names = [
            'rice', 'maize', 'chickpea', 'kidneybeans', 'pigeonpeas',
            'mothbeans', 'mungbean', 'blackgram', 'lentil', 'pomegranate',
            'banana', 'mango', 'grapes', 'watermelon', 'muskmelon', 'apple',
            'orange', 'papaya', 'coconut', 'cotton', 'jute', 'coffee'
        ]
        
        # Load or train the model
        self._load_or_train_model()
    
    def _load_or_train_model(self):
        """Load the pre-trained model or train a new one if not available"""
        model_path = os.path.join(os.path.dirname(__file__), '../data/crop_recommendation_model.pkl')
        scaler_path = os.path.join(os.path.dirname(__file__), '../data/crop_scaler.pkl')
        
        if os.path.exists(model_path) and os.path.exists(scaler_path):
            # Load pre-trained model and scaler
            with open(model_path, 'rb') as f:
                self.crop_recommendation_model = pickle.load(f)
            with open(scaler_path, 'rb') as f:
                self.scaler = pickle.load(f)
        else:
            # Train a new model
            self._train_model()
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(model_path), exist_ok=True)
            
            # Save the model and scaler
            with open(model_path, 'wb') as f:
                pickle.dump(self.crop_recommendation_model, f)
            with open(scaler_path, 'wb') as f:
                pickle.dump(self.scaler, f)
    
    def _train_model(self):
        """Train a new crop recommendation model using sample data"""
        # This is a simplified version. In a real scenario, you would use actual data
        # For demonstration, we'll create synthetic data based on known agricultural patterns
        
        # Create synthetic data
        np.random.seed(42)
        n_samples = 2000
        
        # Features: N, P, K, temperature, humidity, ph, rainfall
        data = {
            'N': np.random.randint(0, 140, n_samples),
            'P': np.random.randint(5, 145, n_samples),
            'K': np.random.randint(5, 205, n_samples),
            'temperature': np.random.uniform(8.8, 43.7, n_samples),
            'humidity': np.random.uniform(14.3, 99.9, n_samples),
            'ph': np.random.uniform(3.5, 9.9, n_samples),
            'rainfall': np.random.uniform(20.2, 298.6, n_samples)
        }
        
        # Create rules for crops based on known agricultural knowledge
        labels = []
        for i in range(n_samples):
            if data['temperature'][i] > 30 and data['humidity'][i] > 80:
                labels.append('rice')
            elif data['N'][i] > 100 and data['rainfall'][i] < 100:
                labels.append('cotton')
            elif data['ph'][i] < 5.5:
                labels.append('tea')
            elif data['temperature'][i] < 20:
                labels.append('apple')
            elif data['humidity'][i] < 40:
                labels.append('chickpea')
            elif data['rainfall'][i] > 200:
                labels.append('banana')
            else:
                # Randomly assign one of the crops
                labels.append(np.random.choice(self.crop_names))
        
        # Create DataFrame
        df = pd.DataFrame(data)
        df['label'] = labels
        
        # Split data
        X = df.drop('label', axis=1)
        y = df['label']
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Scale features
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        # Train model
        self.crop_recommendation_model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.crop_recommendation_model.fit(X_train_scaled, y_train)
        
        # Evaluate model
        X_test_scaled = self.scaler.transform(X_test)
        accuracy = self.crop_recommendation_model.score(X_test_scaled, y_test)
        print(f"Crop recommendation model accuracy: {accuracy:.2f}")
    
    def predict_crop(self, n, p, k, temperature, humidity, ph, rainfall):
        """
        Predict the best crop based on soil and climate conditions
        
        Args:
            n (float): Nitrogen content in soil (kg/ha)
            p (float): Phosphorus content in soil (kg/ha)
            k (float): Potassium content in soil (kg/ha)
            temperature (float): Temperature in Celsius
            humidity (float): Relative humidity in %
            ph (float): pH value of soil
            rainfall (float): Rainfall in mm
            
        Returns:
            dict: Prediction results with recommended crop and confidence
        """
        if self.crop_recommendation_model is None:
            return {"error": "Model not loaded"}
        
        # Prepare input data
        input_data = np.array([[n, p, k, temperature, humidity, ph, rainfall]])
        input_data_scaled = self.scaler.transform(input_data)
        
        # Make prediction
        prediction = self.crop_recommendation_model.predict(input_data_scaled)[0]
        
        # Get prediction probabilities
        probabilities = self.crop_recommendation_model.predict_proba(input_data_scaled)[0]
        confidence = round(float(max(probabilities) * 100), 2)
        
        # Get top 3 recommendations
        top_indices = np.argsort(probabilities)[-3:][::-1]
        top_crops = [self.crop_recommendation_model.classes_[i] for i in top_indices]
        top_probabilities = [round(float(probabilities[i] * 100), 2) for i in top_indices]
        
        # Create recommendations list
        recommendations = [
            {"crop": crop, "confidence": prob} 
            for crop, prob in zip(top_crops, top_probabilities)
        ]
        
        return {
            "recommended_crop": prediction,
            "confidence": confidence,
            "alternative_recommendations": recommendations,
            "soil_health": self._evaluate_soil_health(n, p, k, ph),
            "climate_suitability": self._evaluate_climate(temperature, humidity, rainfall)
        }
    
    def _evaluate_soil_health(self, n, p, k, ph):
        """Evaluate soil health based on NPK values and pH"""
        health = {}
        
        # Nitrogen evaluation
        if n < 30:
            health["nitrogen"] = {"status": "low", "recommendation": "Increase nitrogen through organic manures or fertilizers"}
        elif n < 80:
            health["nitrogen"] = {"status": "medium", "recommendation": "Maintain current nitrogen levels"}
        else:
            health["nitrogen"] = {"status": "high", "recommendation": "Reduce nitrogen application"}
        
        # Phosphorus evaluation
        if p < 20:
            health["phosphorus"] = {"status": "low", "recommendation": "Apply phosphatic fertilizers or bone meal"}
        elif p < 60:
            health["phosphorus"] = {"status": "medium", "recommendation": "Maintain current phosphorus levels"}
        else:
            health["phosphorus"] = {"status": "high", "recommendation": "Reduce phosphorus application"}
        
        # Potassium evaluation
        if k < 30:
            health["potassium"] = {"status": "low", "recommendation": "Apply potash fertilizers or wood ash"}
        elif k < 80:
            health["potassium"] = {"status": "medium", "recommendation": "Maintain current potassium levels"}
        else:
            health["potassium"] = {"status": "high", "recommendation": "Reduce potassium application"}
        
        # pH evaluation
        if ph < 5.5:
            health["ph"] = {"status": "acidic", "recommendation": "Apply lime to increase soil pH"}
        elif ph < 7.5:
            health["ph"] = {"status": "neutral", "recommendation": "Ideal pH for most crops"}
        else:
            health["ph"] = {"status": "alkaline", "recommendation": "Apply organic matter or sulfur to decrease pH"}
        
        return health
    
    def _evaluate_climate(self, temperature, humidity, rainfall):
        """Evaluate climate suitability"""
        climate = {}
        
        # Temperature evaluation
        if temperature < 15:
            climate["temperature"] = {"status": "cold", "suitable_crops": ["apple", "potato", "peas"]}
        elif temperature < 25:
            climate["temperature"] = {"status": "moderate", "suitable_crops": ["wheat", "maize", "chickpea"]}
        else:
            climate["temperature"] = {"status": "hot", "suitable_crops": ["rice", "cotton", "sugarcane"]}
        
        # Humidity evaluation
        if humidity < 40:
            climate["humidity"] = {"status": "low", "suitable_crops": ["chickpea", "millet", "sorghum"]}
        elif humidity < 70:
            climate["humidity"] = {"status": "moderate", "suitable_crops": ["wheat", "maize", "cotton"]}
        else:
            climate["humidity"] = {"status": "high", "suitable_crops": ["rice", "tea", "banana"]}
        
        # Rainfall evaluation
        if rainfall < 80:
            climate["rainfall"] = {"status": "low", "suitable_crops": ["millet", "chickpea", "sorghum"]}
        elif rainfall < 200:
            climate["rainfall"] = {"status": "moderate", "suitable_crops": ["wheat", "maize", "cotton"]}
        else:
            climate["rainfall"] = {"status": "high", "suitable_crops": ["rice", "tea", "banana"]}
        
        return climate

# Singleton instance
agriculture_model = AgricultureModel()
