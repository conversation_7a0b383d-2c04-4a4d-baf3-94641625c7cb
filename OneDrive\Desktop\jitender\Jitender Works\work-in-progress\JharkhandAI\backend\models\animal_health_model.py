import numpy as np
from sklearn.ensemble import RandomForestClassifier
from typing import Dict, List, Optional
import logging
import json
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class AnimalHealthModel:
    """
    AI model for animal health monitoring and disease prediction:
    - Health status monitoring
    - Disease prediction and prevention
    - Nutrition optimization
    - Vaccination scheduling
    """
    
    def __init__(self):
        self.disease_model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.nutrition_guidelines = self._load_nutrition_guidelines()
        self.vaccination_schedule = self._load_vaccination_schedule()
        self.disease_symptoms = self._load_disease_symptoms()
    
    def _load_nutrition_guidelines(self) -> Dict:
        """Load nutrition guidelines for different animal types"""
        guidelines_path = os.path.join(os.path.dirname(__file__), '../data/animal_nutrition_guidelines.json')
        if os.path.exists(guidelines_path):
            with open(guidelines_path, 'r') as f:
                return json.load(f)
        return {}
    
    def _load_vaccination_schedule(self) -> Dict:
        """Load vaccination schedules for different animal types"""
        schedule_path = os.path.join(os.path.dirname(__file__), '../data/vaccination_schedule.json')
        if os.path.exists(schedule_path):
            with open(schedule_path, 'r') as f:
                return json.load(f)
        return {}
    
    def _load_disease_symptoms(self) -> Dict:
        """Load disease symptoms database"""
        symptoms_path = os.path.join(os.path.dirname(__file__), '../data/disease_symptoms.json')
        if os.path.exists(symptoms_path):
            with open(symptoms_path, 'r') as f:
                return json.load(f)
        return {}
    
    def monitor_health_status(self, animal_data: Dict) -> Dict:
        """Monitor animal health status using vital signs and behavior patterns"""
        try:
            # Extract vital signs and behavior patterns
            vitals = self._extract_vitals(animal_data)
            behavior = self._analyze_behavior(animal_data)
            
            # Calculate health score
            health_score = self._calculate_health_score(vitals, behavior)
            
            # Generate health status report
            status_report = {
                "animal_id": animal_data["id"],
                "health_score": health_score,
                "vital_signs": vitals,
                "behavior_analysis": behavior,
                "recommendations": self._generate_health_recommendations(health_score, vitals, behavior)
            }
            
            return status_report
        except Exception as e:
            logger.error(f"Error monitoring health status: {str(e)}")
            return {"error": str(e)}
    
    def predict_diseases(self, symptoms: List[str], animal_type: str) -> Dict:
        """Predict potential diseases based on symptoms"""
        try:
            # Match symptoms with disease database
            potential_diseases = self._match_symptoms(symptoms, animal_type)
            
            # Calculate risk scores
            risk_scores = self._calculate_disease_risk(potential_diseases, symptoms)
            
            # Generate prevention measures
            prevention_measures = self._generate_prevention_measures(potential_diseases)
            
            return {
                "potential_diseases": [
                    {
                        "disease": disease,
                        "risk_score": score,
                        "prevention_measures": prevention_measures[disease]
                    }
                    for disease, score in risk_scores.items()
                ],
                "immediate_actions": self._get_immediate_actions(risk_scores)
            }
        except Exception as e:
            logger.error(f"Error predicting diseases: {str(e)}")
            return {"error": str(e)}
    
    def optimize_nutrition(self, animal_data: Dict) -> Dict:
        """Optimize nutrition based on animal type, age, and purpose"""
        try:
            animal_type = animal_data["type"]
            age = animal_data["age"]
            purpose = animal_data["purpose"]
            
            if animal_type not in self.nutrition_guidelines:
                return {"error": "Animal type not supported"}
            
            # Get base nutrition requirements
            base_requirements = self.nutrition_guidelines[animal_type]["base_requirements"]
            
            # Adjust for age and purpose
            adjusted_requirements = self._adjust_nutrition(base_requirements, age, purpose)
            
            # Generate feeding schedule
            feeding_schedule = self._generate_feeding_schedule(adjusted_requirements)
            
            return {
                "daily_requirements": adjusted_requirements,
                "feeding_schedule": feeding_schedule,
                "supplements": self._recommend_supplements(animal_data)
            }
        except Exception as e:
            logger.error(f"Error optimizing nutrition: {str(e)}")
            return {"error": str(e)}
    
    def get_vaccination_schedule(self, animal_data: Dict) -> Dict:
        """Get vaccination schedule and reminders"""
        try:
            animal_type = animal_data["type"]
            age = animal_data["age"]
            
            if animal_type not in self.vaccination_schedule:
                return {"error": "Animal type not supported"}
            
            # Get required vaccinations
            vaccinations = self.vaccination_schedule[animal_type]
            
            # Filter and sort by due date
            due_vaccinations = self._get_due_vaccinations(vaccinations, age)
            
            return {
                "upcoming_vaccinations": due_vaccinations,
                "reminders": self._generate_vaccination_reminders(due_vaccinations),
                "vaccination_history": animal_data.get("vaccination_history", [])
            }
        except Exception as e:
            logger.error(f"Error getting vaccination schedule: {str(e)}")
            return {"error": str(e)}
    
    def _extract_vitals(self, animal_data: Dict) -> Dict:
        """Extract and analyze vital signs"""
        vitals = {
            "temperature": animal_data.get("temperature"),
            "heart_rate": animal_data.get("heart_rate"),
            "respiratory_rate": animal_data.get("respiratory_rate"),
            "weight": animal_data.get("weight")
        }
        return self._analyze_vitals(vitals, animal_data["type"])
    
    def _analyze_behavior(self, animal_data: Dict) -> Dict:
        """Analyze animal behavior patterns"""
        behavior_patterns = {
            "activity_level": animal_data.get("activity_level"),
            "appetite": animal_data.get("appetite"),
            "water_intake": animal_data.get("water_intake"),
            "social_behavior": animal_data.get("social_behavior")
        }
        return self._evaluate_behavior(behavior_patterns)
    
    def _calculate_health_score(self, vitals: Dict, behavior: Dict) -> float:
        """Calculate overall health score"""
        vital_score = sum(vitals.values()) / len(vitals)
        behavior_score = sum(behavior.values()) / len(behavior)
        return (vital_score + behavior_score) / 2
    
    def _match_symptoms(self, symptoms: List[str], animal_type: str) -> Dict:
        """Match symptoms with known diseases"""
        matches = {}
        for disease, data in self.disease_symptoms.get(animal_type, {}).items():
            match_score = len(set(symptoms) & set(data["symptoms"])) / len(data["symptoms"])
            if match_score > 0.3:  # Threshold for potential match
                matches[disease] = match_score
        return matches