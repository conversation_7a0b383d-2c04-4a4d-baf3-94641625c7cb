"""Business Feature Extractor for AI-driven business analysis.
This module handles feature extraction for business advisory, financial planning, and marketing."""

from typing import Dict, List, Any, Optional
import numpy as np
from datetime import datetime
from sklearn.preprocessing import StandardScaler

class BusinessFeatureExtractor:
    """Extracts and processes features from business data for AI analysis."""
    
    def __init__(self):
        """Initialize the feature extractor with necessary components."""
        self.scaler = StandardScaler()
        self.business_type_encoding = {
            'retail': 0,
            'manufacturing': 1,
            'service': 2,
            'artisan': 3,
            'agriculture': 4,
            'food': 5,
            'other': 6
        }
        self.sector_encoding = {
            'traditional': 0,
            'modern': 1,
            'hybrid': 2
        }
    
    def extract_business_features(self, data: Dict) -> np.ndarray:
        """Extract features from business advisory request data.
        
        Args:
            data: Dictionary containing business information
            
        Returns:
            numpy array of extracted features
        """
        features = []
        
        # Basic business metrics
        features.extend([
            float(data.get('years_in_operation', 0)),
            float(data.get('annual_revenue', 0)),
            float(data.get('employee_count', 0)),
            self.business_type_encoding.get(data.get('business_type', 'other'), 6),
            self.sector_encoding.get(data.get('sector', 'traditional'), 0)
        ])
        
        # Financial health indicators
        features.extend([
            float(data.get('profit_margin', 0)),
            float(data.get('cash_reserves', 0)),
            float(data.get('debt_ratio', 0))
        ])
        
        # Market position indicators
        features.extend([
            float(data.get('market_share', 0)),
            len(data.get('competitors', [])),
            len(data.get('products_services', []))
        ])
        
        # Growth indicators
        features.extend([
            float(data.get('growth_rate', 0)),
            float(data.get('customer_retention', 0)),
            float(data.get('market_penetration', 0))
        ])
        
        return np.array(features)
    
    def extract_financial_features(self, data: Dict) -> np.ndarray:
        """Extract features from financial data.
        
        Args:
            data: Dictionary containing financial information
            
        Returns:
            numpy array of extracted features
        """
        features = []
        
        # Revenue and expense metrics
        features.extend([
            float(data.get('monthly_revenue', 0)),
            float(data.get('monthly_expenses', 0)),
            float(data.get('fixed_costs', 0)),
            float(data.get('variable_costs', 0))
        ])
        
        # Asset and liability metrics
        features.extend([
            float(data.get('current_assets', 0)),
            float(data.get('current_liabilities', 0)),
            float(data.get('total_assets', 0)),
            float(data.get('total_liabilities', 0))
        ])
        
        # Growth and projection metrics
        features.extend([
            float(data.get('growth_rate', 0)),
            float(data.get('projection_months', 12)),
            1 if data.get('seasonal_factors', False) else 0
        ])
        
        return np.array(features)
    
    def extract_loan_eligibility_features(self, data: Dict) -> np.ndarray:
        """Extract features for loan eligibility assessment.
        
        Args:
            data: Dictionary containing loan application information
            
        Returns:
            numpy array of extracted features
        """
        features = []
        
        # Business metrics
        features.extend([
            float(data.get('years_in_operation', 0)),
            float(data.get('annual_revenue', 0)),
            float(data.get('monthly_profit', 0))
        ])
        
        # Financial metrics
        features.extend([
            float(data.get('credit_score', 0)),
            float(data.get('debt_to_income', 0)),
            float(data.get('current_ratio', 0))
        ])
        
        # Loan specific metrics
        features.extend([
            float(data.get('loan_amount', 0)),
            float(data.get('loan_term', 0)),
            float(data.get('collateral_value', 0))
        ])
        
        return np.array(features)
    
    def scale_features(self, features: np.ndarray) -> np.ndarray:
        """Scale features using StandardScaler.
        
        Args:
            features: numpy array of features to scale
            
        Returns:
            scaled features as numpy array
        """
        return self.scaler.fit_transform(features.reshape(1, -1))