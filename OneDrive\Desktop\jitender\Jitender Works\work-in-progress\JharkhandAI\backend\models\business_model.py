from typing import List, Dict, Any, Optional
import random
import uuid
import math
from datetime import datetime
import json
import os
from ..schemas.business_schemas import (
    BusinessAdvisoryRequest, 
    BusinessAdvisoryResponse,
    BusinessRecommendation,
    GrowthOpportunity,
    FinancialDataRequest,
    CashFlowResponse,
    FinancialPlanResponse,
    InvestmentNeeds,
    RiskAssessment,
    LoanEligibilityRequest,
    LoanEligibilityResponse,
    LoanApplicationRequest,
    LoanApplicationResponse,
    LoanScheme,
    MarketingStrategyRequest,
    MarketingStrategyResponse,
    ContentIdea,
    ContentItem,
    MarketingContentResponse,
    CompetitorAnalysisRequest,
    CompetitorAnalysisResponse,
    Competitor
)

class BusinessModel:
    """
    Model for business-related operations including advisory, financial planning,
    loan assistance, and marketing.
    """
    
    def __init__(self):
        """
        Initialize the business model with default data.
        """
        # Load loan schemes
        self.loan_schemes = [
            LoanScheme(
                id="mudra_shishu",
                name="<PERSON><PERSON>",
                max_amount=50000,
                interest_rate=0.07,
                eligibility="Small businesses with annual turnover under ₹5 lakhs",
                subsidy="Interest subsidy of 2% for timely repayment"
            ),
            LoanScheme(
                id="mudra_kishor",
                name="Mudra Kishor Loan",
                max_amount=500000,
                interest_rate=0.10,
                eligibility="Small businesses with annual turnover between ₹5-25 lakhs",
                subsidy="No processing fee"
            ),
            LoanScheme(
                id="stand_up_india",
                name="Stand Up India Loan",
                max_amount=1000000,
                interest_rate=0.11,
                eligibility="SC/ST and women entrepreneurs",
                subsidy="Collateral-free loan up to 25% of project cost"
            ),
            LoanScheme(
                id="pmegp",
                name="PMEGP Scheme",
                max_amount=2500000,
                interest_rate=0.11,
                eligibility="New enterprises in manufacturing, service or trading",
                subsidy="15-35% subsidy on project cost"
            )
        ]
    
    # Business Advisory
    def generate_business_advisory(self, request: BusinessAdvisoryRequest) -> BusinessAdvisoryResponse:
        """
        Generate personalized business advisory recommendations.
        """
        # In a real implementation, this would use AI/ML to generate personalized recommendations
        # For now, we'll return sample data based on business type
        
        recommendations = [
            BusinessRecommendation(
                title="Implement Digital Payments",
                description="Accept UPI, credit cards, and other digital payment methods to increase sales.",
                impact="high",
                implementation_difficulty="low",
                estimated_cost="₹5,000 - ₹10,000"
            ),
            BusinessRecommendation(
                title="Create Social Media Presence",
                description="Establish profiles on Facebook, Instagram and WhatsApp Business to reach more customers.",
                impact="medium",
                implementation_difficulty="low",
                estimated_cost="₹0 - ₹5,000"
            ),
            BusinessRecommendation(
                title="Join Local Business Association",
                description="Network with other local businesses for partnerships and knowledge sharing.",
                impact="medium",
                implementation_difficulty="low",
                estimated_cost="₹1,000 - ₹5,000"
            )
        ]
        
        market_insights = [
            "Local consumer spending has increased by 15% in your category over the past year",
            "Customers are increasingly looking for businesses with online presence",
            "There is growing demand for locally-made products in your area"
        ]
        
        growth_opportunities = [
            GrowthOpportunity(
                title="Expand Product Range",
                description="Add complementary products to increase average transaction value",
                potential_impact="₹10,000 - ₹50,000 monthly revenue increase"
            ),
            GrowthOpportunity(
                title="Offer Home Delivery",
                description="Provide delivery services within 5km radius",
                potential_impact="₹5,000 - ₹15,000 monthly revenue increase"
            )
        ]
        
        return BusinessAdvisoryResponse(
            recommendations=recommendations,
            market_insights=market_insights,
            growth_opportunities=growth_opportunities
        )
    
    # Financial Planning - Cash Flow
    def predict_cash_flow(self, request: FinancialDataRequest) -> CashFlowResponse:
        """
        Predict cash flow based on financial data.
        """
        # In a real implementation, this would use financial models to predict cash flow
        # For now, we'll generate sample projections
        
        monthly_revenue = request.monthly_revenue
        monthly_expenses = request.monthly_expenses
        growth_rate = request.growth_rate / 100  # Convert percentage to decimal
        months = request.projection_months
        
        revenue_projections = []
        expense_projections = []
        profit_projections = []
        
        for i in range(months):
            # Apply growth rate to revenue
            projected_revenue = monthly_revenue * (1 + growth_rate) ** i
            
            # Expenses grow slower than revenue
            projected_expenses = monthly_expenses * (1 + growth_rate * 0.2) ** i
            
            # Calculate profit
            projected_profit = projected_revenue - projected_expenses
            
            revenue_projections.append(round(projected_revenue))
            expense_projections.append(round(projected_expenses))
            profit_projections.append(round(projected_profit))
        
        # Generate insights based on projections
        insights = [
            f"Your business shows a healthy profit margin of approximately {round((profit_projections[0] / revenue_projections[0]) * 100)}%",
            f"Revenue is projected to grow steadily at {growth_rate * 100}% per month",
            "Consider investing in inventory management to optimize costs",
            f"Cash reserves should be maintained at minimum ₹{round(monthly_expenses * 1.5)} to handle fluctuations"
        ]
        
        return CashFlowResponse(
            revenue_projections=revenue_projections,
            expense_projections=expense_projections,
            profit_projections=profit_projections,
            insights=insights
        )
    
    # Financial Planning - Financial Plan
    def generate_financial_plan(self, request: FinancialDataRequest) -> FinancialPlanResponse:
        """
        Generate a comprehensive financial plan.
        """
        # In a real implementation, this would use financial models to generate a plan
        # For now, we'll generate sample data
        
        # Calculate investment needs based on business type and expansion plans
        expansion_amount = request.monthly_revenue * 2
        technology_amount = request.monthly_revenue * 1
        marketing_amount = request.monthly_revenue * 0.6
        other_amount = request.monthly_revenue * 0.4
        total_investment = expansion_amount + technology_amount + marketing_amount + other_amount
        
        investment_needs = InvestmentNeeds(
            expansion=round(expansion_amount),
            technology=round(technology_amount),
            marketing=round(marketing_amount),
            other=round(other_amount),
            total=round(total_investment)
        )
        
        # Calculate break-even point (in months)
        monthly_profit = request.monthly_revenue - request.monthly_expenses
        break_even_point = math.ceil(total_investment / monthly_profit) if monthly_profit > 0 else 24
        
        # Calculate ROI
        annual_profit = monthly_profit * 12
        roi = round((annual_profit / total_investment) * 100) if total_investment > 0 else 0
        
        # Calculate cash reserve needed
        cash_reserve_needed = round(request.monthly_expenses * 3)
        
        # Calculate debt service ratio
        debt_service_ratio = round((monthly_profit / (total_investment / 24)) * 10) / 10 if total_investment > 0 else 0
        
        # Generate financing options
        financing_options = [
            "MSME business loan with 12% interest rate",
            "Mudra Loan scheme with 7% interest rate for small businesses",
            "State government subsidy program for local entrepreneurs",
            "Equipment financing with 10% down payment"
        ]
        
        # Generate recommended actions
        recommended_actions = [
            "Apply for Mudra Loan to finance expansion plans",
            "Implement inventory management system to reduce costs",
            "Increase prices by 5% on premium products to improve margins",
            "Negotiate with suppliers for better payment terms"
        ]
        
        # Generate risk assessment
        risk_assessment = [
            RiskAssessment(
                name="Seasonal Demand Fluctuation",
                description="Your business may experience lower sales during monsoon season",
                mitigation="Diversify product offerings and create special promotions for slow periods"
            ),
            RiskAssessment(
                name="Rising Input Costs",
                description="Raw material costs may increase due to supply chain disruptions",
                mitigation="Maintain relationships with multiple suppliers and consider bulk purchasing"
            ),
            RiskAssessment(
                name="Cash Flow Gaps",
                description="Delayed payments from customers could create temporary cash shortages",
                mitigation="Implement stricter payment terms and maintain adequate cash reserves"
            )
        ]
        
        return FinancialPlanResponse(
            investment_needs=investment_needs,
            break_even_point=break_even_point,
            roi=roi,
            cash_reserve_needed=cash_reserve_needed,
            debt_service_ratio=debt_service_ratio,
            financing_options=financing_options,
            recommended_actions=recommended_actions,
            risk_assessment=risk_assessment
        )
    
    # Loan Assistance - Get Loan Schemes
    def get_loan_schemes(self) -> List[LoanScheme]:
        """
        Get available loan schemes.
        """
        return self.loan_schemes
    
    # Loan Assistance - Check Eligibility
    def check_loan_eligibility(self, request: LoanEligibilityRequest) -> LoanEligibilityResponse:
        """
        Check eligibility for business loans.
        """
        # In a real implementation, this would use credit scoring models
        # For now, we'll use simple rules
        
        annual_revenue = request.annual_revenue
        monthly_profit = request.monthly_profit
        amount_required = request.amount_required
        repayment_period = request.repayment_period
        
        # Calculate monthly repayment amount (simple calculation)
        monthly_repayment = amount_required / repayment_period
        
        # Check if monthly profit can cover repayment with buffer
        can_repay = monthly_profit > monthly_repayment * 1.5
        
        # Check if loan amount is reasonable compared to revenue
        reasonable_amount = amount_required <= annual_revenue * 0.5
        
        eligible = can_repay and reasonable_amount
        
        # Calculate eligible amount
        eligible_amount = min(annual_revenue * 0.5, monthly_profit * repayment_period * 0.6) if eligible else 0
        
        # Generate suggestions
        suggestions = [
            "Maintain proper business documentation and financial records",
            "Reduce existing debt obligations to improve eligibility for higher loan amounts",
            "Consider government schemes specifically designed for your business category"
        ]
        
        if not can_repay:
            suggestions.append("Increase monthly profit or request a longer repayment period")
        
        if not reasonable_amount:
            suggestions.append("Request a lower loan amount or demonstrate higher annual revenue")
        
        return LoanEligibilityResponse(
            eligible=eligible,
            eligible_amount=round(eligible_amount),
            suggestions=suggestions
        )
    
    # Loan Assistance - Generate Application
    def generate_loan_application(self, scheme_id: str, request: LoanApplicationRequest) -> LoanApplicationResponse:
        """
        Generate a loan application for a specific scheme.
        """
        # Find the scheme
        scheme = next((s for s in self.loan_schemes if s.id == scheme_id), None)
        if not scheme:
            raise ValueError(f"Loan scheme with ID {scheme_id} not found")
        
        # Generate application ID
        application_id = f"LA-{datetime.now().year}-{uuid.uuid4().hex[:5]}"
        
        # Set date submitted
        date_submitted = datetime.now().isoformat()
        
        # Set required documents based on scheme
        required_documents = [
            "Business registration certificate",
            "PAN card and Aadhaar card",
            "Last 6 months bank statements",
            "GST returns (if applicable)",
            "Business plan or project report",
            "Proof of business address"
        ]
        
        # Set submission process
        submission_process = "Visit your nearest bank branch with the application form and required documents. The loan officer will verify your documents and guide you through the process."
        
        # Set timeline
        timeline = "Application processing typically takes 7-14 working days. After approval, disbursement will be made within 3-5 working days."
        
        return LoanApplicationResponse(
            application_id=application_id,
            date_submitted=date_submitted,
            scheme_name=scheme.name,
            amount_requested=request.amount_required,
            status="Submitted",
            required_documents=required_documents,
            submission_process=submission_process,
            timeline=timeline
        )
    
    # Marketing - Generate Strategy
    def generate_marketing_strategy(self, request: MarketingStrategyRequest) -> MarketingStrategyResponse:
        """
        Generate a marketing strategy based on business profile.
        """
        # In a real implementation, this would use AI/ML to generate a personalized strategy
        # For now, we'll return sample data
        
        strategy_summary = "Focus on digital marketing channels to reach local customers, emphasize your unique local products, and implement a loyalty program to increase repeat business."
        
        target_channels = ["Facebook", "WhatsApp Business", "Google My Business", "Local Events"]
        
        content_ideas = [
            ContentIdea(
                type="Social Media Posts",
                topics=[
                    "Product showcases with local context",
                    "Customer testimonials and reviews",
                    "Behind-the-scenes of your business",
                    "Special offers and promotions"
                ]
            ),
            ContentIdea(
                type="WhatsApp Messages",
                topics=[
                    "New product announcements",
                    "Limited-time offers",
                    "Festive greetings with promotions",
                    "Order confirmations and updates"
                ]
            )
        ]
        
        budget_allocation = {
            "digital_ads": "40%",
            "content_creation": "25%",
            "social_media": "20%",
            "traditional_media": "10%",
            "influencer_marketing": "5%"
        }
        
        timeline = {
            "month_1": "Set up social media profiles and Google My Business",
            "month_2": "Create content calendar and begin regular posting",
            "month_3": "Implement loyalty program and start local promotions",
            "month_6": "Evaluate performance and adjust strategy"
        }
        
        return MarketingStrategyResponse(
            strategy_summary=strategy_summary,
            target_channels=target_channels,
            content_ideas=content_ideas,
            budget_allocation=budget_allocation,
            timeline=timeline
        )
    
    # Marketing - Generate Content
    def generate_marketing_content(
        self, 
        business_profile: MarketingStrategyRequest,
        content_type: str,
        platform: str,
        count: int
    ) -> MarketingContentResponse:
        """
        Generate marketing content based on business profile and content type.
        """
        # In a real implementation, this would use AI/ML to generate personalized content
        # For now, we'll return sample data
        
        # Sample content templates
        social_media_templates = [
            "Discover our new range of locally-sourced products! 🌱 Supporting local artisans while bringing you the best quality. Visit our store today and get 10% off on your first purchase.",
            "Our customers love us! ⭐⭐⭐⭐⭐ \"The products are amazing and the service is even better!\" - Priya K. Come experience the difference yourself!",
            "Festival special offer! 🎉 Get 15% off on all products this week. Limited time only! Tag a friend who would love our products.",
            "Behind the scenes at {business_name}! See how we create our products with care and attention to detail. Quality is our priority!",
            "We're proud to be part of the {location} community! Supporting local businesses helps create jobs and strengthen our economy. Shop local, shop {business_name}!"
        ]
        
        # Sample hashtags
        hashtags_by_platform = {
            "facebook": ["#SupportLocal", "#QualityProducts", "#LocalBusiness"],
            "instagram": ["#ShopLocal", "#HandmadeWithLove", "#SmallBusinessSupport"],
            "twitter": ["#SupportSmallBusiness", "#ShopLocal", "#QualityMatters"],
            "linkedin": ["#BusinessGrowth", "#LocalEntrepreneur", "#QualityProducts"],
            "whatsapp": []
        }
        
        # Generate content
        content = []
        for i in range(min(count, len(social_media_templates))):
            text = social_media_templates[i].replace("{business_name}", business_profile.business_name).replace("{location}", business_profile.location)
            
            content_item = ContentItem(
                text=text,
                hashtags=hashtags_by_platform.get(platform, [])
            )
            content.append(content_item)
        
        # Generate tips
        tips = [
            "Post consistently at least 3 times per week",
            "Use high-quality images of your products",
            "Engage with comments and messages promptly",
            "Include a clear call-to-action in each post"
        ]
        
        return MarketingContentResponse(
            content=content,
            tips=tips
        )
    
    # Marketing - Analyze Competitors
    def analyze_competitors(self, request: CompetitorAnalysisRequest) -> CompetitorAnalysisResponse:
        """
        Analyze competitors based on business profile.
        """
        # In a real implementation, this would use AI/ML and market data to analyze competitors
        # For now, we'll return sample data
        
        top_competitors = [
            Competitor(
                name="Local Crafts Store",
                description="Offers similar products but focuses more on handicrafts"
            ),
            Competitor(
                name="Urban Essentials",
                description="Higher price point but has established brand recognition"
            ),
            Competitor(
                name="Village Emporium",
                description="Government-supported store with wider product range"
            )
        ]
        
        competitive_advantages = [
            "Your products have better quality control",
            "Your pricing is more competitive",
            "You offer personalized customer service",
            "Your location has better foot traffic"
        ]
        
        competitive_disadvantages = [
            "Competitors have stronger online presence",
            "Some competitors have wider product selection",
            "Limited marketing budget compared to larger competitors"
        ]
        
        market_gaps = [
            "No businesses offering home delivery in your area",
            "Limited options for customized products",
            "Lack of loyalty programs among competitors"
        ]
        
        recommendations = [
            "Develop a simple e-commerce website or WhatsApp ordering system",
            "Introduce a loyalty program to encourage repeat business",
            "Offer product customization as a premium service",
            "Focus marketing on your competitive pricing and quality"
        ]
        
        return CompetitorAnalysisResponse(
            top_competitors=top_competitors,
            competitive_advantages=competitive_advantages,
            competitive_disadvantages=competitive_disadvantages,
            market_gaps=market_gaps,
            recommendations=recommendations
        )
