from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Text, DateTime, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import datetime

Base = declarative_base()

class BaseModel(object):
    """Base model for all database models"""
    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

class User(Base, BaseModel):
    """User model for authentication and user management"""
    __tablename__ = "users"

    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    full_name = Column(String)
    hashed_password = Column(String)
    district = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    
    # Relationships
    agriculture_queries = relationship("AgricultureQuery", back_populates="user")
    healthcare_queries = relationship("HealthcareQuery", back_populates="user")
    job_applications = relationship("JobApplication", back_populates="user")
    chat_messages = relationship("ChatMessage", back_populates="user")
    education_enrollments = relationship("CourseEnrollment", back_populates="user")
    tourism_bookings = relationship("TourismBooking", back_populates="user")
    service_logs = relationship("ServiceLog", back_populates="user")
    feedback = relationship("Feedback", back_populates="user")
    regional_usage = relationship("RegionalUsage", back_populates="user")

class AgricultureQuery(Base, BaseModel):
    """Model for agriculture related queries"""
    __tablename__ = "agriculture_queries"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    query_type = Column(String)  # crop_recommendation, weather_forecast, etc.
    query_text = Column(Text)
    response_text = Column(Text)
    location = Column(String, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="agriculture_queries")

class HealthcareQuery(Base, BaseModel):
    """Model for healthcare related queries"""
    __tablename__ = "healthcare_queries"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    query_type = Column(String)  # symptom_check, facility_finder, etc.
    query_text = Column(Text)
    response_text = Column(Text)
    location = Column(String, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="healthcare_queries")

class Job(Base, BaseModel):
    """Model for job listings"""
    __tablename__ = "jobs"
    
    title = Column(String)
    company = Column(String)
    location = Column(String)
    description = Column(Text)
    requirements = Column(Text)
    salary_range = Column(String, nullable=True)
    job_type = Column(String)  # full-time, part-time, contract
    is_active = Column(Boolean, default=True)
    
    # Relationships
    applications = relationship("JobApplication", back_populates="job")

class JobApplication(Base, BaseModel):
    """Model for job applications"""
    __tablename__ = "job_applications"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    job_id = Column(Integer, ForeignKey("jobs.id"))
    cover_letter = Column(Text, nullable=True)
    resume_url = Column(String, nullable=True)
    status = Column(String, default="applied")  # applied, reviewed, interviewed, offered, rejected
    
    # Relationships
    user = relationship("User", back_populates="job_applications")
    job = relationship("Job", back_populates="applications")

class ChatMessage(Base, BaseModel):
    """Model for chat messages"""
    __tablename__ = "chat_messages"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    message_text = Column(Text)
    is_user_message = Column(Boolean, default=True)
    response_text = Column(Text, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="chat_messages")

class ServiceLog(Base, BaseModel):
    """Model for service usage logging and analytics"""
    __tablename__ = "service_logs"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    service_name = Column(String, nullable=False)
    timestamp = Column(DateTime, default=func.now())
    response_time = Column(Float)
    status_code = Column(Integer)

class Feedback(Base, BaseModel):
    """Model for user feedback on services"""
    __tablename__ = "feedback"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    service_name = Column(String, nullable=False)
    rating = Column(Integer)
    comment = Column(Text)
    
    user = relationship("User", back_populates="feedback")

class RegionalUsage(Base, BaseModel):
    """Model for tracking regional service usage"""
    __tablename__ = "regional_usage"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    region = Column(String, nullable=False)
    service_name = Column(String, nullable=False)
    usage_count = Column(Integer, default=0)
    last_used = Column(DateTime, default=func.now())
    
    user = relationship("User", back_populates="regional_usage")
    user = relationship("User", back_populates="chat_messages")

class Course(Base, BaseModel):
    """Model for educational courses"""
    __tablename__ = "courses"
    
    title = Column(String)
    description = Column(Text)
    instructor = Column(String)
    duration = Column(String)
    level = Column(String)  # beginner, intermediate, advanced
    category = Column(String)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    enrollments = relationship("CourseEnrollment", back_populates="course")

class CourseEnrollment(Base, BaseModel):
    """Model for course enrollments"""
    __tablename__ = "course_enrollments"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    course_id = Column(Integer, ForeignKey("courses.id"))
    enrollment_date = Column(DateTime, default=func.now())
    completion_status = Column(String, default="enrolled")  # enrolled, in-progress, completed
    
    # Relationships
    user = relationship("User", back_populates="education_enrollments")
    course = relationship("Course", back_populates="enrollments")

class GovernmentScheme(Base, BaseModel):
    """Model for government schemes"""
    __tablename__ = "government_schemes"
    
    title = Column(String)
    description = Column(Text)
    eligibility = Column(Text)
    benefits = Column(Text)
    application_process = Column(Text)
    department = Column(String)
    is_active = Column(Boolean, default=True)

class TourismDestination(Base, BaseModel):
    """Model for tourism destinations"""
    __tablename__ = "tourism_destinations"
    
    name = Column(String)
    description = Column(Text)
    location = Column(String)
    image_url = Column(String, nullable=True)
    category = Column(String)  # heritage, nature, adventure, etc.
    
    # Relationships
    bookings = relationship("TourismBooking", back_populates="destination")

class TourismBooking(Base, BaseModel):
    """Model for tourism bookings"""
    __tablename__ = "tourism_bookings"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    destination_id = Column(Integer, ForeignKey("tourism_destinations.id"))
    booking_date = Column(DateTime)
    number_of_people = Column(Integer)
    status = Column(String, default="booked")  # booked, cancelled, completed
    
    # Relationships
    user = relationship("User", back_populates="tourism_bookings")
    destination = relationship("TourismDestination", back_populates="bookings")
