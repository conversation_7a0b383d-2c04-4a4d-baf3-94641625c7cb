from typing import List, Dict, Optional
from datetime import datetime
import logging
from fastapi import H<PERSON>P<PERSON>x<PERSON>
from pydantic import BaseModel
import cv2
import numpy as np
import pytesseract
from face_recognition import face_encodings, face_locations
from fingerprint_enhancer import enhance_fingerprint

class DocumentBiometricAI:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.supported_doc_types = [
            'aadhar_card',
            'pan_card',
            'voter_id',
            'driving_license',
            'ration_card'
        ]
        self.supported_biometrics = [
            'fingerprint',
            'face',
            'iris'
        ]

    async def process_document(self, image_data: bytes, doc_type: str) -> Dict:
        """Process and digitize physical documents using OCR"""
        try:
            # Convert bytes to OpenCV format
            nparr = np.frombuffer(image_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            # Preprocess image
            processed_img = self._preprocess_image(img)

            # Extract text using OCR
            extracted_text = pytesseract.image_to_string(processed_img)

            # Extract structured data based on document type
            structured_data = self._extract_structured_data(extracted_text, doc_type)

            return {
                'status': 'success',
                'doc_type': doc_type,
                'extracted_text': extracted_text,
                'structured_data': structured_data,
                'confidence_score': self._calculate_confidence(extracted_text)
            }

        except Exception as e:
            self.logger.error(f"Error processing document: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    def _preprocess_image(self, img: np.ndarray) -> np.ndarray:
        """Preprocess image for better OCR results"""
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply thresholding
        thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
        
        # Remove noise
        denoised = cv2.fastNlMeansDenoising(thresh)
        
        return denoised

    def _extract_structured_data(self, text: str, doc_type: str) -> Dict:
        """Extract structured data based on document type"""
        data = {}
        
        if doc_type == 'aadhar_card':
            # Extract Aadhar number using regex
            data['aadhar_number'] = self._extract_aadhar_number(text)
            data['name'] = self._extract_name(text)
            data['dob'] = self._extract_dob(text)
            
        elif doc_type == 'pan_card':
            # Extract PAN details
            data['pan_number'] = self._extract_pan_number(text)
            data['name'] = self._extract_name(text)
            
        # Add more document types as needed
        
        return data

    async def verify_biometric(self, biometric_data: bytes, biometric_type: str, stored_template: bytes) -> Dict:
        """Verify biometric data against stored template"""
        try:
            if biometric_type == 'fingerprint':
                return await self._verify_fingerprint(biometric_data, stored_template)
            elif biometric_type == 'face':
                return await self._verify_face(biometric_data, stored_template)
            else:
                raise HTTPException(status_code=400, detail=f"Unsupported biometric type: {biometric_type}")

        except Exception as e:
            self.logger.error(f"Error verifying biometric: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def _verify_fingerprint(self, fingerprint_data: bytes, stored_template: bytes) -> Dict:
        """Verify fingerprint against stored template"""
        # Convert bytes to image
        nparr = np.frombuffer(fingerprint_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_GRAYSCALE)

        # Enhance fingerprint
        enhanced = enhance_fingerprint(img)

        # Extract features and match with stored template
        match_score = self._match_fingerprint_features(enhanced, stored_template)

        return {
            'match': match_score > 0.8,
            'confidence': match_score,
            'timestamp': datetime.now().isoformat()
        }

    async def _verify_face(self, face_data: bytes, stored_template: bytes) -> Dict:
        """Verify face against stored template"""
        # Convert bytes to image
        nparr = np.frombuffer(face_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        # Detect face locations
        face_locs = face_locations(img)
        if not face_locs:
            raise HTTPException(status_code=400, detail="No face detected in image")

        # Get face encodings
        face_enc = face_encodings(img, face_locs)[0]
        stored_enc = np.frombuffer(stored_template, dtype=np.float64)

        # Calculate similarity
        similarity = np.linalg.norm(face_enc - stored_enc)

        return {
            'match': similarity < 0.6,
            'confidence': 1 - similarity,
            'timestamp': datetime.now().isoformat()
        }

    def _calculate_confidence(self, extracted_text: str) -> float:
        """Calculate confidence score for extracted text"""
        # Implement confidence calculation logic
        # This is a simplified version
        if not extracted_text:
            return 0.0
        
        # Calculate based on text length and quality indicators
        words = extracted_text.split()
        if len(words) < 5:
            return 0.3
        elif len(words) < 20:
            return 0.7
        else:
            return 0.9