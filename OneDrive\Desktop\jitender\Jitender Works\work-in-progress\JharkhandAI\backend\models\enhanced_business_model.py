"""Enhanced Business Model for MSMEs & Business Growth.
This module provides AI-driven business advisory, financial planning, and smart marketing capabilities."""

from typing import List, Dict, Any, Optional
import numpy as np
import pandas as pd
from datetime import datetime
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from transformers import pipeline
from .marketing_model import MarketingAIModel
from ..schemas.business_schemas import (
    BusinessAdvisoryRequest, 
    BusinessAdvisoryResponse,
    BusinessRecommendation,
    GrowthOpportunity,
    FinancialDataRequest,
    CashFlowResponse,
    FinancialPlanResponse,
    InvestmentNeeds,
    RiskAssessment,
    LoanEligibilityRequest,
    LoanEligibilityResponse,
    LoanApplicationRequest,
    LoanApplicationResponse,
    LoanScheme
)

class EnhancedBusinessModel:
    """Enhanced model for business-related operations with AI capabilities."""
    
    def __init__(self):
        """Initialize the enhanced business model with AI components."""
        # Initialize AI components
        try:
            self.text_generator = pipeline("text-generation", model="gpt2")
            self.classifier = pipeline("zero-shot-classification")
        except Exception as e:
            print(f"Error loading NLP models: {e}")
            self.text_generator = None
            self.classifier = None

        # Initialize ML models
        self.cash_flow_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.loan_eligibility_model = RandomForestClassifier(n_estimators=100, random_state=42)
        
        # Initialize feature extractor
        self.feature_extractor = BusinessFeatureExtractor()
        
        # Initialize marketing AI
        self.marketing_ai = MarketingAIModel()
        
        # Load loan schemes
        self.loan_schemes = self._load_loan_schemes()
    
    def generate_business_advisory(self, request: BusinessAdvisoryRequest) -> BusinessAdvisoryResponse:
        """Generate AI-driven business advisory recommendations."""
        # Extract business features
        features = self._extract_business_features(request)
        
        # Generate recommendations using zero-shot classification
        if self.classifier:
            recommendations = self._generate_ai_recommendations(features)
        else:
            recommendations = self._generate_fallback_recommendations(request)
        
        # Analyze growth opportunities
        opportunities = self._analyze_growth_opportunities(features)
        
        return BusinessAdvisoryResponse(
            recommendations=recommendations,
            opportunities=opportunities,
            analysis_date=datetime.now().isoformat()
        )
    
    def predict_cash_flow(self, request: FinancialDataRequest) -> CashFlowResponse:
        """Predict cash flow using ML model."""
        # Extract financial features
        features = self._extract_financial_features(request)
        
        # Scale features
        scaled_features = self.scaler.transform(features.reshape(1, -1))
        
        # Predict cash flow
        predictions = self.cash_flow_model.predict(scaled_features)
        
        return CashFlowResponse(
            predicted_cash_flow=float(predictions[0]),
            confidence_score=0.85,  # Calculate actual confidence
            prediction_date=datetime.now().isoformat()
        )
    
    def check_loan_eligibility(self, request: LoanEligibilityRequest) -> LoanEligibilityResponse:
        """Check loan eligibility using ML model."""
        # Extract eligibility features
        features = self._extract_eligibility_features(request)
        
        # Scale features
        scaled_features = self.scaler.transform(features.reshape(1, -1))
        
        # Predict eligibility
        eligibility_score = self.loan_eligibility_model.predict_proba(scaled_features)[0][1]
        
        # Get suitable loan schemes
        suitable_schemes = self._find_suitable_loan_schemes(request, eligibility_score)
        
        return LoanEligibilityResponse(
            eligibility_score=float(eligibility_score),
            suitable_schemes=suitable_schemes,
            assessment_date=datetime.now().isoformat()
        )
    
    def _extract_business_features(self, request: BusinessAdvisoryRequest) -> np.ndarray:
        """Extract features from business data for AI analysis."""
        return self.feature_extractor.extract_business_features(request.dict())
    
    def _extract_financial_features(self, request: FinancialDataRequest) -> np.ndarray:
        """Extract features from financial data for ML models."""
        return self.feature_extractor.extract_financial_features(request.dict())
    
    def _extract_eligibility_features(self, request: LoanEligibilityRequest) -> np.ndarray:
        """Extract features for loan eligibility prediction."""
        return self.feature_extractor.extract_loan_eligibility_features(request.dict())
    
    def _generate_ai_recommendations(self, features: np.ndarray) -> List[BusinessRecommendation]:
        """Generate business recommendations using AI."""
        if not self.classifier:
            return self._generate_fallback_recommendations()
            
        # Define business aspects to analyze
        aspects = [
            "digital transformation",
            "operational efficiency",
            "market expansion",
            "financial management",
            "customer engagement"
        ]
        
        # Generate recommendations for each aspect
        recommendations = []
        for aspect in aspects:
            # Use zero-shot classification to determine relevance
            result = self.classifier(
                "Business needs improvement in " + aspect,
                candidate_labels=["high priority", "medium priority", "low priority"],
                hypothesis_template="This business needs {}.")
            
            # Generate recommendation based on priority
            if result["labels"][0] == "high priority":
                recommendation = self._generate_recommendation_for_aspect(aspect)
                recommendations.append(recommendation)
                
        return recommendations[:5]  # Return top 5 recommendations

    def _generate_recommendation_for_aspect(self, aspect: str) -> BusinessRecommendation:
        """Generate recommendation for a specific business aspect."""
        recommendations = {
            "digital transformation": BusinessRecommendation(
                title="Implement Digital Solutions",
                description="Adopt digital payment systems, create an online presence, and use digital inventory management",
                impact="high",
                implementation_difficulty="medium",
                estimated_cost="₹25,000 - ₹50,000",
                expected_roi="25-35% increase in efficiency"
            ),
            "operational efficiency": BusinessRecommendation(
                title="Streamline Operations",
                description="Implement inventory management system, optimize supply chain, and automate routine tasks",
                impact="high",
                implementation_difficulty="medium",
                estimated_cost="₹15,000 - ₹35,000",
                expected_roi="15-25% cost reduction"
            ),
            "market expansion": BusinessRecommendation(
                title="Expand Market Reach",
                description="Explore new market segments, establish online presence, and implement targeted marketing",
                impact="high",
                implementation_difficulty="medium",
                estimated_cost="₹30,000 - ₹60,000",
                expected_roi="20-30% revenue increase"
            ),
            "financial management": BusinessRecommendation(
                title="Improve Financial Health",
                description="Implement financial tracking tools, optimize cash flow, and explore financing options",
                impact="high",
                implementation_difficulty="medium",
                estimated_cost="₹10,000 - ₹25,000",
                expected_roi="15-25% better cash flow"
            ),
            "customer engagement": BusinessRecommendation(
                title="Enhance Customer Relations",
                description="Implement CRM system, start loyalty program, and improve customer service",
                impact="high",
                implementation_difficulty="low",
                estimated_cost="₹20,000 - ₹40,000",
                expected_roi="20-30% customer retention"
            )
        }
        
        return recommendations.get(aspect, BusinessRecommendation(
            title="General Improvement",
            description="Implement best practices and modern solutions",
            impact="medium",
            implementation_difficulty="medium",
            estimated_cost="₹15,000 - ₹30,000",
            expected_roi="10-20% improvement"
        ))
    
    def _analyze_growth_opportunities(self, features: np.ndarray) -> List[GrowthOpportunity]:
        """Analyze growth opportunities using AI."""
        opportunities = [
            GrowthOpportunity(
                title="Digital Transformation",
                description="Implement digital payment solutions and online presence",
                potential_impact="15-25% revenue increase",
                implementation_cost="₹10,000 - ₹50,000",
                timeframe="3-6 months"
            ),
            GrowthOpportunity(
                title="Market Expansion",
                description="Expand to neighboring markets and explore e-commerce",
                potential_impact="20-30% revenue increase",
                implementation_cost="₹50,000 - ₹1,00,000",
                timeframe="6-12 months"
            ),
            GrowthOpportunity(
                title="Product Diversification",
                description="Add complementary products or services",
                potential_impact="10-20% revenue increase",
                implementation_cost="₹25,000 - ₹75,000",
                timeframe="3-9 months"
            )
        ]
        return opportunities
    
    def _find_suitable_loan_schemes(self, request: LoanEligibilityRequest, eligibility_score: float) -> List[LoanScheme]:
        """Find suitable loan schemes based on eligibility."""
        suitable_schemes = []
        
        for scheme in self.loan_schemes:
            # Check eligibility criteria
            if eligibility_score >= 0.7:
                # High eligibility - include all schemes
                suitable_schemes.append(scheme)
            elif eligibility_score >= 0.5 and scheme.max_amount <= 500000:
                # Medium eligibility - include smaller loans
                suitable_schemes.append(scheme)
            elif eligibility_score >= 0.3 and scheme.max_amount <= 100000:
                # Low eligibility - include only micro loans
                suitable_schemes.append(scheme)
        
        return suitable_schemes
    
    def _load_loan_schemes(self) -> List[LoanScheme]:
        """Load available loan schemes."""
        return [
            LoanScheme(
                id="mudra_shishu",
                name="Mudra Shishu Loan",
                max_amount=50000,
                interest_rate=0.07,
                eligibility="Small businesses with annual turnover under ₹5 lakhs",
                subsidy="Interest subsidy of 2% for timely repayment"
            ),
            LoanScheme(
                id="mudra_kishor",
                name="Mudra Kishor Loan",
                max_amount=500000,
                interest_rate=0.10,
                eligibility="Small businesses with annual turnover between ₹5-25 lakhs",
                subsidy="No processing fee"
            ),
            LoanScheme(
                id="stand_up_india",
                name="Stand Up India Loan",
                max_amount=1000000,
                interest_rate=0.11,
                eligibility="SC/ST and women entrepreneurs",
                subsidy="Collateral-free loan up to 25% of project cost"
            ),
            LoanScheme(
                id="pmegp",
                name="PMEGP Scheme",
                max_amount=2500000,
                interest_rate=0.11,
                eligibility="New enterprises in manufacturing, service or trading",
                subsidy="15-35% subsidy on project cost"
            )
        ]