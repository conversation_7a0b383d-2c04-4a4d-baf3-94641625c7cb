import os
import torch
import json
from typing import Dict, List, Optional, Union
import logging
from pathlib import Path
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
from .enhanced_speech_model import EnhancedSpeechModel
from .enhanced_language_model import JharkhandLanguageModel

logger = logging.getLogger(__name__)

class EnhancedEducationModel:
    """
    Enhanced Education Model with AI-powered virtual tutors and offline learning support
    for tribal and rural students in Jharkhand.
    """
    
    def __init__(self, model_dir: str = "models/education", offline_mode: bool = False):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        self.offline_mode = offline_mode
        
        # Initialize components
        self.speech_model = EnhancedSpeechModel()
        self.language_model = JharkhandLanguageModel(offline_mode=offline_mode)
        
        # Load tutoring models and content
        self.tutoring_models = {}
        self.cached_content = {}
        self.skill_programs = {}
        
        self._initialize_models()
        self._load_cached_content()
    
    def _initialize_models(self):
        """Initialize AI tutoring models for different subjects and languages"""
        try:
            # Load base tutoring model
            if self.offline_mode:
                model_path = self.model_dir / "base_tutor"
                if not model_path.exists():
                    raise ValueError("Offline mode enabled but no base tutor model found")
            else:
                model_path = "microsoft/DialoGPT-medium"  # Base conversational model
            
            self.base_tutor = AutoModelForCausalLM.from_pretrained(model_path).to(self.device)
            self.tutor_tokenizer = AutoTokenizer.from_pretrained(model_path)
            
            # Initialize subject-specific models
            subjects = ["math", "science", "language", "skills"]
            for subject in subjects:
                self._load_subject_model(subject)
            
            logger.info("Successfully initialized tutoring models")
        
        except Exception as e:
            logger.error(f"Error initializing tutoring models: {str(e)}")
    
    def _load_subject_model(self, subject: str):
        """Load specialized model for a specific subject"""
        try:
            custom_path = self.model_dir / f"tutor_{subject}"
            if custom_path.exists():
                self.tutoring_models[subject] = AutoModelForCausalLM.from_pretrained(
                    str(custom_path)
                ).to(self.device)
            else:
                # Use base model with subject-specific prompts
                self.tutoring_models[subject] = self.base_tutor
        except Exception as e:
            logger.error(f"Error loading model for {subject}: {str(e)}")
    
    def _load_cached_content(self):
        """Load cached educational content for offline use"""
        try:
            cache_path = self.model_dir / "cached_content.json"
            if cache_path.exists():
                with open(cache_path, "r", encoding="utf-8") as f:
                    self.cached_content = json.load(f)
            else:
                # Initialize with basic content structure
                self.cached_content = {
                    "lessons": {},
                    "exercises": {},
                    "assessments": {},
                    "skill_programs": {}
                }
                self._save_cached_content()
        except Exception as e:
            logger.error(f"Error loading cached content: {str(e)}")
    
    def _save_cached_content(self):
        """Save cached content to disk"""
        try:
            cache_path = self.model_dir / "cached_content.json"
            with open(cache_path, "w", encoding="utf-8") as f:
                json.dump(self.cached_content, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error saving cached content: {str(e)}")
    
    def get_virtual_tutor(self, subject: str, language: str = "en") -> Dict:
        """Get an AI-powered virtual tutor for a specific subject and language"""
        try:
            if subject not in self.tutoring_models:
                return {"error": f"Tutor not available for subject: {subject}"}
            
            model = self.tutoring_models[subject]
            return {
                "model": model,
                "tokenizer": self.tutor_tokenizer,
                "subject": subject,
                "language": language
            }
        except Exception as e:
            return {"error": str(e)}
    
    def generate_study_material(self, subject: str, topic: str, language: str = "en") -> Dict:
        """Generate personalized study materials and practice exercises"""
        try:
            if not self.offline_mode:
                # Generate new content using AI
                generator = pipeline(
                    "text-generation",
                    model=self.tutoring_models[subject],
                    tokenizer=self.tutor_tokenizer,
                    device=0 if self.device == "cuda" else -1
                )
                
                content = generator(
                    f"Create study material for {subject} topic: {topic}",
                    max_length=1000,
                    num_return_sequences=1
                )[0]["generated_text"]
                
                # Translate if needed
                if language != "en":
                    content = self.language_model.translate(content, "en", language)
                
                # Cache the generated content
                if "study_materials" not in self.cached_content:
                    self.cached_content["study_materials"] = {}
                self.cached_content["study_materials"][f"{subject}_{topic}"] = {
                    "content": content,
                    "language": language
                }
                self._save_cached_content()
                
                return {"content": content, "language": language}
            else:
                # Return cached content if available
                cached = self.cached_content.get("study_materials", {})
                material = cached.get(f"{subject}_{topic}")
                if material and material["language"] == language:
                    return material
                return {"error": "Content not available offline"}
        
        except Exception as e:
            return {"error": str(e)}
    
    def generate_exam_materials(self, subject: str, exam_type: str, difficulty: str = "medium", language: str = "en") -> Dict:
        """Generate AI-powered exam preparation materials with adaptive difficulty"""
        try:
            if not self.offline_mode:
                # Generate exam content using AI
                generator = pipeline(
                    "text-generation",
                    model=self.tutoring_models[subject],
                    tokenizer=self.tutor_tokenizer,
                    device=0 if self.device == "cuda" else -1
                )
                
                # Generate questions and answers
                prompt = f"Create {difficulty} level {exam_type} questions for {subject}"
                content = generator(
                    prompt,
                    max_length=1500,
                    num_return_sequences=1
                )[0]["generated_text"]
                
                # Translate if needed
                if language != "en":
                    content = self.language_model.translate(content, "en", language)
                
                # Structure the content
                exam_material = {
                    "subject": subject,
                    "type": exam_type,
                    "difficulty": difficulty,
                    "content": content,
                    "language": language,
                    "practice_questions": self._generate_practice_questions(subject, difficulty),
                    "study_guide": self._generate_study_guide(subject, exam_type)
                }
                
                # Cache the content
                if "exam_materials" not in self.cached_content:
                    self.cached_content["exam_materials"] = {}
                self.cached_content["exam_materials"][f"{subject}_{exam_type}_{difficulty}"] = exam_material
                self._save_cached_content()
                
                return exam_material
            else:
                # Return cached content if available
                cached = self.cached_content.get("exam_materials", {})
                material = cached.get(f"{subject}_{exam_type}_{difficulty}")
                if material and material["language"] == language:
                    return material
                return {"error": "Exam materials not available offline"}
        
        except Exception as e:
            return {"error": str(e)}
    
    def _generate_practice_questions(self, subject: str, difficulty: str) -> List[Dict]:
        """Generate practice questions with varying formats"""
        try:
            generator = pipeline(
                "text-generation",
                model=self.tutoring_models[subject],
                tokenizer=self.tutor_tokenizer,
                device=0 if self.device == "cuda" else -1
            )
            
            questions = []
            formats = ["multiple_choice", "short_answer", "practical"]
            
            for format_type in formats:
                prompt = f"Create a {difficulty} {format_type} question for {subject}"
                response = generator(
                    prompt,
                    max_length=500,
                    num_return_sequences=1
                )[0]["generated_text"]
                
                questions.append({
                    "type": format_type,
                    "content": response
                })
            
            return questions
        except Exception as e:
            logger.error(f"Error generating practice questions: {str(e)}")
            return []
    
    def _generate_study_guide(self, subject: str, exam_type: str) -> Dict:
        """Generate a comprehensive study guide"""
        try:
            generator = pipeline(
                "text-generation",
                model=self.tutoring_models[subject],
                tokenizer=self.tutor_tokenizer,
                device=0 if self.device == "cuda" else -1
            )
            
            prompt = f"Create a study guide for {exam_type} exam in {subject}"
            content = generator(
                prompt,
                max_length=1000,
                num_return_sequences=1
            )[0]["generated_text"]
            
            return {
                "content": content,
                "key_topics": self._extract_key_topics(content),
                "resources": self._suggest_resources(subject)
            }
        except Exception as e:
            logger.error(f"Error generating study guide: {str(e)}")
            return {}
    
    def create_skill_program(self, skill_name: str, industry: str = None, difficulty: str = "beginner", language: str = "en") -> Dict:
        """Create a comprehensive skill development program with industry-specific content"""
        try:
            program_key = f"{skill_name}_{industry}_{difficulty}"
            
            if not self.offline_mode:
                # Generate program structure with industry context
                generator = pipeline(
                    "text-generation",
                    model=self.tutoring_models["skills"],
                    tokenizer=self.tutor_tokenizer,
                    device=0 if self.device == "cuda" else -1
                )
                
                # Generate industry-specific content
                industry_context = f" for {industry} industry" if industry else ""
                program = {
                    "name": skill_name,
                    "industry": industry,
                    "difficulty": difficulty,
                    "language": language,
                    "modules": self._generate_skill_modules(skill_name, industry, difficulty, generator)
                }
                
                # Translate content if needed
                if language != "en":
                    program = self._translate_program_content(program)
                
                # Cache the program
                self.cached_content["skill_programs"][program_key] = program
                self._save_cached_content()
                
                return program
            else:
                # Return cached program if available
                cached_program = self.cached_content["skill_programs"].get(program_key)
                if cached_program and cached_program.get("language") == language:
                    return cached_program
                return {"error": "Program not available offline"}
        
        except Exception as e:
            return {"error": str(e)}
    
    def _generate_skill_modules(self, skill_name: str, industry: str, difficulty: str, generator) -> List[Dict]:
        """Generate comprehensive skill development modules"""
        try:
            modules = []
            module_types = [
                ("Introduction", "Basic concepts and fundamentals"),
                ("Theory", "Theoretical knowledge and principles"),
                ("Practical", "Hands-on exercises and applications"),
                ("Industry Application", "Real-world industry applications"),
                ("Assessment", "Skill evaluation and certification")
            ]
            
            for module_type, description in module_types:
                industry_context = f" in {industry}" if industry else ""
                prompt = f"Create {difficulty} level {module_type} content for {skill_name}{industry_context}"
                
                content = generator(
                    prompt,
                    max_length=1000,
                    num_return_sequences=1
                )[0]["generated_text"]
                
                modules.append({
                    "title": f"{module_type}",
                    "description": description,
                    "content": content,
                    "exercises": self._generate_module_exercises(skill_name, module_type, difficulty, generator),
                    "resources": self._generate_module_resources(skill_name, module_type, industry)
                })
            
            return modules
        except Exception as e:
            logger.error(f"Error generating skill modules: {str(e)}")
            return []
    
    def _translate_program_content(self, program: Dict) -> Dict:
        """Translate all program content to target language"""
        try:
            translated = program.copy()
            target_lang = program["language"]
            
            for module in translated["modules"]:
                module["content"] = self.language_model.translate(
                    module["content"], "en", target_lang
                )
                module["description"] = self.language_model.translate(
                    module["description"], "en", target_lang
                )
                
                # Translate exercises
                for exercise in module["exercises"]:
                    exercise["question"] = self.language_model.translate(
                        exercise["question"], "en", target_lang
                    )
                    if "solution" in exercise:
                        exercise["solution"] = self.language_model.translate(
                            exercise["solution"], "en", target_lang
                        )
            
            return translated
        except Exception as e:
            logger.error(f"Error translating program content: {str(e)}")
            return program

    def voice_based_learning(self, audio_input: str, language: str = "en", subject: str = None) -> Dict:
        """Enhanced voice-based learning assistance with interactive dialogue and subject-specific support"""
        try:
            # Convert speech to text with improved accuracy for tribal languages
            text_input = self.speech_model.speech_to_text(audio_input, language)
            
            if "error" in text_input:
                return text_input
            
            # Determine the subject from input or use provided subject
            detected_subject = subject or self._detect_subject(text_input["text"])
            
            # Get appropriate tutor model
            response = self.get_virtual_tutor(detected_subject, language)
            if "error" in response:
                return response
            
            tutor = response["model"]
            tokenizer = response["tokenizer"]
            
            # Generate contextual response
            context = self._get_learning_context(text_input["text"], detected_subject)
            inputs = tokenizer(context, return_tensors="pt").to(self.device)
            outputs = tutor.generate(**inputs, max_length=200, temperature=0.7)
            response_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Add learning resources and next steps
            enriched_response = self._enrich_voice_response(response_text, detected_subject, language)
            
            # Convert response to natural speech with appropriate voice modulation
            audio_response = self.speech_model.text_to_speech(
                enriched_response["text"],
                language,
                voice_style="educational"
            )
            
            return {
                "input_text": text_input["text"],
                "detected_subject": detected_subject,
                "response_text": enriched_response["text"],
                "audio_response": audio_response,
                "next_steps": enriched_response["next_steps"],
                "resources": enriched_response["resources"]
            }
        
        except Exception as e:
            return {"error": str(e)}
    
    def _detect_subject(self, text: str) -> str:
        """Detect the subject area from user's voice input"""
        try:
            # Use base tutor model to classify subject
            inputs = self.tutor_tokenizer(f"Classify subject area: {text}", return_tensors="pt").to(self.device)
            outputs = self.base_tutor.generate(**inputs, max_length=50)
            subject = self.tutor_tokenizer.decode(outputs[0], skip_special_tokens=True).lower()
            
            # Map to available subjects
            available_subjects = ["math", "science", "language", "skills"]
            for s in available_subjects:
                if s in subject:
                    return s
            return "language"  # Default to language if no specific subject detected
        except Exception as e:
            logger.error(f"Error detecting subject: {str(e)}")
            return "language"
    
    def _get_learning_context(self, text: str, subject: str) -> str:
        """Generate learning context for voice interaction"""
        try:
            # Add subject-specific context
            context = f"As a {subject} tutor, help with: {text}"
            
            # Add previous interactions if available
            if "voice_history" in self.cached_content:
                recent_history = self.cached_content["voice_history"][-3:]  # Last 3 interactions
                for interaction in recent_history:
                    context += f"\nPrevious: {interaction}"
            
            return context
        except Exception as e:
            logger.error(f"Error getting learning context: {str(e)}")
            return text
    
    def _enrich_voice_response(self, response: str, subject: str, language: str) -> Dict:
        """Enrich voice response with additional learning resources"""
        try:
            # Structure the response with clear sections
            sections = {
                "text": response,
                "next_steps": [],
                "resources": []
            }
            
            # Generate next steps
            generator = pipeline(
                "text-generation",
                model=self.tutoring_models[subject],
                tokenizer=self.tutor_tokenizer,
                device=0 if self.device == "cuda" else -1
            )
            
            next_steps = generator(
                f"Suggest next learning steps for: {response}",
                max_length=100,
                num_return_sequences=1
            )[0]["generated_text"]
            
            # Add relevant resources
            resources = self.get_offline_resources(subject)
            
            sections["next_steps"] = next_steps.split("\n")
            sections["resources"] = resources
            
            return sections
        except Exception as e:
            logger.error(f"Error enriching voice response: {str(e)}")
            return {"text": response, "next_steps": [], "resources": []}

    def get_offline_resources(self, subject: str = None) -> Dict:
        """Get available offline learning resources"""
        try:
            resources = {
                "study_materials": [],
                "exercises": [],
                "skill_programs": []
            }
            
            # Filter by subject if specified
            for category, content in self.cached_content.items():
                if isinstance(content, dict):
                    for key, value in content.items():
                        if not subject or subject in key:
                            resources[category].append({
                                "key": key,
                                "summary": value.get("content", "")[:100] + "..."
                                if "content" in value else "No summary available"
                            })
            
            return resources
        
        except Exception as e:
            return {"error": str(e)}