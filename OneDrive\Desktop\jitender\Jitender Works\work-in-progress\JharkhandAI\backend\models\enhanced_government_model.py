from typing import List, Dict, Optional, Union
from datetime import datetime
import logging
from database import get_db
from models.government_policy_model import PolicyAnalytics
from models.tribal_nlu_model import TribalNLUModel
from fastapi import HTTPException

class EnhancedGovernmentAI:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.nlu_model = TribalNLUModel()
        
        # Enhanced scheme categories with detailed keywords
        self.scheme_categories = {
            'agriculture': {
                'keywords': ['farming', 'crop', 'irrigation', 'seeds', 'fertilizer', 'organic', 'soil', 'harvest'],
                'schemes': ['PM-KISAN', 'Soil Health Card', 'Crop Insurance']
            },
            'education': {
                'keywords': ['school', 'college', 'scholarship', 'student', 'education', 'skill', 'training'],
                'schemes': ['Post Matric Scholarship', 'Pre Matric Scholarship', 'Merit Scholarship']
            },
            'healthcare': {
                'keywords': ['medical', 'hospital', 'health', 'treatment', 'insurance', 'ambulance'],
                'schemes': ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>']
            },
            'employment': {
                'keywords': ['job', 'work', 'career', 'skill', 'training', 'self-employment'],
                'schemes': ['PMEGP', 'Skill India', 'MGNREGA']
            },
            'infrastructure': {
                'keywords': ['road', 'electricity', 'water', 'housing', 'construction', 'solar'],
                'schemes': ['PMAY', 'Jal Jeevan Mission', 'Rural Electrification']
            }
        }
        
        # Grievance categories for smart routing
        self.grievance_categories = {
            'public_services': ['water', 'electricity', 'road', 'sanitation'],
            'welfare_schemes': ['pension', 'scholarship', 'subsidy', 'ration'],
            'law_order': ['police', 'crime', 'security', 'protection'],
            'health_services': ['hospital', 'medicine', 'ambulance', 'treatment'],
            'education': ['school', 'teacher', 'mid-day meal', 'facilities']
        }

    async def process_query(self, query: str, language: str = 'en', context: Optional[Dict] = None) -> Dict:
        """Process user query with enhanced multilingual support"""
        try:
            # Translate query if not in English
            if language != 'en':
                query = await self.nlu_model.translate_to_english(query, source_lang=language)
            
            # Enhanced intent and entity analysis
            intent_data = self._analyze_enhanced_intent(query)
            relevant_schemes = self._find_relevant_schemes(query)
            entities = self._extract_entities(query)
            
            # Generate comprehensive response
            response = self._generate_enhanced_response(
                intent_data,
                relevant_schemes,
                entities,
                context
            )
            
            # Translate response if needed
            if language != 'en':
                response['message'] = await self.nlu_model.translate_from_english(
                    response['message'],
                    target_lang=language
                )
            
            # Store interaction with enhanced analytics
            await self._store_enhanced_interaction(query, intent_data, response, language)
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing government query: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    def _analyze_enhanced_intent(self, query: str) -> Dict:
        """Enhanced intent analysis with confidence scores and sub-intents"""
        query_lower = query.lower()
        intents = {
            'application_process': {
                'keywords': ['how', 'apply', 'process', 'procedure', 'steps'],
                'confidence': 0.0
            },
            'eligibility_check': {
                'keywords': ['eligible', 'qualify', 'criteria', 'requirements'],
                'confidence': 0.0
            },
            'status_check': {
                'keywords': ['status', 'track', 'application', 'progress'],
                'confidence': 0.0
            },
            'document_requirements': {
                'keywords': ['document', 'require', 'need', 'papers', 'proof'],
                'confidence': 0.0
            },
            'grievance_filing': {
                'keywords': ['complain', 'grievance', 'issue', 'problem', 'resolve'],
                'confidence': 0.0
            }
        }
        
        # Calculate confidence scores
        for intent, data in intents.items():
            matches = sum(1 for keyword in data['keywords'] if keyword in query_lower)
            data['confidence'] = matches / len(data['keywords']) if matches > 0 else 0.0
        
        # Get primary and secondary intents
        sorted_intents = sorted(intents.items(), key=lambda x: x[1]['confidence'], reverse=True)
        primary_intent = sorted_intents[0][0]
        secondary_intents = [intent[0] for intent in sorted_intents[1:3] if intent[1]['confidence'] > 0.3]
        
        return {
            'primary_intent': primary_intent,
            'secondary_intents': secondary_intents,
            'confidence_scores': {k: v['confidence'] for k, v in intents.items()}
        }

    def _find_relevant_schemes(self, query: str) -> List[Dict]:
        """Enhanced scheme matching with relevance scores"""
        query_lower = query.lower()
        relevant_schemes = []
        
        for category, data in self.scheme_categories.items():
            # Calculate category relevance
            keyword_matches = sum(1 for keyword in data['keywords'] if keyword in query_lower)
            if keyword_matches > 0:
                relevance_score = keyword_matches / len(data['keywords'])
                relevant_schemes.append({
                    'category': category,
                    'schemes': data['schemes'],
                    'relevance_score': relevance_score
                })
        
        return sorted(relevant_schemes, key=lambda x: x['relevance_score'], reverse=True)

    def _extract_entities(self, query: str) -> Dict:
        """Extract relevant entities from query"""
        entities = {
            'locations': [],
            'dates': [],
            'document_types': [],
            'scheme_names': []
        }
        # Implement entity extraction logic
        return entities

    def _generate_enhanced_response(self, intent_data: Dict, schemes: List[Dict], 
                                  entities: Dict, context: Optional[Dict]) -> Dict:
        """Generate detailed response with actionable items"""
        primary_intent = intent_data['primary_intent']
        response = {
            'intent_analysis': intent_data,
            'relevant_schemes': schemes,
            'entities': entities,
            'message': '',
            'actions': [],
            'required_documents': [],
            'next_steps': []
        }
        
        if primary_intent == 'application_process':
            response.update(self._get_enhanced_application_process(schemes))
        elif primary_intent == 'eligibility_check':
            response.update(self._get_enhanced_eligibility_criteria(schemes))
        elif primary_intent == 'status_check':
            response.update(self._get_enhanced_status_info(context))
        elif primary_intent == 'grievance_filing':
            response.update(self._process_grievance(entities))
        else:
            response.update(self._get_enhanced_general_info(schemes))
        
        return response

    def _get_enhanced_application_process(self, schemes: List[Dict]) -> Dict:
        """Get detailed application process with step-by-step guide"""
        if not schemes:
            return {
                'message': "Please specify which scheme you're interested in.",
                'actions': ['view_all_schemes', 'contact_helpdesk']
            }
        
        scheme = schemes[0]  # Most relevant scheme
        return {
            'message': f"Application process for {scheme['category']} schemes:",
            'actions': ['download_application_form', 'view_tutorial', 'schedule_assistance'],
            'required_documents': ['identity_proof', 'address_proof', 'income_certificate'],
            'next_steps': [
                'Fill online application form',
                'Upload required documents',
                'Submit application',
                'Track application status'
            ]
        }

    def _process_grievance(self, entities: Dict) -> Dict:
        """Process grievance with smart routing and tracking"""
        return {
            'message': "Your grievance has been registered.",
            'actions': ['track_grievance', 'upload_evidence', 'contact_officer'],
            'tracking_id': f"GR{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'estimated_resolution_time': "48 hours"
        }

    async def _store_enhanced_interaction(self, query: str, intent_data: Dict, 
                                        response: Dict, language: str) -> None:
        """Store detailed interaction data for analytics"""
        try:
            db = next(get_db())
            analytics = PolicyAnalytics(
                policy_area='government_schemes',
                metrics={
                    'query': query,
                    'language': language,
                    'primary_intent': intent_data['primary_intent'],
                    'confidence_scores': intent_data['confidence_scores'],
                    'timestamp': datetime.now().isoformat()
                },
                insights={
                    'relevant_schemes': response.get('relevant_schemes', []),
                    'suggested_actions': response.get('actions', []),
                    'entities_extracted': response.get('entities', {})
                }
            )
            db.add(analytics)
            await db.commit()
        except Exception as e:
            self.logger.error(f"Error storing interaction: {e}")

    async def process_land_record_query(self, query: str, language: str = 'en', context: Optional[Dict] = None) -> Dict:
        """Process land record related queries"""
        try:
            # Extract land record specific entities
            entities = self._extract_entities(query)
            intent_data = self._analyze_enhanced_intent(query)
            
            response = {
                'message': '',
                'actions': [],
                'required_documents': [],
                'next_steps': []
            }
            
            # Handle different land record intents
            if 'view' in query.lower():
                response.update({
                    'message': 'To view your land records, please provide:',
                    'required_documents': ['Khata number', 'Aadhaar card', 'Property documents'],
                    'actions': ['View land records', 'Download copy', 'Verify ownership'],
                    'next_steps': ['Submit required documents', 'Pay applicable fees', 'Receive verified records']
                })
            elif 'mutation' in query.lower():
                response.update({
                    'message': 'For land mutation process:',
                    'required_documents': ['Sale deed', 'Current land records', 'ID proof', 'Tax receipts'],
                    'actions': ['Start mutation', 'Track application', 'Schedule inspection'],
                    'next_steps': ['Submit mutation request', 'Pay mutation fees', 'Attend field verification']
                })
            else:
                response['message'] = 'How can I help you with your land records?'
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing land record query: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def verify_bpl_status(self, data: Dict) -> Dict:
        """Verify BPL card status and eligibility"""
        try:
            # Verify BPL status and eligibility
            response = {
                'status': 'success',
                'is_eligible': self._check_bpl_eligibility(data),
                'required_documents': [
                    'Income certificate',
                    'Residence proof',
                    'Aadhaar card',
                    'Family details'
                ],
                'next_steps': [
                    'Submit application with documents',
                    'Attend verification visit',
                    'Receive BPL card'
                ],
                'message': 'Please ensure all documents are valid and current.'
            }
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error verifying BPL status: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def process_pension_query(self, query: str, language: str = 'en', context: Optional[Dict] = None) -> Dict:
        """Process pension related queries"""
        try:
            intent_data = self._analyze_enhanced_intent(query)
            entities = self._extract_entities(query)
            
            response = {
                'message': '',
                'pension_types': [
                    'Old Age Pension',
                    'Widow Pension',
                    'Disability Pension'
                ],
                'actions': [],
                'required_documents': [],
                'next_steps': []
            }
            
            if 'apply' in query.lower():
                response.update({
                    'message': 'To apply for pension:',
                    'required_documents': ['Age proof', 'Identity proof', 'Income certificate', 'Bank details'],
                    'actions': ['Start application', 'Check eligibility', 'Track status'],
                    'next_steps': ['Submit application', 'Verify documents', 'Attend interview']
                })
            elif 'status' in query.lower():
                response.update({
                    'message': 'To check pension status:',
                    'required_documents': ['Application ID', 'Aadhaar number'],
                    'actions': ['Check status', 'Update details', 'File grievance'],
                    'next_steps': ['Enter application details', 'View status']
                })
            else:
                response['message'] = 'How can I assist you with pension services?'
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing pension query: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    def _check_bpl_eligibility(self, data: Dict) -> bool:
        """Check BPL eligibility based on provided data"""
        try:
            # Implement eligibility criteria checking
            # This is a placeholder implementation
            annual_income = data.get('annual_income', 0)
            family_size = data.get('family_size', 0)
            assets = data.get('assets', [])
            
            # Basic eligibility checks
            if annual_income > 27000:  # Example threshold
                return False
            
            if len(assets) > 3:  # Example asset limit
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking BPL eligibility: {e}")
            return False