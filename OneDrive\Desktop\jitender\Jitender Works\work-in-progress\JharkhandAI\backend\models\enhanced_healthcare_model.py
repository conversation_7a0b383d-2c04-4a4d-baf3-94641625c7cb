import os
import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Union
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from transformers import pipeline
import logging

logger = logging.getLogger(__name__)

class EnhancedHealthcareModel:
    """
    Enhanced Healthcare Model with AI-based disease prediction,
    telemedicine support, health report generation, and nutrition guidance
    """
    
    def __init__(self):
        # Initialize disease prediction components
        self.symptom_checker_model = None
        self.symptom_encoder = None
        self.disease_encoder = None
        self.symptoms_list = []
        self.diseases_list = []
        self.disease_descriptions = {}
        self.disease_precautions = {}
        
        # Initialize NLP components for health reports
        self.nlp_generator = pipeline('text-generation', model='gpt2')
        
        # Initialize nutrition recommendation system
        self.nutrition_guidelines = self._load_nutrition_guidelines()
        self.maternal_health_guidelines = self._load_maternal_health_guidelines()
        
        # Initialize telemedicine components
        self.healthcare_facilities = self._load_healthcare_facilities()
        
        # Load all models and data
        self._load_or_initialize_model()
    
    def _load_or_initialize_model(self):
        """Load pre-trained models and initialize components"""
        model_dir = os.path.join(os.path.dirname(__file__), '../data')
        os.makedirs(model_dir, exist_ok=True)
        
        # Load disease data and initialize symptom checker
        self._load_disease_data()
        self._initialize_symptom_checker()
    
    def _load_nutrition_guidelines(self) -> Dict:
        """Load nutrition guidelines and recommendations"""
        return {
            "general": {
                "daily_requirements": {
                    "calories": "2000-2500 kcal",
                    "protein": "50-60g",
                    "carbohydrates": "275-300g",
                    "fats": "50-70g"
                },
                "recommendations": [
                    "Include variety of fruits and vegetables",
                    "Choose whole grains over refined grains",
                    "Include lean proteins",
                    "Limit processed foods and sugars"
                ]
            },
            "diabetic": {
                "daily_requirements": {
                    "calories": "1800-2200 kcal",
                    "carbohydrates": "200-225g",
                    "protein": "60-70g",
                    "fats": "40-60g"
                },
                "recommendations": [
                    "Monitor carbohydrate intake",
                    "Choose low glycemic index foods",
                    "Eat at regular intervals",
                    "Include fiber-rich foods"
                ]
            },
            "hypertension": {
                "daily_requirements": {
                    "calories": "1800-2200 kcal",
                    "sodium": "<2300mg",
                    "potassium": "3500-4700mg"
                },
                "recommendations": [
                    "Reduce salt intake",
                    "Increase potassium-rich foods",
                    "Limit alcohol consumption",
                    "Include DASH diet principles"
                ]
            }
        }
    
    def _load_maternal_health_guidelines(self) -> Dict:
        """Load maternal health guidelines and recommendations"""
        return {
            "first_trimester": {
                "nutrition": {
                    "calories_extra": "300 kcal",
                    "key_nutrients": [
                        "Folic acid",
                        "Iron",
                        "Calcium",
                        "Vitamin D"
                    ],
                    "recommendations": [
                        "Take prenatal vitamins",
                        "Eat small, frequent meals",
                        "Stay hydrated",
                        "Avoid raw foods and unpasteurized dairy"
                    ]
                },
                "health_checks": [
                    "Regular prenatal visits",
                    "Blood pressure monitoring",
                    "Weight tracking",
                    "Ultrasound scanning"
                ]
            },
            "second_trimester": {
                "nutrition": {
                    "calories_extra": "300-350 kcal",
                    "key_nutrients": [
                        "Protein",
                        "Omega-3 fatty acids",
                        "Calcium",
                        "Iron"
                    ],
                    "recommendations": [
                        "Increase protein intake",
                        "Continue prenatal vitamins",
                        "Include omega-3 rich foods",
                        "Maintain balanced diet"
                    ]
                },
                "health_checks": [
                    "Regular prenatal visits",
                    "Glucose screening",
                    "Ultrasound for anomalies",
                    "Blood pressure monitoring"
                ]
            },
            "third_trimester": {
                "nutrition": {
                    "calories_extra": "400-450 kcal",
                    "key_nutrients": [
                        "Protein",
                        "Iron",
                        "Calcium",
                        "Vitamin D"
                    ],
                    "recommendations": [
                        "Increase caloric intake",
                        "Focus on nutrient-dense foods",
                        "Stay well hydrated",
                        "Eat small, frequent meals"
                    ]
                },
                "health_checks": [
                    "Weekly prenatal visits",
                    "Group B strep testing",
                    "Fetal position check",
                    "Birth preparation"
                ]
            }
        }
    
    def _load_healthcare_facilities(self) -> Dict:
        """Load information about local healthcare facilities"""
        return {
            "hospitals": [
                {
                    "name": "Rajendra Institute of Medical Sciences",
                    "location": "Ranchi",
                    "specialties": ["General Medicine", "Surgery", "Obstetrics", "Pediatrics"],
                    "telemedicine": True
                },
                {
                    "name": "Medanta Hospital",
                    "location": "Ranchi",
                    "specialties": ["Cardiology", "Neurology", "Oncology"],
                    "telemedicine": True
                }
            ],
            "primary_health_centers": [
                {
                    "name": "Urban PHC Doranda",
                    "location": "Doranda, Ranchi",
                    "services": ["General Medicine", "Maternal Care", "Immunization"],
                    "telemedicine": True
                }
            ],
            "specialized_clinics": [
                {
                    "name": "Diabetes Care Center",
                    "location": "Ranchi",
                    "specialties": ["Diabetes Management", "Endocrinology"],
                    "telemedicine": True
                }
            ]
        }
    
    def predict_disease(self, symptoms: List[str]) -> Dict:
        """Predict possible diseases based on symptoms"""
        if not self.symptom_checker_model:
            return {"error": "Model not initialized"}
        
        try:
            # Encode symptoms
            symptom_vector = np.zeros(len(self.symptoms_list))
            for symptom in symptoms:
                if symptom in self.symptoms_list:
                    idx = self.symptom_encoder.transform([symptom])[0]
                    symptom_vector[idx] = 1
            
            # Get predictions
            prediction = self.symptom_checker_model.predict_proba([symptom_vector])[0]
            
            # Get top 3 predictions
            top_indices = prediction.argsort()[-3:][::-1]
            
            results = []
            for idx in top_indices:
                disease = self.disease_encoder.inverse_transform([idx])[0]
                probability = prediction[idx]
                
                results.append({
                    "disease": disease,
                    "probability": float(probability),
                    "description": self.disease_descriptions.get(disease, ""),
                    "precautions": self.disease_precautions.get(disease, [])
                })
            
            return {
                "predictions": results,
                "disclaimer": "This is an AI-based prediction and should not replace professional medical diagnosis."
            }
        
        except Exception as e:
            logger.error(f"Error in disease prediction: {str(e)}")
            return {"error": str(e)}
    
    def generate_health_report(self, patient_data: Dict) -> Dict:
        """Generate comprehensive health report with AI analysis"""
        try:
            # Extract patient information
            vitals = patient_data.get('vitals', {})
            symptoms = patient_data.get('symptoms', [])
            medical_history = patient_data.get('medical_history', {})
            
            # Get disease predictions
            predictions = self.predict_disease(symptoms)
            
            # Generate report sections
            report = {
                "timestamp": datetime.now().isoformat(),
                "patient_info": {
                    "vitals": vitals,
                    "symptoms": symptoms,
                    "medical_history": medical_history
                },
                "ai_analysis": {
                    "disease_predictions": predictions.get('predictions', []),
                    "risk_factors": self._analyze_risk_factors(patient_data),
                    "recommendations": self._generate_recommendations(patient_data)
                },
                "nutrition_guidance": self._get_nutrition_guidance(patient_data),
                "follow_up": self._recommend_follow_up(predictions)
            }
            
            return report
        
        except Exception as e:
            logger.error(f"Error generating health report: {str(e)}")
            return {"error": str(e)}
    
    def get_nutrition_guidance(self, patient_data: Dict) -> Dict:
        """Get personalized nutrition and health guidance"""
        try:
            medical_conditions = patient_data.get('medical_conditions', [])
            is_pregnant = patient_data.get('is_pregnant', False)
            trimester = patient_data.get('trimester', None)
            
            guidance = {
                "general_guidelines": self.nutrition_guidelines['general'],
                "specific_recommendations": []
            }
            
            # Add condition-specific guidelines
            for condition in medical_conditions:
                if condition.lower() in self.nutrition_guidelines:
                    guidance['specific_recommendations'].append({
                        "condition": condition,
                        "guidelines": self.nutrition_guidelines[condition.lower()]
                    })
            
            # Add maternal health guidelines if applicable
            if is_pregnant and trimester:
                guidance['maternal_health'] = self.maternal_health_guidelines.get(f"{trimester}_trimester", {})
            
            return guidance
        
        except Exception as e:
            logger.error(f"Error getting nutrition guidance: {str(e)}")
            return {"error": str(e)}
    
    def find_healthcare_facilities(self, 
                                  location: str, 
                                  specialty: Optional[str] = None, 
                                  telemedicine_only: bool = False) -> Dict:
        """Find suitable healthcare facilities based on criteria"""
        try:
            matching_facilities = []
            
            # Search through all facility types
            for facility_type, facilities in self.healthcare_facilities.items():
                for facility in facilities:
                    # Check location match
                    if location.lower() in facility['location'].lower():
                        # Check specialty if specified
                        if specialty and specialty not in facility.get('specialties', []):
                            continue
                        
                        # Check telemedicine if required
                        if telemedicine_only and not facility.get('telemedicine', False):
                            continue
                        
                        matching_facilities.append({
                            **facility,
                            "facility_type": facility_type
                        })
            
            return {
                "facilities": matching_facilities,
                "total_count": len(matching_facilities)
            }
        
        except Exception as e:
            logger.error(f"Error finding healthcare facilities: {str(e)}")
            return {"error": str(e)}
    
    def _analyze_risk_factors(self, patient_data: Dict) -> List[Dict]:
        """Analyze patient risk factors based on data"""
        risk_factors = []
        
        # Analyze vitals
        vitals = patient_data.get('vitals', {})
        if vitals.get('blood_pressure_systolic', 0) > 140 or vitals.get('blood_pressure_diastolic', 0) > 90:
            risk_factors.append({
                "factor": "High Blood Pressure",
                "severity": "moderate",
                "recommendation": "Regular blood pressure monitoring and lifestyle modifications recommended"
            })
        
        # Analyze BMI
        if 'weight' in vitals and 'height' in vitals:
            bmi = vitals['weight'] / (vitals['height'] ** 2)
            if bmi > 30:
                risk_factors.append({
                    "factor": "Obesity",
                    "severity": "high",
                    "recommendation": "Weight management program and regular exercise recommended"
                })
        
        return risk_factors
    
    def _generate_recommendations(self, patient_data: Dict) -> List[str]:
        """Generate personalized health recommendations"""
        recommendations = [
            "Schedule regular health check-ups",
            "Maintain a balanced diet",
            "Exercise regularly",
            "Get adequate sleep"
        ]
        
        # Add condition-specific recommendations
        medical_conditions = patient_data.get('medical_conditions', [])
        for condition in medical_conditions:
            if condition.lower() in self.disease_precautions:
                recommendations.extend(self.disease_precautions[condition.lower()])
        
        return recommendations
    
    def _recommend_follow_up(self, predictions: Dict) -> Dict:
        """Recommend follow-up actions based on predictions"""
        highest_prob = 0
        urgent_conditions = ["malaria", "dengue", "pneumonia"]
        
        for pred in predictions.get('predictions', []):
            if pred['probability'] > highest_prob:
                highest_prob = pred['probability']
                condition = pred['disease']
        
        if highest_prob > 0.7 and condition in urgent_conditions:
            return {
                "urgency": "high",
                "recommendation": "Immediate medical attention recommended",
                "timeframe": "Within 24 hours"
            }
        elif highest_prob > 0.5:
            return {
                "urgency": "moderate",
                "recommendation": "Schedule a medical consultation",
                "timeframe": "Within 3-5 days"
            }
        else:
            return {
                "urgency": "low",
                "recommendation": "Monitor symptoms and schedule routine check-up",
                "timeframe": "Within 2 weeks"
            }