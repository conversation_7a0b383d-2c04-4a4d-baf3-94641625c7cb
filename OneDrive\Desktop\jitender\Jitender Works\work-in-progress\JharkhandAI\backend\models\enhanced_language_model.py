"""
Enhanced Language Model for Jharkhand-specific data.
This module extends the basic language model with fine-tuning capabilities
for Jharkhand-specific languages and cultural context.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Union, Any
from pathlib import Path
import torch
from transformers import (
    AutoModelForCausalLM, 
    AutoTokenizer, 
    AutoModelForSeq2SeqLM,
    TrainingArguments, 
    Trainer, 
    DataCollatorForLanguageModeling
)
from datasets import Dataset, load_dataset
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class JharkhandLanguageModel:
    """
    Enhanced language model with fine-tuning capabilities for Jharkhand-specific data.
    Supports multiple tribal languages including Santhali, Ho, Mundari, Kurukh, and Kharia.
    """
    
    SUPPORTED_LANGUAGES = {
        'en': 'English',
        'hi': 'Hindi',
        'sa': '<PERSON><PERSON>',
        'ho': 'Ho',
        'mu': '<PERSON><PERSON><PERSON>',
        'ku': 'Kurukh',
        'kh': 'Kharia'
    }
    
    def __init__(
        self, 
        model_name: str = "google/mt5-small", 
        device: str = None,
        offline_mode: bool = False,
        model_dir: str = "models/language"
    ):
        """
        Initialize the Jharkhand Language Model.
        
        Args:
            model_name: Base model to use (default: google/mt5-small for multilingual support)
            device: Device to run the model on ('cpu', 'cuda', or None for auto-detection)
            offline_mode: Whether to use offline models only
            model_dir: Directory to store and load fine-tuned models
        """
        self.model_name = model_name
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        self.offline_mode = offline_mode
        
        # Auto-detect device if not specified
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
            
        logger.info(f"Initializing JharkhandLanguageModel with {model_name} on {self.device}")
        
        # Load base model and tokenizer
        try:
            if offline_mode:
                # Load from local directory if in offline mode
                local_model_path = self.model_dir / "base_model"
                if local_model_path.exists():
                    self.tokenizer = AutoTokenizer.from_pretrained(str(local_model_path))
                    self.model = AutoModelForSeq2SeqLM.from_pretrained(
                        str(local_model_path),
                        device_map=self.device if self.device == "cuda" else None
                    )
                else:
                    raise ValueError(f"Offline mode enabled but no model found at {local_model_path}")
            else:
                # Load from Hugging Face
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                self.model = AutoModelForSeq2SeqLM.from_pretrained(
                    model_name,
                    device_map=self.device if self.device == "cuda" else None
                )
                
                # Save the base model for offline use
                self.tokenizer.save_pretrained(str(self.model_dir / "base_model"))
                self.model.save_pretrained(str(self.model_dir / "base_model"))
                
            logger.info(f"Successfully loaded model and tokenizer")
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise
            
        # Dictionary to track fine-tuned models for specific languages
        self.fine_tuned_models = {}
        self.load_fine_tuned_models()
    
    def load_fine_tuned_models(self):
        """Load all available fine-tuned models from the model directory."""
        for lang_code in self.SUPPORTED_LANGUAGES:
            model_path = self.model_dir / f"fine_tuned_{lang_code}"
            if model_path.exists():
                try:
                    model = AutoModelForSeq2SeqLM.from_pretrained(
                        str(model_path),
                        device_map=self.device if self.device == "cuda" else None
                    )
                    self.fine_tuned_models[lang_code] = model
                    logger.info(f"Loaded fine-tuned model for {self.SUPPORTED_LANGUAGES[lang_code]}")
                except Exception as e:
                    logger.error(f"Error loading fine-tuned model for {lang_code}: {str(e)}")
    
    def translate(
        self, 
        text: str, 
        source_lang: str, 
        target_lang: str,
        use_fine_tuned: bool = True
    ) -> str:
        """
        Translate text from source language to target language.
        
        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code
            use_fine_tuned: Whether to use fine-tuned models if available
            
        Returns:
            Translated text
        """
        if source_lang not in self.SUPPORTED_LANGUAGES:
            raise ValueError(f"Source language {source_lang} not supported")
        if target_lang not in self.SUPPORTED_LANGUAGES:
            raise ValueError(f"Target language {target_lang} not supported")
            
        # If source and target are the same, return the original text
        if source_lang == target_lang:
            return text
            
        # Prepare input for the model
        input_text = f"translate {source_lang} to {target_lang}: {text}"
        inputs = self.tokenizer(input_text, return_tensors="pt").to(self.device)
        
        # Use fine-tuned model if available and requested
        if use_fine_tuned and target_lang in self.fine_tuned_models:
            model = self.fine_tuned_models[target_lang]
            logger.info(f"Using fine-tuned model for {self.SUPPORTED_LANGUAGES[target_lang]}")
        else:
            model = self.model
            logger.info("Using base model for translation")
        
        # Generate translation
        outputs = model.generate(
            **inputs,
            max_length=512,
            num_beams=4,
            early_stopping=True
        )
        
        # Decode and return the translation
        translation = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        return translation
    
    def fine_tune(
        self, 
        dataset_path: str, 
        language_code: str,
        output_dir: Optional[str] = None,
        num_train_epochs: int = 3,
        batch_size: int = 8,
        learning_rate: float = 5e-5
    ) -> None:
        """
        Fine-tune the model on Jharkhand-specific data for a particular language.
        
        Args:
            dataset_path: Path to the dataset (CSV or JSON)
            language_code: Language code to fine-tune for
            output_dir: Directory to save the fine-tuned model
            num_train_epochs: Number of training epochs
            batch_size: Training batch size
            learning_rate: Learning rate for training
        """
        if language_code not in self.SUPPORTED_LANGUAGES:
            raise ValueError(f"Language {language_code} not supported")
            
        logger.info(f"Fine-tuning model for {self.SUPPORTED_LANGUAGES[language_code]}")
        
        # Set output directory
        if output_dir is None:
            output_dir = str(self.model_dir / f"fine_tuned_{language_code}")
            
        # Load dataset
        try:
            if dataset_path.endswith('.csv'):
                dataset = load_dataset('csv', data_files=dataset_path)
            elif dataset_path.endswith('.json'):
                dataset = load_dataset('json', data_files=dataset_path)
            else:
                raise ValueError("Dataset must be in CSV or JSON format")
                
            # Ensure dataset has 'text' column
            if 'text' not in dataset['train'].column_names:
                raise ValueError("Dataset must have a 'text' column")
                
            logger.info(f"Loaded dataset with {len(dataset['train'])} examples")
        except Exception as e:
            logger.error(f"Error loading dataset: {str(e)}")
            raise
            
        # Tokenize dataset
        def tokenize_function(examples):
            return self.tokenizer(examples['text'], padding='max_length', truncation=True)
            
        tokenized_dataset = dataset.map(tokenize_function, batched=True)
        
        # Set up training arguments
        training_args = TrainingArguments(
            output_dir=output_dir,
            overwrite_output_dir=True,
            num_train_epochs=num_train_epochs,
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            learning_rate=learning_rate,
            weight_decay=0.01,
            save_total_limit=1,
            save_steps=500,
            logging_dir=f"{output_dir}/logs",
        )
        
        # Set up data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=True,
            mlm_probability=0.15
        )
        
        # Set up trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=tokenized_dataset['train'],
            data_collator=data_collator,
        )
        
        # Train the model
        trainer.train()
        
        # Save the fine-tuned model
        trainer.save_model(output_dir)
        self.tokenizer.save_pretrained(output_dir)
        
        # Load the fine-tuned model
        fine_tuned_model = AutoModelForSeq2SeqLM.from_pretrained(
            output_dir,
            device_map=self.device if self.device == "cuda" else None
        )
        self.fine_tuned_models[language_code] = fine_tuned_model
        
        logger.info(f"Fine-tuning complete. Model saved to {output_dir}")
    
    def generate_text(
        self, 
        prompt: str, 
        language_code: str = 'en',
        max_length: int = 100,
        use_fine_tuned: bool = True
    ) -> str:
        """
        Generate text based on a prompt in the specified language.
        
        Args:
            prompt: Text prompt to generate from
            language_code: Language code to generate in
            max_length: Maximum length of generated text
            use_fine_tuned: Whether to use fine-tuned models if available
            
        Returns:
            Generated text
        """
        if language_code not in self.SUPPORTED_LANGUAGES:
            raise ValueError(f"Language {language_code} not supported")
            
        # Prepare input for the model
        input_text = f"generate in {language_code}: {prompt}"
        inputs = self.tokenizer(input_text, return_tensors="pt").to(self.device)
        
        # Use fine-tuned model if available and requested
        if use_fine_tuned and language_code in self.fine_tuned_models:
            model = self.fine_tuned_models[language_code]
            logger.info(f"Using fine-tuned model for {self.SUPPORTED_LANGUAGES[language_code]}")
        else:
            model = self.model
            logger.info("Using base model for text generation")
        
        # Generate text
        outputs = model.generate(
            **inputs,
            max_length=max_length,
            num_beams=5,
            temperature=0.7,
            top_p=0.9,
            do_sample=True,
            early_stopping=True
        )
        
        # Decode and return the generated text
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        return generated_text
    
    def save_model_metadata(self):
        """Save metadata about available models and their capabilities."""
        metadata = {
            "base_model": self.model_name,
            "supported_languages": self.SUPPORTED_LANGUAGES,
            "fine_tuned_models": list(self.fine_tuned_models.keys()),
            "device": self.device,
            "offline_mode": self.offline_mode
        }
        
        with open(self.model_dir / "model_metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)
            
        logger.info(f"Saved model metadata to {self.model_dir / 'model_metadata.json'}")
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Return a dictionary of supported languages."""
        return self.SUPPORTED_LANGUAGES
    
    def get_fine_tuned_languages(self) -> List[str]:
        """Return a list of languages with fine-tuned models."""
        return list(self.fine_tuned_models.keys())
