import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.preprocessing import StandardScaler
from geopy.geocoders import Nominatim
from geopy.distance import geodesic
import joblib
import logging
from pathlib import Path

from .law_security_model import (
    CrimeReport, TrafficIncident, LegalAssistance,
    CrimeHotspot, CrimeType, CaseStatus
)

logger = logging.getLogger(__name__)

class EnhancedLawSecurityModel:
    """
    Enhanced Law and Security Model with AI-driven capabilities for:
    - Crime analysis and prediction
    - Smart traffic management
    - Legal assistance for land disputes
    - Predictive policing
    """
    
    def __init__(self, model_dir: str = "models/law_security"):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize models
        self.crime_prediction_model = None
        self.traffic_analysis_model = None
        self.legal_assistance_model = None
        
        # Load or train models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize or load pre-trained models"""
        try:
            # Crime prediction model
            crime_model_path = self.model_dir / "crime_prediction_model.joblib"
            if crime_model_path.exists():
                self.crime_prediction_model = joblib.load(crime_model_path)
            else:
                self.crime_prediction_model = RandomForestClassifier()
                logger.info("Initialized new crime prediction model")
            
            # Traffic analysis model
            traffic_model_path = self.model_dir / "traffic_analysis_model.joblib"
            if traffic_model_path.exists():
                self.traffic_analysis_model = joblib.load(traffic_model_path)
            else:
                self.traffic_analysis_model = IsolationForest()
                logger.info("Initialized new traffic analysis model")
            
            # Legal assistance model (for land dispute resolution)
            legal_model_path = self.model_dir / "legal_assistance_model.joblib"
            if legal_model_path.exists():
                self.legal_assistance_model = joblib.load(legal_model_path)
            else:
                self.legal_assistance_model = RandomForestClassifier()
                logger.info("Initialized new legal assistance model")
                
        except Exception as e:
            logger.error(f"Error initializing models: {str(e)}")
    
    def analyze_crime_patterns(self, district: str, time_period: int = 30) -> Dict:
        """Analyze crime patterns and predict potential hotspots"""
        try:
            # Get historical crime data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=time_period)
            
            # Analyze patterns using the crime prediction model
            crime_data = self._get_crime_data(district, start_date, end_date)
            if not crime_data.empty:
                predictions = self.crime_prediction_model.predict_proba(crime_data)
                
                # Identify hotspots
                hotspots = self._identify_hotspots(crime_data, predictions)
                
                return {
                    "status": "success",
                    "hotspots": hotspots,
                    "risk_levels": self._calculate_risk_levels(predictions),
                    "recommendations": self._generate_security_recommendations(hotspots)
                }
            
            return {"status": "error", "message": "No crime data available for analysis"}
            
        except Exception as e:
            logger.error(f"Error analyzing crime patterns: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def manage_traffic_incidents(self, incident_data: Dict) -> Dict:
        """Smart traffic management and incident analysis"""
        try:
            # Process incident data
            processed_data = self._preprocess_traffic_data(incident_data)
            
            # Analyze incident severity and impact
            severity_score = self.traffic_analysis_model.predict([processed_data])[0]
            
            # Generate response recommendations
            recommendations = self._generate_traffic_recommendations(severity_score, incident_data)
            
            return {
                "status": "success",
                "severity_score": severity_score,
                "impact_zone": self._calculate_impact_zone(incident_data),
                "recommendations": recommendations
            }
            
        except Exception as e:
            logger.error(f"Error managing traffic incident: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def provide_legal_assistance(self, case_data: Dict) -> Dict:
        """AI-powered legal assistance for land disputes"""
        try:
            # Process case data
            processed_case = self._preprocess_legal_case(case_data)
            
            # Analyze case and generate recommendations
            case_analysis = self.legal_assistance_model.predict_proba([processed_case])[0]
            
            # Generate detailed response
            return {
                "status": "success",
                "case_type": case_data.get('case_type'),
                "analysis": self._analyze_legal_case(case_analysis),
                "recommendations": self._generate_legal_recommendations(case_analysis),
                "similar_cases": self._find_similar_cases(case_data)
            }
            
        except Exception as e:
            logger.error(f"Error providing legal assistance: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def update_predictive_models(self, new_data: Dict) -> Dict:
        """Update AI models with new data"""
        try:
            # Update crime prediction model
            if 'crime_data' in new_data:
                self.crime_prediction_model.fit(
                    new_data['crime_data']['features'],
                    new_data['crime_data']['labels']
                )
                joblib.dump(self.crime_prediction_model,
                          self.model_dir / "crime_prediction_model.joblib")
            
            # Update traffic analysis model
            if 'traffic_data' in new_data:
                self.traffic_analysis_model.fit(new_data['traffic_data'])
                joblib.dump(self.traffic_analysis_model,
                          self.model_dir / "traffic_analysis_model.joblib")
            
            # Update legal assistance model
            if 'legal_data' in new_data:
                self.legal_assistance_model.fit(
                    new_data['legal_data']['features'],
                    new_data['legal_data']['labels']
                )
                joblib.dump(self.legal_assistance_model,
                          self.model_dir / "legal_assistance_model.joblib")
            
            return {"status": "success", "message": "Models updated successfully"}
            
        except Exception as e:
            logger.error(f"Error updating models: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def _get_crime_data(self, district: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Retrieve and preprocess crime data for analysis"""
        try:
            # Query crime reports from database
            crime_reports = CrimeReport.query.filter(
                CrimeReport.district == district,
                CrimeReport.date_of_incident.between(start_date, end_date)
            ).all()
            
            # Convert to DataFrame
            data = pd.DataFrame([
                {
                    'crime_type': report.crime_type,
                    'location': report.location,
                    'time_of_day': report.date_of_incident.hour,
                    'day_of_week': report.date_of_incident.weekday(),
                    'month': report.date_of_incident.month,
                    'is_resolved': report.status == 'resolved'
                }
                for report in crime_reports
            ])
            
            # One-hot encode categorical variables
            data = pd.get_dummies(data, columns=['crime_type'])
            
            return data
            
        except Exception as e:
            logger.error(f"Error retrieving crime data: {str(e)}")
            return pd.DataFrame()
    
    def _identify_hotspots(self, crime_data: pd.DataFrame, predictions: np.ndarray) -> List[Dict]:
        """Identify crime hotspots based on historical data and predictions"""
        try:
            # Group data by location
            location_stats = crime_data.groupby('location').agg({
                'is_resolved': ['count', 'mean'],
                'time_of_day': 'mean'
            }).reset_index()
            
            # Calculate risk scores
            location_stats['risk_score'] = (
                predictions.mean(axis=1) * 
                (1 - location_stats['is_resolved']['mean'])
            )
            
            # Identify top hotspots
            hotspots = [
                {
                    'location': row['location'],
                    'incident_count': row['is_resolved']['count'],
                    'resolution_rate': row['is_resolved']['mean'],
                    'risk_score': row['risk_score'],
                    'peak_time': f"{int(row['time_of_day']['mean'])}:00"
                }
                for _, row in location_stats.nlargest(5, 'risk_score').iterrows()
            ]
            
            return hotspots
            
        except Exception as e:
            logger.error(f"Error identifying hotspots: {str(e)}")
            return []
    
    def _calculate_risk_levels(self, predictions: np.ndarray) -> Dict:
        """Calculate risk levels for different areas and crime types"""
        try:
            risk_levels = {
                'overall': float(predictions.mean()),
                'by_severity': {
                    'high': float((predictions > 0.7).mean()),
                    'medium': float(((predictions > 0.3) & (predictions <= 0.7)).mean()),
                    'low': float((predictions <= 0.3).mean())
                }
            }
            
            return risk_levels
            
        except Exception as e:
            logger.error(f"Error calculating risk levels: {str(e)}")
            return {'overall': 0.0, 'by_severity': {'high': 0.0, 'medium': 0.0, 'low': 0.0}}
    
    def _generate_security_recommendations(self, hotspots: List[Dict]) -> List[str]:
        """Generate security recommendations based on hotspot analysis"""
        try:
            recommendations = []
            
            for hotspot in hotspots:
                if hotspot['risk_score'] > 0.7:
                    recommendations.append(
                        f"High-risk area detected at {hotspot['location']}. "
                        f"Increase patrols during peak time {hotspot['peak_time']}"
                    )
                elif hotspot['risk_score'] > 0.3:
                    recommendations.append(
                        f"Moderate risk at {hotspot['location']}. "
                        f"Consider community policing initiatives"
                    )
                
                if hotspot['resolution_rate'] < 0.5:
                    recommendations.append(
                        f"Low resolution rate at {hotspot['location']}. "
                        f"Review investigation procedures"
                    )
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return []
    
    def _preprocess_traffic_data(self, incident_data: Dict) -> np.ndarray:
        """Preprocess traffic incident data for analysis"""
        try:
            # Extract relevant features
            features = np.array([
                incident_data.get('severity', 1),  # Incident severity
                incident_data.get('vehicles_involved', 0),  # Number of vehicles
                incident_data.get('injuries', 0),  # Number of injuries
                incident_data.get('road_type', 0),  # Type of road
                incident_data.get('weather_condition', 0),  # Weather condition
                incident_data.get('time_of_day', 0),  # Hour of day
                incident_data.get('day_of_week', 0)  # Day of week
            ])
            
            # Scale features
            scaler = StandardScaler()
            scaled_features = scaler.fit_transform(features.reshape(1, -1))
            
            return scaled_features[0]
            
        except Exception as e:
            logger.error(f"Error preprocessing traffic data: {str(e)}")
            return np.zeros(7)
    
    def _calculate_impact_zone(self, incident_data: Dict) -> Dict:
        """Calculate the impact zone of a traffic incident"""
        try:
            # Get incident location coordinates
            geolocator = Nominatim(user_agent="jharkhand_ai")
            location = geolocator.geocode(incident_data.get('location', ''))
            
            if location:
                # Calculate impact radius based on severity
                severity = incident_data.get('severity', 1)
                base_radius = 0.5  # Base radius in kilometers
                impact_radius = base_radius * severity
                
                return {
                    'center': {
                        'lat': location.latitude,
                        'lng': location.longitude
                    },
                    'radius': impact_radius,
                    'affected_roads': self._get_affected_roads(location, impact_radius)
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"Error calculating impact zone: {str(e)}")
            return {}
    
    def _generate_traffic_recommendations(self, severity_score: float, incident_data: Dict) -> List[str]:
        """Generate traffic management recommendations"""
        try:
            recommendations = []
            
            # Basic response based on severity
            if severity_score > 0.8:
                recommendations.append("Immediate emergency response required. Deploy traffic control units.")
            elif severity_score > 0.5:
                recommendations.append("Moderate incident. Dispatch patrol units for traffic management.")
            else:
                recommendations.append("Minor incident. Monitor situation remotely.")
            
            # Additional context-based recommendations
            if incident_data.get('vehicles_involved', 0) > 2:
                recommendations.append("Multiple vehicles involved. Consider alternate route suggestions.")
            
            if incident_data.get('injuries', 0) > 0:
                recommendations.append("Medical response team required at the scene.")
            
            # Time-based recommendations
            hour = incident_data.get('time_of_day', 0)
            if 7 <= hour <= 10 or 16 <= hour <= 19:  # Rush hours
                recommendations.append("Peak traffic hours. Implement traffic diversion plan.")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating traffic recommendations: {str(e)}")
            return []
    
    def _preprocess_legal_case(self, case_data: Dict) -> np.ndarray:
        """Preprocess legal case data for analysis"""
        try:
            # Extract case features
            features = [
                self._encode_case_type(case_data.get('case_type', '')),
                self._encode_party_type(case_data.get('party_type', '')),
                case_data.get('land_area', 0),
                case_data.get('dispute_duration', 0),
                self._encode_tribal_status(case_data.get('tribal_status', False))
            ]
            
            return np.array(features)
            
        except Exception as e:
            logger.error(f"Error preprocessing legal case: {str(e)}")
            return np.zeros(5)
    
    def _analyze_legal_case(self, case_analysis: np.ndarray) -> Dict:
        """Analyze legal case and provide insights"""
        try:
            # Convert probabilities to meaningful insights
            resolution_prob = float(case_analysis[0])
            
            return {
                'resolution_probability': resolution_prob,
                'complexity_level': self._get_complexity_level(resolution_prob),
                'estimated_duration': self._estimate_case_duration(resolution_prob),
                'required_documents': self._get_required_documents(case_analysis)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing legal case: {str(e)}")
            return {}
    
    def _generate_legal_recommendations(self, case_analysis: np.ndarray) -> List[str]:
        """Generate legal recommendations based on case analysis"""
        try:
            recommendations = []
            resolution_prob = float(case_analysis[0])
            
            # Basic recommendations
            if resolution_prob > 0.7:
                recommendations.append("High probability of resolution through mediation.")
            elif resolution_prob > 0.4:
                recommendations.append("Consider alternative dispute resolution methods.")
            else:
                recommendations.append("Complex case. Legal representation recommended.")
            
            # Additional recommendations based on case type
            if case_analysis[1] > 0.5:  # Tribal land case
                recommendations.extend([
                    "Consult tribal council for traditional dispute resolution.",
                    "Review tribal land rights documentation."
                ])
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating legal recommendations: {str(e)}")
            return []
    
    def _find_similar_cases(self, case_data: Dict) -> List[Dict]:
        """Find similar legal cases for reference"""
        try:
            # Query similar cases from database
            similar_cases = LegalAssistance.query.filter(
                LegalAssistance.case_type == case_data.get('case_type'),
                LegalAssistance.status == 'resolved'
            ).limit(5).all()
            
            return [
                {
                    'id': case.id,
                    'case_type': case.case_type,
                    'resolution_time': case.resolution_time,
                    'resolution_method': case.resolution_method
                }
                for case in similar_cases
            ]
            
        except Exception as e:
            logger.error(f"Error finding similar cases: {str(e)}")
            return []