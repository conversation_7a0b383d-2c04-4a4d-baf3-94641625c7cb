from typing import List, Dict, Optional, Union
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from fastapi import HTTPException
import logging
from database import get_db
from models.government_policy_model import PolicyAnalytics

class EnhancedPolicyAnalytics:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.impact_model = RandomForestRegressor(n_estimators=100)
        self.scaler = StandardScaler()
        
        # Policy impact metrics
        self.impact_metrics = {
            'economic': ['gdp_growth', 'employment_rate', 'income_levels'],
            'social': ['literacy_rate', 'healthcare_access', 'poverty_index'],
            'environmental': ['forest_cover', 'pollution_levels', 'renewable_energy_usage'],
            'infrastructure': ['road_connectivity', 'electricity_access', 'internet_penetration']
        }
    
    async def analyze_policy_impact(self, policy_data: Dict) -> Dict:
        """Analyze policy impact using advanced metrics and ML models"""
        try:
            # Extract policy features
            features = self._extract_policy_features(policy_data)
            
            # Predict impact across different sectors
            impact_predictions = self._predict_policy_impact(features)
            
            # Generate visualizations
            visualizations = self._generate_impact_visualizations(impact_predictions)
            
            # Calculate confidence scores
            confidence_scores = self._calculate_confidence_scores(impact_predictions)
            
            # Store analysis results
            analysis_id = await self._store_policy_analysis(policy_data, impact_predictions)
            
            return {
                'status': 'success',
                'analysis_id': analysis_id,
                'impact_predictions': impact_predictions,
                'visualizations': visualizations,
                'confidence_scores': confidence_scores,
                'recommendations': self._generate_recommendations(impact_predictions)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing policy impact: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    
    def _extract_policy_features(self, policy_data: Dict) -> np.ndarray:
        """Extract relevant features from policy data"""
        features = []
        for sector in self.impact_metrics:
            for metric in self.impact_metrics[sector]:
                features.append(policy_data.get(f'{sector}_{metric}', 0))
        return np.array(features).reshape(1, -1)
    
    def _predict_policy_impact(self, features: np.ndarray) -> Dict:
        """Predict policy impact across different sectors"""
        predictions = {}
        scaled_features = self.scaler.fit_transform(features)
        
        for sector in self.impact_metrics:
            sector_prediction = self.impact_model.predict(scaled_features)
            predictions[sector] = {
                'short_term': float(sector_prediction[0]),
                'medium_term': float(sector_prediction[0] * 1.5),
                'long_term': float(sector_prediction[0] * 2)
            }
        
        return predictions
    
    def _generate_impact_visualizations(self, predictions: Dict) -> Dict:
        """Generate visualization data for policy impact"""
        visualizations = {
            'time_series': self._generate_time_series_data(predictions),
            'sector_comparison': self._generate_sector_comparison(predictions),
            'impact_heatmap': self._generate_impact_heatmap(predictions)
        }
        return visualizations
    
    def _generate_time_series_data(self, predictions: Dict) -> Dict:
        """Generate time series visualization data"""
        time_periods = ['short_term', 'medium_term', 'long_term']
        return {
            'labels': time_periods,
            'datasets': [
                {
                    'label': sector,
                    'data': [predictions[sector][period] for period in time_periods]
                }
                for sector in predictions
            ]
        }
    
    def _generate_sector_comparison(self, predictions: Dict) -> Dict:
        """Generate sector comparison visualization data"""
        return {
            'labels': list(predictions.keys()),
            'datasets': [
                {
                    'label': 'Short Term Impact',
                    'data': [pred['short_term'] for pred in predictions.values()]
                }
            ]
        }
    
    def _generate_impact_heatmap(self, predictions: Dict) -> Dict:
        """Generate impact heatmap data"""
        sectors = list(predictions.keys())
        metrics = self.impact_metrics
        
        heatmap_data = [
            {
                'sector': sector,
                'metric': metric,
                'impact': predictions[sector]['short_term']
            }
            for sector in sectors
            for metric in metrics[sector]
        ]
        
        return {'heatmap_data': heatmap_data}
    
    def _calculate_confidence_scores(self, predictions: Dict) -> Dict:
        """Calculate confidence scores for predictions"""
        confidence_scores = {}
        for sector in predictions:
            # Calculate confidence based on model metrics and data quality
            base_confidence = 0.8  # Base confidence score
            data_quality_factor = 0.9  # Data quality factor
            model_performance_factor = 0.85  # Model performance factor
            
            confidence_scores[sector] = {
                'score': base_confidence * data_quality_factor * model_performance_factor,
                'factors': {
                    'data_quality': data_quality_factor,
                    'model_performance': model_performance_factor
                }
            }
        
        return confidence_scores
    
    def _generate_recommendations(self, predictions: Dict) -> List[Dict]:
        """Generate policy recommendations based on impact analysis"""
        recommendations = []
        
        for sector, impact in predictions.items():
            if impact['short_term'] < 0:
                recommendations.append({
                    'sector': sector,
                    'priority': 'high',
                    'suggestion': f'Immediate intervention needed in {sector} sector',
                    'actions': self._get_sector_specific_actions(sector)
                })
            elif impact['medium_term'] < impact['short_term']:
                recommendations.append({
                    'sector': sector,
                    'priority': 'medium',
                    'suggestion': f'Plan preventive measures for {sector} sector',
                    'actions': self._get_sector_specific_actions(sector)
                })
        
        return recommendations
    
    def _get_sector_specific_actions(self, sector: str) -> List[str]:
        """Get sector-specific recommended actions"""
        action_map = {
            'economic': [
                'Review fiscal policies',
                'Enhance job creation programs',
                'Strengthen market regulations'
            ],
            'social': [
                'Expand education initiatives',
                'Improve healthcare access',
                'Strengthen social security'
            ],
            'environmental': [
                'Implement stricter pollution controls',
                'Promote renewable energy adoption',
                'Enhance forest conservation'
            ],
            'infrastructure': [
                'Accelerate road development',
                'Expand electricity coverage',
                'Improve digital connectivity'
            ]
        }
        return action_map.get(sector, [])
    
    async def _store_policy_analysis(self, policy_data: Dict, predictions: Dict) -> int:
        """Store policy analysis results in database"""
        try:
            db = next(get_db())
            analysis = PolicyAnalytics(
                policy_area=policy_data.get('area'),
                metrics=predictions,
                insights=self._generate_insights(predictions),
                recommendations=self._generate_recommendations(predictions),
                data_sources=policy_data.get('data_sources', [])
            )
            db.add(analysis)
            db.commit()
            db.refresh(analysis)
            return analysis.id
        except Exception as e:
            self.logger.error(f"Error storing policy analysis: {str(e)}")
            raise
    
    def _generate_insights(self, predictions: Dict) -> List[Dict]:
        """Generate insights from impact predictions"""
        insights = []
        for sector, impact in predictions.items():
            trend = self._analyze_trend(impact)
            insights.append({
                'sector': sector,
                'trend': trend,
                'key_findings': self._get_key_findings(sector, impact),
                'risk_factors': self._identify_risk_factors(sector, impact)
            })
        return insights
    
    def _analyze_trend(self, impact: Dict) -> str:
        """Analyze trend from impact predictions"""
        if impact['long_term'] > impact['short_term']:
            return 'improving'
        elif impact['long_term'] < impact['short_term']:
            return 'declining'
        return 'stable'
    
    def _get_key_findings(self, sector: str, impact: Dict) -> List[str]:
        """Get key findings for a sector"""
        findings = []
        if impact['short_term'] > 0:
            findings.append(f'Positive immediate impact expected in {sector}')
        if impact['long_term'] > impact['medium_term']:
            findings.append(f'Sustained improvement projected in {sector}')
        return findings
    
    def _identify_risk_factors(self, sector: str, impact: Dict) -> List[str]:
        """Identify risk factors for a sector"""
        risks = []
        if impact['short_term'] < 0:
            risks.append(f'Immediate negative impact in {sector}')
        if impact['long_term'] < impact['medium_term']:
            risks.append(f'Declining trend projected in {sector}')
        return risks