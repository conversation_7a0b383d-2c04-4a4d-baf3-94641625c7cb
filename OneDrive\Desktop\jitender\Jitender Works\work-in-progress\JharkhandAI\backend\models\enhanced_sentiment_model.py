"""Enhanced Sentiment Analysis Model for Jharkhand citizen feedback.
This module provides advanced sentiment analysis capabilities for processing
citizen feedback in multiple languages including tribal languages."""

import os
import json
import logging
from typing import Dict, List, Optional, Union, Any
from pathlib import Path
import torch
from transformers import (
    AutoModelForSequenceClassification,
    AutoTokenizer,
    TrainingArguments,
    Trainer
)
from datasets import Dataset
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class JharkhandSentimentModel:
    """Enhanced sentiment analysis model with support for multiple languages
    including tribal languages of Jharkhand."""
    
    SUPPORTED_LANGUAGES = {
        'en': 'English',
        'hi': 'Hindi',
        'sa': 'Santhali',
        'ho': 'Ho',
        'mu': 'Mundari',
        'ku': 'Kurukh',
        'kh': 'Kharia'
    }
    
    SENTIMENT_LABELS = {
        0: 'negative',
        1: 'neutral',
        2: 'positive'
    }
    
    def __init__(
        self,
        model_name: str = "nlptown/bert-base-multilingual-uncased-sentiment",
        device: Optional[str] = None,
        offline_mode: bool = False,
        model_dir: str = "models/sentiment"
    ):
        """Initialize the sentiment analysis model.
        
        Args:
            model_name: Base model to use
            device: Device to run the model on ('cpu', 'cuda', or None for auto-detection)
            offline_mode: Whether to use offline models only
            model_dir: Directory to store and load fine-tuned models
        """
        self.model_name = model_name
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        self.offline_mode = offline_mode
        
        # Auto-detect device if not specified
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
            
        logger.info(f"Initializing JharkhandSentimentModel with {model_name} on {self.device}")
        
        # Load model and tokenizer
        try:
            if offline_mode:
                local_model_path = self.model_dir / "base_model"
                if local_model_path.exists():
                    self.tokenizer = AutoTokenizer.from_pretrained(str(local_model_path))
                    self.model = AutoModelForSequenceClassification.from_pretrained(
                        str(local_model_path),
                        device_map=self.device if self.device == "cuda" else None
                    )
                else:
                    raise ValueError(f"Offline mode enabled but no model found at {local_model_path}")
            else:
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                self.model = AutoModelForSequenceClassification.from_pretrained(
                    model_name,
                    device_map=self.device if self.device == "cuda" else None
                )
                
                # Save the base model for offline use
                self.tokenizer.save_pretrained(str(self.model_dir / "base_model"))
                self.model.save_pretrained(str(self.model_dir / "base_model"))
                
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise
    
    def analyze_sentiment(
        self,
        text: str,
        language: str = 'en'
    ) -> Dict[str, Any]:
        """Analyze the sentiment of the given text.
        
        Args:
            text: Input text for sentiment analysis
            language: Language code of the input text
            
        Returns:
            Dictionary containing sentiment analysis results
        """
        if language not in self.SUPPORTED_LANGUAGES:
            raise ValueError(f"Unsupported language: {language}")
            
        try:
            # Tokenize input text
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                max_length=512
            ).to(self.device)
            
            # Get model predictions
            with torch.no_grad():
                outputs = self.model(**inputs)
                scores = torch.softmax(outputs.logits, dim=1)
                prediction = torch.argmax(scores, dim=1).item()
            
            # Convert scores to probabilities
            probabilities = scores.cpu().numpy()[0]
            
            return {
                'text': text,
                'language': language,
                'sentiment': self.SENTIMENT_LABELS[prediction],
                'confidence': float(probabilities[prediction]),
                'probabilities': {
                    label: float(prob)
                    for label, prob in zip(self.SENTIMENT_LABELS.values(), probabilities)
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {str(e)}")
            raise
    
    def analyze_batch(
        self,
        texts: List[str],
        language: str = 'en'
    ) -> List[Dict[str, Any]]:
        """Analyze sentiment for a batch of texts.
        
        Args:
            texts: List of input texts
            language: Language code for the input texts
            
        Returns:
            List of dictionaries containing sentiment analysis results
        """
        return [self.analyze_sentiment(text, language) for text in texts]
    
    def fine_tune(
        self,
        train_texts: List[str],
        train_labels: List[int],
        validation_texts: Optional[List[str]] = None,
        validation_labels: Optional[List[int]] = None,
        language: str = 'en',
        num_epochs: int = 3,
        batch_size: int = 8,
        learning_rate: float = 2e-5
    ):
        """Fine-tune the model on domain-specific data.
        
        Args:
            train_texts: Training text samples
            train_labels: Training labels (0: negative, 1: neutral, 2: positive)
            validation_texts: Optional validation text samples
            validation_labels: Optional validation labels
            language: Language of the training data
            num_epochs: Number of training epochs
            batch_size: Training batch size
            learning_rate: Learning rate for fine-tuning
        """
        if language not in self.SUPPORTED_LANGUAGES:
            raise ValueError(f"Unsupported language: {language}")
            
        try:
            # Prepare training dataset
            train_encodings = self.tokenizer(
                train_texts,
                truncation=True,
                padding=True,
                max_length=512
            )
            train_dataset = Dataset.from_dict({
                'input_ids': train_encodings['input_ids'],
                'attention_mask': train_encodings['attention_mask'],
                'labels': train_labels
            })
            
            # Prepare validation dataset if provided
            if validation_texts and validation_labels:
                val_encodings = self.tokenizer(
                    validation_texts,
                    truncation=True,
                    padding=True,
                    max_length=512
                )
                val_dataset = Dataset.from_dict({
                    'input_ids': val_encodings['input_ids'],
                    'attention_mask': val_encodings['attention_mask'],
                    'labels': validation_labels
                })
            else:
                val_dataset = None
            
            # Set up training arguments
            training_args = TrainingArguments(
                output_dir=str(self.model_dir / f"finetuned_{language}"),
                num_train_epochs=num_epochs,
                per_device_train_batch_size=batch_size,
                per_device_eval_batch_size=batch_size,
                learning_rate=learning_rate,
                weight_decay=0.01,
                logging_dir=str(self.model_dir / "logs"),
                save_strategy="epoch"
            )
            
            # Initialize trainer
            trainer = Trainer(
                model=self.model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset
            )
            
            # Start fine-tuning
            trainer.train()
            
            # Save fine-tuned model
            self.model.save_pretrained(str(self.model_dir / f"finetuned_{language}"))
            self.tokenizer.save_pretrained(str(self.model_dir / f"finetuned_{language}"))
            
            logger.info(f"Model fine-tuning completed for language: {language}")
            
        except Exception as e:
            logger.error(f"Error during fine-tuning: {str(e)}")
            raise