import os
import torch
try:
    import torchaudio
except ImportError:
    # Install torchaudio if not present
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "torchaudio"])
    import torchaudio
from transformers import Wav2Vec2ForCTC, Wav2Vec2Processor, AutoModelForSpeechSeq2Seq, AutoProcessor
import numpy as np
from typing import Dict, List, Optional, Union
import logging

logger = logging.getLogger(__name__)

class EnhancedSpeechModel:
    """
    Enhanced Speech Model with support for tribal languages of Jharkhand
    including Santhali, Ho, Mundari, Kurukh, and Kharia.
    """
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize models for different languages
        self.speech_to_text_models = {}
        self.text_to_speech_models = {}
        self.processors = {}
        
        # Load models for each supported language
        self._load_models()
    
    def _load_models(self):
        """Load speech models for all supported languages"""
        # Standard languages
        self._load_language_models("en", "english")
        self._load_language_models("hi", "hindi")
        
        # Tribal languages
        self._load_language_models("sa", "santhali")
        self._load_language_models("ho", "ho")
        self._load_language_models("mu", "mundari")
        self._load_language_models("ku", "kurukh")
        self._load_language_models("kh", "kharia")
    
    def _load_language_models(self, lang_code: str, lang_name: str):
        """Load models for a specific language"""
        try:
            # For well-supported languages like English and Hindi, use pre-trained models
            if lang_code in ["en", "hi"]:
                model_name = f"facebook/wav2vec2-large-xlsr-53-{lang_name}" if lang_code == "en" else "ai4bharat/indicwav2vec-hindi"
                self.speech_to_text_models[lang_code] = Wav2Vec2ForCTC.from_pretrained(model_name).to(self.device)
                self.processors[lang_code] = Wav2Vec2Processor.from_pretrained(model_name)
                
                # Text to speech models
                tts_model_name = "facebook/mms-tts-eng" if lang_code == "en" else "ai4bharat/indictts-hindi"
                self.text_to_speech_models[lang_code] = AutoModelForSpeechSeq2Seq.from_pretrained(tts_model_name).to(self.device)
            
            # For tribal languages, use fine-tuned or specialized models
            else:
                # Check if we have custom fine-tuned models for tribal languages
                custom_model_path = f"./models/speech/{lang_code}_speech_to_text"
                if os.path.exists(custom_model_path):
                    self.speech_to_text_models[lang_code] = Wav2Vec2ForCTC.from_pretrained(custom_model_path).to(self.device)
                    self.processors[lang_code] = Wav2Vec2Processor.from_pretrained(custom_model_path)
                else:
                    # Fallback to multilingual model with language adaptation
                    logger.info(f"No specialized model found for {lang_name}, using multilingual model with adaptation")
                    self.speech_to_text_models[lang_code] = Wav2Vec2ForCTC.from_pretrained("facebook/wav2vec2-large-xlsr-53").to(self.device)
                    self.processors[lang_code] = Wav2Vec2Processor.from_pretrained("facebook/wav2vec2-large-xlsr-53")
                
                # Text to speech for tribal languages
                custom_tts_path = f"./models/speech/{lang_code}_text_to_speech"
                if os.path.exists(custom_tts_path):
                    self.text_to_speech_models[lang_code] = AutoModelForSpeechSeq2Seq.from_pretrained(custom_tts_path).to(self.device)
                else:
                    # Fallback to a base model that can be adapted
                    logger.info(f"No specialized TTS model found for {lang_name}, using adaptable base model")
                    self.text_to_speech_models[lang_code] = AutoModelForSpeechSeq2Seq.from_pretrained("facebook/mms-tts").to(self.device)
            
            logger.info(f"Successfully loaded models for {lang_name} ({lang_code})")
        
        except Exception as e:
            logger.error(f"Error loading models for {lang_name}: {str(e)}")
            # Set to None to indicate this language is not fully supported
            self.speech_to_text_models[lang_code] = None
            self.text_to_speech_models[lang_code] = None
    
    def speech_to_text(self, audio_file: str, language: str = "en", real_time: bool = False) -> Dict:
        """
        Convert speech to text in the specified language with real-time processing support
        
        Args:
            audio_file: Path to the audio file or audio stream for real-time processing
            language: Language code (en, hi, sa, ho, mu, ku, kh)
            real_time: Whether to process audio in real-time streaming mode
            
        Returns:
            Dict containing the transcribed text, confidence score, and timing information
        """
        if language not in self.speech_to_text_models or self.speech_to_text_models[language] is None:
            return {"error": f"Speech to text not supported for language: {language}"}
        
        try:
            # Load and preprocess audio
            waveform, sample_rate = torchaudio.load(audio_file)
            if sample_rate != 16000:
                waveform = torchaudio.functional.resample(waveform, sample_rate, 16000)
            
            # Convert to mono if stereo
            if waveform.shape[0] > 1:
                waveform = torch.mean(waveform, dim=0, keepdim=True)
            
            # Process through model
            processor = self.processors[language]
            model = self.speech_to_text_models[language]
            
            input_values = processor(waveform.squeeze().numpy(), sampling_rate=16000, return_tensors="pt").input_values.to(self.device)
            
            with torch.no_grad():
                logits = model(input_values).logits
                predicted_ids = torch.argmax(logits, dim=-1)
            
            transcription = processor.batch_decode(predicted_ids)[0]
            
            # Calculate confidence score (simplified)
            confidence = torch.softmax(logits, dim=-1).max(dim=-1)[0].mean().item()
            
            return {
                "text": transcription,
                "confidence": confidence,
                "language": language
            }
        
        except Exception as e:
            logger.error(f"Error in speech to text conversion: {str(e)}")
            return {"error": str(e)}
    
    def text_to_speech(self, text: str, language: str = "en", output_file: str = "output.wav", voice_style: str = None) -> Dict:
        """Convert text to speech in the specified language with enhanced voice quality"""
        if language not in self.text_to_speech_models or self.text_to_speech_models[language] is None:
            return {"error": f"Text to speech not supported for language: {language}"}
        
        try:
            model = self.text_to_speech_models[language]
            
            # Apply voice style and prosody adjustments
            voice_config = self._get_voice_config(language, voice_style)
            
            # Generate speech with enhanced quality
            inputs = AutoProcessor.from_pretrained("facebook/mms-tts")(
                text=text,
                return_tensors="pt",
                voice_preset=voice_config.get("preset"),
                speaking_rate=voice_config.get("speaking_rate", 1.0),
                pitch=voice_config.get("pitch", 0.0)
            ).to(self.device)
            
            with torch.no_grad():
                output = model.generate(
                    **inputs,
                    do_sample=True,
                    temperature=voice_config.get("temperature", 0.7),
                    max_length=1000
                )
            
            # Apply audio enhancements
            sampling_rate = model.config.sampling_rate
            audio_array = output[0].cpu().numpy()
            audio_array = self._enhance_audio_quality(audio_array, language)
            
            # Optimize for low bandwidth if needed
            audio_tensor = self._optimize_audio_for_bandwidth(torch.tensor(audio_array).unsqueeze(0))
            
            # Save with compression
            torchaudio.save(
                output_file,
                audio_tensor,
                sampling_rate,
                compression_type="ogg",
                compression_args={"quality": 5}
            )
            
            return {
                "audio_file": output_file,
                "language": language,
                "duration": len(audio_array) / sampling_rate,
                "voice_style": voice_style
            }
        
        except Exception as e:
            logger.error(f"Error in text to speech conversion: {str(e)}")
            return {"error": str(e)}
    
    def _get_voice_config(self, language: str, voice_style: str = None) -> Dict:
        """Get voice configuration for natural-sounding speech"""
        base_config = {
            "preset": "natural",
            "speaking_rate": 1.0,
            "pitch": 0.0,
            "temperature": 0.7
        }
        
        if voice_style == "educational":
            base_config.update({
                "speaking_rate": 0.9,
                "temperature": 0.6
            })
        elif voice_style == "conversational":
            base_config.update({
                "speaking_rate": 1.1,
                "temperature": 0.8
            })
        
        # Language-specific adjustments
        if language in ["sa", "ho", "mu", "ku", "kh"]:
            base_config["temperature"] = 0.65  # More stable for tribal languages
        
        return base_config
    
    def _enhance_audio_quality(self, audio_array: np.ndarray, language: str) -> np.ndarray:
        """Enhance audio quality with language-specific processing"""
        try:
            # Apply noise reduction
            import noisereduce as nr
            audio_array = nr.reduce_noise(y=audio_array, sr=16000)
            
            # Normalize volume
            audio_array = librosa.util.normalize(audio_array)
            
            # Apply language-specific enhancements
            if language in ["sa", "ho", "mu", "ku", "kh"]:
                # Additional processing for tribal languages
                audio_array = self._enhance_tribal_audio(audio_array)
            
            return audio_array
        except Exception as e:
            logger.warning(f"Audio enhancement failed: {str(e)}. Using original audio.")
            return audio_array
    
    def _optimize_audio_for_bandwidth(self, audio_tensor: torch.Tensor) -> torch.Tensor:
        """Optimize audio for low-bandwidth environments"""
        try:
            # Reduce sample rate if needed
            if audio_tensor.size(-1) > 160000:  # More than 10 seconds
                audio_tensor = torchaudio.functional.resample(
                    audio_tensor,
                    orig_freq=16000,
                    new_freq=8000
                )
            
            # Apply compression
            audio_tensor = torch.clamp(audio_tensor, -1.0, 1.0)
            
            return audio_tensor
        except Exception as e:
            logger.warning(f"Audio optimization failed: {str(e)}. Using original audio.")
            return audio_tensor
    
    def _enhance_tribal_audio(self, audio_array: np.ndarray) -> np.ndarray:
        """Apply specific enhancements for tribal language audio"""
        try:
            # Enhance clarity
            audio_array = librosa.effects.preemphasis(audio_array)
            
            # Adjust speed slightly for better comprehension
            audio_array = librosa.effects.time_stretch(audio_array, rate=0.95)
            
            return audio_array
        except Exception as e:
            logger.warning(f"Tribal audio enhancement failed: {str(e)}. Using original audio.")
            return audio_array
    
    def get_supported_languages(self) -> List[Dict]:
        """
        Get list of supported languages with their capabilities
        
        Returns:
            List of dictionaries with language information
        """
        languages = []
        
        for lang_code in self.speech_to_text_models.keys():
            stt_supported = self.speech_to_text_models[lang_code] is not None
            tts_supported = self.text_to_speech_models[lang_code] is not None
            
            language_info = {
                "code": lang_code,
                "name": self._get_language_name(lang_code),
                "speech_to_text": stt_supported,
                "text_to_speech": tts_supported
            }
            
            languages.append(language_info)
        
        return languages
    
    def _get_language_name(self, lang_code: str) -> str:
        """Get the full name of a language from its code"""
        language_names = {
            "en": "English",
            "hi": "Hindi",
            "sa": "Santhali",
            "ho": "Ho",
            "mu": "Mundari",
            "ku": "Kurukh",
            "kh": "Kharia"
        }
        return language_names.get(lang_code, "Unknown")
