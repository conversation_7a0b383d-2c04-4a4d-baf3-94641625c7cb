import os
import json
import logging
from typing import Dict, List, Optional, Union, Any
from pathlib import Path
import torch
from transformers import AutoModelForSeq2SeqLM, AutoTokenizer
from PIL import Image
import numpy as np

from .enhanced_vision_model import JharkhandVisionModel
from .enhanced_language_model import JharkhandLanguageModel
from .enhanced_speech_model import EnhancedSpeechModel
from ..utils.language_utils import get_language_code, get_language_name

logger = logging.getLogger(__name__)

class EnhancedTourismModel:
    """
    Enhanced Tourism Model with AI-powered features including:
    - Virtual tribal museum with AI-generated visuals
    - AR/VR experiences for cultural immersion
    - AI-driven language translation for tourists
    - Personalized tourism recommendations
    """
    
    def __init__(
        self,
        vision_model: Optional[JharkhandVisionModel] = None,
        language_model: Optional[JharkhandLanguageModel] = None,
        speech_model: Optional[EnhancedSpeechModel] = None,
        model_dir: str = "models/tourism"
    ):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize component models
        self.vision_model = vision_model or JharkhandVisionModel()
        self.language_model = language_model or JharkhandLanguageModel()
        self.speech_model = speech_model or EnhancedSpeechModel()
        
        # Set up model directory
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # Load tourism-specific data
        self.tourism_data = self._load_tourism_data()
    
    def _load_tourism_data(self) -> Dict:
        """Load tourism-specific data including destinations, cultural sites, etc."""
        try:
            data_path = self.model_dir / "tourism_data.json"
            if data_path.exists():
                with open(data_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading tourism data: {str(e)}")
            return {}
    
    def generate_virtual_exhibit(
        self,
        artifact_name: str,
        description: str,
        language: str = "en"
    ) -> Dict:
        """Generate AI-powered virtual museum exhibit"""
        try:
            # Generate visual representation
            visual_prompt = f"Traditional tribal artifact: {artifact_name} from Jharkhand. {description}"
            visual_result = self.vision_model.generate_image(visual_prompt)
            
            # Translate description if needed
            if language != "en":
                translated_desc = self.language_model.translate(
                    text=description,
                    source_lang="en",
                    target_lang=language
                )
            else:
                translated_desc = description
            
            # Generate audio narration
            audio_result = self.speech_model.text_to_speech(
                text=translated_desc,
                language=language
            )
            
            return {
                "name": artifact_name,
                "description": translated_desc,
                "visual_content": visual_result,
                "audio_narration": audio_result,
                "language": language
            }
        except Exception as e:
            logger.error(f"Error generating virtual exhibit: {str(e)}")
            return {"error": str(e)}
    
    def create_ar_experience(
        self,
        location_name: str,
        cultural_elements: List[Dict],
        language: str = "en"
    ) -> Dict:
        """Create AR/VR experience for cultural immersion"""
        try:
            ar_elements = []
            
            for element in cultural_elements:
                # Generate visual content
                visual = self.vision_model.generate_image(
                    prompt=element.get("description", "")
                )
                
                # Generate audio narration
                if language != "en":
                    translated_text = self.language_model.translate(
                        text=element.get("description", ""),
                        source_lang="en",
                        target_lang=language
                    )
                else:
                    translated_text = element.get("description", "")
                
                audio = self.speech_model.text_to_speech(
                    text=translated_text,
                    language=language
                )
                
                ar_elements.append({
                    "name": element.get("name", ""),
                    "description": translated_text,
                    "visual_content": visual,
                    "audio_content": audio,
                    "position": element.get("position", {}),
                    "interaction_type": element.get("interaction_type", "view")
                })
            
            return {
                "location": location_name,
                "elements": ar_elements,
                "language": language
            }
        except Exception as e:
            logger.error(f"Error creating AR experience: {str(e)}")
            return {"error": str(e)}
    
    def get_personalized_recommendations(
        self,
        user_preferences: Dict,
        language: str = "en"
    ) -> Dict:
        """Get AI-powered personalized tourism recommendations"""
        try:
            # Extract user preferences
            interests = user_preferences.get("interests", [])
            duration = user_preferences.get("duration", 3)
            include_tribal = user_preferences.get("include_tribal", True)
            budget = user_preferences.get("budget", "medium")
            
            # Filter destinations based on preferences
            recommended_destinations = self._filter_destinations(
                interests=interests,
                duration=duration,
                include_tribal=include_tribal,
                budget=budget
            )
            
            # Translate recommendations if needed
            if language != "en":
                for dest in recommended_destinations:
                    dest["name"] = self.language_model.translate(
                        text=dest["name"],
                        source_lang="en",
                        target_lang=language
                    )
                    dest["description"] = self.language_model.translate(
                        text=dest["description"],
                        source_lang="en",
                        target_lang=language
                    )
            
            return {
                "destinations": recommended_destinations,
                "itinerary": self._generate_itinerary(recommended_destinations, duration),
                "language": language
            }
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return {"error": str(e)}
    
    def _filter_destinations(self, interests: List[str], duration: int, include_tribal: bool, budget: str) -> List[Dict]:
        """Filter destinations based on user preferences"""
        filtered = []
        
        for dest in self.tourism_data.get("destinations", []):
            # Check if destination matches interests
            if not interests or any(i in dest.get("tags", []) for i in interests):
                # Check tribal preference
                if include_tribal or not dest.get("is_tribal", False):
                    # Check budget compatibility
                    if budget == "any" or dest.get("budget_category") == budget:
                        filtered.append(dest)
        
        # Limit based on duration
        return filtered[:duration * 3]  # Assume 3 destinations per day max
    
    def _generate_itinerary(self, destinations: List[Dict], duration: int) -> List[Dict]:
        """Generate a day-by-day itinerary from selected destinations"""
        itinerary = []
        
        # Group destinations by day
        for day in range(duration):
            day_dests = destinations[day * 3:(day + 1) * 3]
            if not day_dests:
                break
                
            day_plan = {
                "day": day + 1,
                "destinations": day_dests,
                "activities": [
                    {
                        "time": "Morning",
                        "destination": day_dests[0],
                        "duration": "3 hours"
                    }
                ]
            }
            
            if len(day_dests) > 1:
                day_plan["activities"].append({
                    "time": "Afternoon",
                    "destination": day_dests[1],
                    "duration": "3 hours"
                })
            
            if len(day_dests) > 2:
                day_plan["activities"].append({
                    "time": "Evening",
                    "destination": day_dests[2],
                    "duration": "2 hours"
                })
            
            itinerary.append(day_plan)
        
        return itinerary