"""
Enhanced Vision Model for Jharkhand-specific computer vision tasks.
This module provides advanced computer vision capabilities for forest monitoring,
tribal art recognition, and other Jharkhand-specific visual tasks.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Union, Any, Tuple
from pathlib import Path
import torch
import numpy as np
from PIL import Image
import cv2
from transformers import (
    AutoImageProcessor, 
    AutoModelForImageClassification,
    AutoModelForObjectDetection,
    AutoModelForSemanticSegmentation,
    DetrImageProcessor,
    DetrForObjectDetection
)
from datasets import Dataset, load_dataset

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class JharkhandVisionModel:
    """
    Enhanced vision model for Jharkhand-specific computer vision tasks.
    Provides capabilities for forest monitoring, tribal art recognition, and more.
    """
    
    # Define task types
    TASK_CLASSIFICATION = "classification"
    TASK_OBJECT_DETECTION = "object_detection"
    TASK_SEGMENTATION = "segmentation"
    
    # Define model types
    MODEL_FOREST_MONITORING = "forest_monitoring"
    MODEL_TRIBAL_ART = "tribal_art"
    MODEL_WILDLIFE = "wildlife"
    MODEL_AGRICULTURE = "agriculture"
    
    def __init__(
        self, 
        classification_model_name: str = "google/vit-base-patch16-224", 
        detection_model_name: str = "facebook/detr-resnet-50",
        segmentation_model_name: str = "nvidia/segformer-b0-finetuned-ade-512-512",
        device: str = None,
        offline_mode: bool = False,
        model_dir: str = "models/vision"
    ):
        """
        Initialize the Jharkhand Vision Model.
        
        Args:
            classification_model_name: Base classification model to use
            detection_model_name: Base object detection model to use
            segmentation_model_name: Base segmentation model to use
            device: Device to run the model on ('cpu', 'cuda', or None for auto-detection)
            offline_mode: Whether to use offline models only
            model_dir: Directory to store and load fine-tuned models
        """
        self.classification_model_name = classification_model_name
        self.detection_model_name = detection_model_name
        self.segmentation_model_name = segmentation_model_name
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        self.offline_mode = offline_mode
        
        # Auto-detect device if not specified
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
            
        logger.info(f"Initializing JharkhandVisionModel on {self.device}")
        logger.info(f"Classification model: {classification_model_name}")
        logger.info(f"Detection model: {detection_model_name}")
        logger.info(f"Segmentation model: {segmentation_model_name}")
        
        # Load base classification model
        try:
            if offline_mode:
                # Load from local directory if in offline mode
                local_cls_path = self.model_dir / "base_classification_model"
                if local_cls_path.exists():
                    self.cls_processor = AutoImageProcessor.from_pretrained(str(local_cls_path))
                    self.cls_model = AutoModelForImageClassification.from_pretrained(
                        str(local_cls_path),
                        device_map=self.device if self.device == "cuda" else None
                    )
                else:
                    raise ValueError(f"Offline mode enabled but no classification model found at {local_cls_path}")
            else:
                # Load from Hugging Face
                self.cls_processor = AutoImageProcessor.from_pretrained(classification_model_name)
                self.cls_model = AutoModelForImageClassification.from_pretrained(
                    classification_model_name,
                    device_map=self.device if self.device == "cuda" else None
                )
                
                # Save the base model for offline use
                self.cls_processor.save_pretrained(str(self.model_dir / "base_classification_model"))
                self.cls_model.save_pretrained(str(self.model_dir / "base_classification_model"))
                
            logger.info(f"Successfully loaded classification model and processor")
        except Exception as e:
            logger.error(f"Error loading classification model: {str(e)}")
            raise
            
        # Load base detection model
        try:
            if offline_mode:
                # Load from local directory if in offline mode
                local_det_path = self.model_dir / "base_detection_model"
                if local_det_path.exists():
                    self.det_processor = DetrImageProcessor.from_pretrained(str(local_det_path))
                    self.det_model = DetrForObjectDetection.from_pretrained(
                        str(local_det_path),
                        device_map=self.device if self.device == "cuda" else None
                    )
                else:
                    raise ValueError(f"Offline mode enabled but no detection model found at {local_det_path}")
            else:
                # Load from Hugging Face
                self.det_processor = DetrImageProcessor.from_pretrained(detection_model_name)
                self.det_model = DetrForObjectDetection.from_pretrained(
                    detection_model_name,
                    device_map=self.device if self.device == "cuda" else None
                )
                
                # Save the base model for offline use
                self.det_processor.save_pretrained(str(self.model_dir / "base_detection_model"))
                self.det_model.save_pretrained(str(self.model_dir / "base_detection_model"))
                
            logger.info(f"Successfully loaded detection model and processor")
        except Exception as e:
            logger.error(f"Error loading detection model: {str(e)}")
            raise
            
        # Load base segmentation model
        try:
            if offline_mode:
                # Load from local directory if in offline mode
                local_seg_path = self.model_dir / "base_segmentation_model"
                if local_seg_path.exists():
                    self.seg_processor = AutoImageProcessor.from_pretrained(str(local_seg_path))
                    self.seg_model = AutoModelForSemanticSegmentation.from_pretrained(
                        str(local_seg_path),
                        device_map=self.device if self.device == "cuda" else None
                    )
                else:
                    raise ValueError(f"Offline mode enabled but no segmentation model found at {local_seg_path}")
            else:
                # Load from Hugging Face
                self.seg_processor = AutoImageProcessor.from_pretrained(segmentation_model_name)
                self.seg_model = AutoModelForSemanticSegmentation.from_pretrained(
                    segmentation_model_name,
                    device_map=self.device if self.device == "cuda" else None
                )
                
                # Save the base model for offline use
                self.seg_processor.save_pretrained(str(self.model_dir / "base_segmentation_model"))
                self.seg_model.save_pretrained(str(self.model_dir / "base_segmentation_model"))
                
            logger.info(f"Successfully loaded segmentation model and processor")
        except Exception as e:
            logger.error(f"Error loading segmentation model: {str(e)}")
            raise
            
        # Dictionary to track fine-tuned models for specific tasks
        self.fine_tuned_models = {}
        self.load_fine_tuned_models()
        
        # Load class labels for different tasks
        self.class_labels = self._load_class_labels()
    
    def _load_class_labels(self) -> Dict[str, List[str]]:
        """Load class labels for different tasks."""
        labels = {}
        labels_dir = self.model_dir / "class_labels"
        
        if not labels_dir.exists():
            labels_dir.mkdir(parents=True, exist_ok=True)
            
            # Create default class labels for different tasks
            forest_labels = [
                "healthy_forest", "deforestation", "forest_fire", "regrowth",
                "illegal_logging", "mining_activity", "plantation", "water_body"
            ]
            
            tribal_art_labels = [
                "sohrai", "khovar", "jadopatia", "paitkar", "saura", 
                "warli", "gond", "santhal", "oraon", "munda"
            ]
            
            wildlife_labels = [
                "elephant", "tiger", "leopard", "bear", "deer", "monkey",
                "wild_boar", "peacock", "snake", "bird"
            ]
            
            agriculture_labels = [
                "rice", "wheat", "maize", "pulses", "vegetables", "fruits",
                "healthy_crop", "diseased_crop", "irrigation", "harvesting"
            ]
            
            # Save default labels
            with open(labels_dir / "forest_monitoring.json", "w") as f:
                json.dump(forest_labels, f, indent=2)
                
            with open(labels_dir / "tribal_art.json", "w") as f:
                json.dump(tribal_art_labels, f, indent=2)
                
            with open(labels_dir / "wildlife.json", "w") as f:
                json.dump(wildlife_labels, f, indent=2)
                
            with open(labels_dir / "agriculture.json", "w") as f:
                json.dump(agriculture_labels, f, indent=2)
            
            # Add to labels dictionary
            labels[self.MODEL_FOREST_MONITORING] = forest_labels
            labels[self.MODEL_TRIBAL_ART] = tribal_art_labels
            labels[self.MODEL_WILDLIFE] = wildlife_labels
            labels[self.MODEL_AGRICULTURE] = agriculture_labels
            
            logger.info("Created default class labels")
            return labels
        
        # Load all available class labels
        for label_file in labels_dir.glob("*.json"):
            task_name = label_file.stem
            with open(label_file, "r") as f:
                task_labels = json.load(f)
            labels[task_name] = task_labels
            logger.info(f"Loaded class labels for {task_name}")
            
        return labels
    
    def load_fine_tuned_models(self):
        """Load all available fine-tuned models from the model directory."""
        # Define tasks and their corresponding model types
        tasks = [
            (self.MODEL_FOREST_MONITORING, self.TASK_CLASSIFICATION),
            (self.MODEL_FOREST_MONITORING, self.TASK_OBJECT_DETECTION),
            (self.MODEL_FOREST_MONITORING, self.TASK_SEGMENTATION),
            (self.MODEL_TRIBAL_ART, self.TASK_CLASSIFICATION),
            (self.MODEL_WILDLIFE, self.TASK_OBJECT_DETECTION),
            (self.MODEL_AGRICULTURE, self.TASK_SEGMENTATION)
        ]
        
        # Load models for each task
        for task_name, task_type in tasks:
            model_path = self.model_dir / f"fine_tuned_{task_name}_{task_type}"
            if model_path.exists():
                try:
                    if task_type == self.TASK_CLASSIFICATION:
                        processor = AutoImageProcessor.from_pretrained(str(model_path))
                        model = AutoModelForImageClassification.from_pretrained(
                            str(model_path),
                            device_map=self.device if self.device == "cuda" else None
                        )
                    elif task_type == self.TASK_OBJECT_DETECTION:
                        processor = DetrImageProcessor.from_pretrained(str(model_path))
                        model = DetrForObjectDetection.from_pretrained(
                            str(model_path),
                            device_map=self.device if self.device == "cuda" else None
                        )
                    elif task_type == self.TASK_SEGMENTATION:
                        processor = AutoImageProcessor.from_pretrained(str(model_path))
                        model = AutoModelForSemanticSegmentation.from_pretrained(
                            str(model_path),
                            device_map=self.device if self.device == "cuda" else None
                        )
                    
                    self.fine_tuned_models[(task_name, task_type)] = (processor, model)
                    logger.info(f"Loaded fine-tuned {task_type} model for {task_name}")
                except Exception as e:
                    logger.error(f"Error loading fine-tuned model for {task_name} ({task_type}): {str(e)}")
    
    def classify_image(
        self, 
        image_path: str, 
        task_name: str = MODEL_FOREST_MONITORING,
        use_fine_tuned: bool = True,
        top_k: int = 3
    ) -> List[Dict[str, Union[str, float]]]:
        """
        Classify an image for a specific task.
        
        Args:
            image_path: Path to the image file
            task_name: Name of the task (e.g., 'forest_monitoring', 'tribal_art')
            use_fine_tuned: Whether to use fine-tuned models if available
            top_k: Number of top predictions to return
            
        Returns:
            List of dictionaries with class labels and confidence scores
        """
        if task_name not in self.class_labels:
            raise ValueError(f"Task {task_name} not supported")
            
        # Load image
        try:
            image = Image.open(image_path).convert("RGB")
            logger.info(f"Loaded image: {image_path}")
        except Exception as e:
            logger.error(f"Error loading image: {str(e)}")
            raise
        
        # Use fine-tuned model if available and requested
        if use_fine_tuned and (task_name, self.TASK_CLASSIFICATION) in self.fine_tuned_models:
            processor, model = self.fine_tuned_models[(task_name, self.TASK_CLASSIFICATION)]
            logger.info(f"Using fine-tuned classification model for {task_name}")
        else:
            processor = self.cls_processor
            model = self.cls_model
            logger.info("Using base classification model")
        
        # Process image
        inputs = processor(images=image, return_tensors="pt").to(self.device)
        
        # Generate predictions
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            probabilities = torch.nn.functional.softmax(logits, dim=1)[0]
            
        # Get top-k predictions
        values, indices = torch.topk(probabilities, min(top_k, len(self.class_labels[task_name])))
        
        # Convert to list of dictionaries
        predictions = []
        for i, (value, index) in enumerate(zip(values.cpu().numpy(), indices.cpu().numpy())):
            if index < len(self.class_labels[task_name]):
                label = self.class_labels[task_name][index]
                predictions.append({
                    "label": label,
                    "confidence": float(value)
                })
        
        logger.info(f"Classification complete")
        return predictions
    
    def detect_objects(
        self, 
        image_path: str, 
        task_name: str = MODEL_FOREST_MONITORING,
        use_fine_tuned: bool = True,
        threshold: float = 0.5
    ) -> List[Dict[str, Union[str, float, List[float]]]]:
        """
        Detect objects in an image for a specific task.
        
        Args:
            image_path: Path to the image file
            task_name: Name of the task (e.g., 'forest_monitoring', 'wildlife')
            use_fine_tuned: Whether to use fine-tuned models if available
            threshold: Confidence threshold for detections
            
        Returns:
            List of dictionaries with class labels, confidence scores, and bounding boxes
        """
        if task_name not in self.class_labels:
            raise ValueError(f"Task {task_name} not supported")
            
        # Load image
        try:
            image = Image.open(image_path).convert("RGB")
            logger.info(f"Loaded image: {image_path}")
        except Exception as e:
            logger.error(f"Error loading image: {str(e)}")
            raise
        
        # Use fine-tuned model if available and requested
        if use_fine_tuned and (task_name, self.TASK_OBJECT_DETECTION) in self.fine_tuned_models:
            processor, model = self.fine_tuned_models[(task_name, self.TASK_OBJECT_DETECTION)]
            logger.info(f"Using fine-tuned object detection model for {task_name}")
        else:
            processor = self.det_processor
            model = self.det_model
            logger.info("Using base object detection model")
        
        # Process image
        inputs = processor(images=image, return_tensors="pt").to(self.device)
        
        # Generate predictions
        with torch.no_grad():
            outputs = model(**inputs)
            
        # Convert outputs to COCO API
        target_sizes = torch.tensor([image.size[::-1]])
        results = processor.post_process_object_detection(
            outputs, 
            target_sizes=target_sizes, 
            threshold=threshold
        )[0]
        
        # Convert to list of dictionaries
        detections = []
        for score, label, box in zip(results["scores"], results["labels"], results["boxes"]):
            if label < len(model.config.id2label):
                model_label = model.config.id2label[label.item()]
                # Map model label to task-specific label if possible
                if model_label in self.class_labels[task_name]:
                    label_text = model_label
                else:
                    # Use a default mapping or the original label
                    label_text = model_label
                    
                detections.append({
                    "label": label_text,
                    "confidence": float(score),
                    "box": box.tolist()
                })
        
        logger.info(f"Object detection complete")
        return detections
    
    def segment_image(
        self, 
        image_path: str, 
        task_name: str = MODEL_FOREST_MONITORING,
        use_fine_tuned: bool = True,
        output_path: Optional[str] = None
    ) -> Union[str, np.ndarray]:
        """
        Perform semantic segmentation on an image for a specific task.
        
        Args:
            image_path: Path to the image file
            task_name: Name of the task (e.g., 'forest_monitoring', 'agriculture')
            use_fine_tuned: Whether to use fine-tuned models if available
            output_path: Path to save the segmentation mask (optional)
            
        Returns:
            Path to the output file if output_path is provided, otherwise the segmentation mask
        """
        if task_name not in self.class_labels:
            raise ValueError(f"Task {task_name} not supported")
            
        # Load image
        try:
            image = Image.open(image_path).convert("RGB")
            logger.info(f"Loaded image: {image_path}")
        except Exception as e:
            logger.error(f"Error loading image: {str(e)}")
            raise
        
        # Use fine-tuned model if available and requested
        if use_fine_tuned and (task_name, self.TASK_SEGMENTATION) in self.fine_tuned_models:
            processor, model = self.fine_tuned_models[(task_name, self.TASK_SEGMENTATION)]
            logger.info(f"Using fine-tuned segmentation model for {task_name}")
        else:
            processor = self.seg_processor
            model = self.seg_model
            logger.info("Using base segmentation model")
        
        # Process image
        inputs = processor(images=image, return_tensors="pt").to(self.device)
        
        # Generate predictions
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            
        # Get segmentation mask
        upsampled_logits = torch.nn.functional.interpolate(
            logits,
            size=image.size[::-1],
            mode="bilinear",
            align_corners=False
        )
        seg_mask = upsampled_logits.argmax(dim=1)[0].cpu().numpy()
        
        # Convert to colored segmentation mask
        colored_mask = self._create_colored_mask(seg_mask, len(self.class_labels[task_name]))
        
        # Save to file if output_path is provided
        if output_path:
            cv2.imwrite(output_path, cv2.cvtColor(colored_mask, cv2.COLOR_RGB2BGR))
            logger.info(f"Saved segmentation mask to {output_path}")
            return output_path
        
        logger.info(f"Segmentation complete")
        return colored_mask
    
    def _create_colored_mask(self, mask: np.ndarray, num_classes: int) -> np.ndarray:
        """Create a colored segmentation mask."""
        # Generate random colors for each class
        np.random.seed(42)  # For reproducibility
        colors = np.random.randint(0, 255, size=(num_classes, 3), dtype=np.uint8)
        
        # Create colored mask
        colored_mask = np.zeros((mask.shape[0], mask.shape[1], 3), dtype=np.uint8)
        for i in range(num_classes):
            colored_mask[mask == i] = colors[i]
            
        return colored_mask
    
    def fine_tune(
        self, 
        dataset_path: str, 
        task_name: str,
        task_type: str,
        output_dir: Optional[str] = None,
        num_train_epochs: int = 3,
        batch_size: int = 8,
        learning_rate: float = 5e-5
    ) -> None:
        """
        Fine-tune a model for a specific task and task type.
        
        Args:
            dataset_path: Path to the dataset (CSV or JSON)
            task_name: Name of the task (e.g., 'forest_monitoring', 'tribal_art')
            task_type: Type of task ('classification', 'object_detection', 'segmentation')
            output_dir: Directory to save the fine-tuned model
            num_train_epochs: Number of training epochs
            batch_size: Training batch size
            learning_rate: Learning rate for training
        """
        if task_name not in self.class_labels:
            raise ValueError(f"Task {task_name} not supported")
            
        if task_type not in [self.TASK_CLASSIFICATION, self.TASK_OBJECT_DETECTION, self.TASK_SEGMENTATION]:
            raise ValueError(f"Task type {task_type} not supported")
            
        logger.info(f"Fine-tuning {task_type} model for {task_name}")
        
        # Set output directory
        if output_dir is None:
            output_dir = str(self.model_dir / f"fine_tuned_{task_name}_{task_type}")
            
        # Implementation of fine-tuning logic would go here
        # This is a placeholder for the actual implementation
        logger.info(f"Fine-tuning for {task_name} ({task_type}) would be implemented here")
        logger.info(f"Using dataset: {dataset_path}")
        logger.info(f"Output directory: {output_dir}")
        
        # In a real implementation, this would:
        # 1. Load and preprocess the dataset
        # 2. Set up training arguments
        # 3. Train the model
        # 4. Save the fine-tuned model
        # 5. Load the fine-tuned model into self.fine_tuned_models
        
        logger.info(f"Fine-tuning placeholder complete")
    
    def monitor_forest_changes(
        self, 
        before_image_path: str,
        after_image_path: str,
        output_path: Optional[str] = None
    ) -> Union[str, Dict[str, Any]]:
        """
        Monitor forest changes between two images taken at different times.
        
        Args:
            before_image_path: Path to the 'before' image
            after_image_path: Path to the 'after' image
            output_path: Path to save the visualization (optional)
            
        Returns:
            Path to the output file if output_path is provided, otherwise a dictionary with change metrics
        """
        logger.info(f"Monitoring forest changes")
        logger.info(f"Before image: {before_image_path}")
        logger.info(f"After image: {after_image_path}")
        
        # Load images
        try:
            before_image = cv2.imread(before_image_path)
            after_image = cv2.imread(after_image_path)
            
            if before_image is None or after_image is None:
                raise ValueError("Failed to load images")
                
            # Convert to RGB
            before_image = cv2.cvtColor(before_image, cv2.COLOR_BGR2RGB)
            after_image = cv2.cvtColor(after_image, cv2.COLOR_BGR2RGB)
            
            # Resize if dimensions don't match
            if before_image.shape != after_image.shape:
                after_image = cv2.resize(after_image, (before_image.shape[1], before_image.shape[0]))
                
            logger.info("Successfully loaded and processed images")
        except Exception as e:
            logger.error(f"Error loading or processing images: {str(e)}")
            raise
        
        # Calculate difference
        diff = cv2.absdiff(before_image, after_image)
        
        # Convert to grayscale for thresholding
        gray_diff = cv2.cvtColor(diff, cv2.COLOR_RGB2GRAY)
        
        # Apply threshold to get binary mask of changes
        _, thresh = cv2.threshold(gray_diff, 30, 255, cv2.THRESH_BINARY)
        
        # Find contours of changed regions
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Create visualization
        visualization = after_image.copy()
        cv2.drawContours(visualization, contours, -1, (0, 255, 0), 2)
        
        # Calculate change metrics
        total_pixels = thresh.shape[0] * thresh.shape[1]
        changed_pixels = np.sum(thresh > 0)
        change_percentage = (changed_pixels / total_pixels) * 100
        
        # Create change metrics dictionary
        change_metrics = {
            "change_percentage": change_percentage,
            "changed_area_pixels": int(changed_pixels),
            "total_area_pixels": int(total_pixels),
            "num_changed_regions": len(contours)
        }
        
        # Save visualization if output_path is provided
        if output_path:
            cv2.imwrite(output_path, cv2.cvtColor(visualization, cv2.COLOR_RGB2BGR))
            logger.info(f"Saved change visualization to {output_path}")
            return output_path
        
        logger.info(f"Forest change monitoring complete")
        return change_metrics
    
    def recognize_tribal_art(
        self, 
        image_path: str,
        top_k: int = 3
    ) -> List[Dict[str, Union[str, float]]]:
        """
        Recognize tribal art style in an image.
        
        Args:
            image_path: Path to the image file
            top_k: Number of top predictions to return
            
        Returns:
            List of dictionaries with art style labels and confidence scores
        """
        # This is a wrapper around classify_image for tribal art
        return self.classify_image(
            image_path=image_path,
            task_name=self.MODEL_TRIBAL_ART,
            use_fine_tuned=True,
            top_k=top_k
        )
    
    def save_model_metadata(self):
        """Save metadata about available models and their capabilities."""
        metadata = {
            "classification_model": self.classification_model_name,
            "detection_model": self.detection_model_name,
            "segmentation_model": self.segmentation_model_name,
            "supported_tasks": list(self.class_labels.keys()),
            "fine_tuned_models": [
                {"task_name": task_name, "task_type": task_type}
                for (task_name, task_type) in self.fine_tuned_models.keys()
            ],
            "device": self.device,
            "offline_mode": self.offline_mode
        }
        
        with open(self.model_dir / "vision_model_metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)
            
        logger.info(f"Saved model metadata to {self.model_dir / 'vision_model_metadata.json'}")
    
    def get_supported_tasks(self) -> List[str]:
        """Return a list of supported tasks."""
        return list(self.class_labels.keys())
    
    def get_class_labels(self, task_name: str) -> List[str]:
        """Return class labels for a specific task."""
        if task_name not in self.class_labels:
            raise ValueError(f"Task {task_name} not supported")
        return self.class_labels[task_name]
