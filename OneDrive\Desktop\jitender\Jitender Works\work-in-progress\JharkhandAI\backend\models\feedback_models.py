from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class Feedback(Base):
    __tablename__ = "feedbacks"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, nullable=True)
    feature_id = Column(String, nullable=False)
    rating = Column(Integer, nullable=False)
    comment = Column(String, nullable=True)
    sentiment = Column(String, nullable=True)
    tags = Column(JSON, nullable=True)
    metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

class FeatureUsage(Base):
    __tablename__ = "feature_usages"

    id = Column(Integer, primary_key=True, index=True)
    feature_id = Column(String, nullable=False)
    user_id = Column(String, nullable=True)
    action = Column(String, nullable=False)
    duration = Column(Float, nullable=True)
    success = Column(Boolean, default=True)
    error_type = Column(String, nullable=True)
    metadata = Column(JSON, nullable=True)
    timestamp = Column(DateTime, default=datetime.now)

class UserSatisfactionSummary(Base):
    __tablename__ = "user_satisfaction_summaries"

    id = Column(Integer, primary_key=True, index=True)
    period = Column(String, nullable=False)  # daily, weekly, monthly
    overall_rating = Column(Float, nullable=False)
    feature_ratings = Column(JSON, nullable=False)
    sentiment_distribution = Column(JSON, nullable=False)
    total_feedback_count = Column(Integer, nullable=False)
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.now)