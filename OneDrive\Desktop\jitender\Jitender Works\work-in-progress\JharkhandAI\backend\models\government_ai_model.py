from typing import List, Dict, Optional
from datetime import datetime
import json
import logging
from database import get_db
from models.government_policy_model import PolicyAnalytics

class GovernmentAI:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.scheme_keywords = {
            'agriculture': ['farming', 'crop', 'irrigation', 'seeds', 'fertilizer'],
            'education': ['school', 'college', 'scholarship', 'student', 'education'],
            'healthcare': ['medical', 'hospital', 'health', 'treatment', 'insurance'],
            'employment': ['job', 'work', 'career', 'skill', 'training'],
            'infrastructure': ['road', 'electricity', 'water', 'housing', 'construction']
        }

    async def process_query(self, query: str, context: Optional[Dict] = None) -> Dict:
        """Process user query and return relevant government information"""
        try:
            # Analyze query intent and extract relevant information
            intent = self._analyze_intent(query)
            relevant_schemes = self._find_relevant_schemes(query)
            
            # Generate response based on intent and available information
            response = self._generate_response(intent, relevant_schemes, context)
            
            # Store interaction for analytics
            self._store_interaction(query, intent, response)
            
            return response
        except Exception as e:
            self.logger.error(f"Error processing government query: {e}")
            return {"error": "Unable to process query at this time"}

    def _analyze_intent(self, query: str) -> str:
        """Analyze the intent of user query"""
        query_lower = query.lower()
        
        # Identify primary intent based on keywords
        if any(word in query_lower for word in ['how', 'apply', 'process']):
            return 'application_process'
        elif any(word in query_lower for word in ['eligible', 'qualify', 'criteria']):
            return 'eligibility_check'
        elif any(word in query_lower for word in ['status', 'track', 'application']):
            return 'status_check'
        elif any(word in query_lower for word in ['document', 'require', 'need']):
            return 'document_requirements'
        else:
            return 'general_info'

    def _find_relevant_schemes(self, query: str) -> List[str]:
        """Find relevant government schemes based on query"""
        query_lower = query.lower()
        relevant_categories = []
        
        # Match query with scheme categories
        for category, keywords in self.scheme_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                relevant_categories.append(category)
        
        return relevant_categories

    def _generate_response(self, intent: str, schemes: List[str], context: Optional[Dict]) -> Dict:
        """Generate appropriate response based on intent and context"""
        response = {
            'intent': intent,
            'schemes': schemes,
            'message': '',
            'actions': []
        }
        
        # Generate contextual response
        if intent == 'application_process':
            response['message'] = self._get_application_process(schemes)
            response['actions'] = ['view_application_form', 'download_guidelines']
        elif intent == 'eligibility_check':
            response['message'] = self._get_eligibility_criteria(schemes)
            response['actions'] = ['check_eligibility', 'view_detailed_criteria']
        elif intent == 'status_check':
            response['message'] = self._get_status_info(context)
            response['actions'] = ['track_application', 'contact_support']
        else:
            response['message'] = self._get_general_info(schemes)
            response['actions'] = ['view_all_schemes', 'contact_helpdesk']
        
        return response

    def _store_interaction(self, query: str, intent: str, response: Dict) -> None:
        """Store interaction for analytics and improvement"""
        try:
            db = next(get_db())
            analytics = PolicyAnalytics(
                policy_area='government_schemes',
                metrics={
                    'query': query,
                    'intent': intent,
                    'timestamp': datetime.now().isoformat()
                },
                insights={
                    'relevant_schemes': response.get('schemes', []),
                    'suggested_actions': response.get('actions', [])
                }
            )
            db.add(analytics)
            db.commit()
        except Exception as e:
            self.logger.error(f"Error storing interaction: {e}")

    def _get_application_process(self, schemes: List[str]) -> str:
        """Get application process information for given schemes"""
        return f"Here's how to apply for {', '.join(schemes)} schemes. Please follow these steps..."

    def _get_eligibility_criteria(self, schemes: List[str]) -> str:
        """Get eligibility criteria for given schemes"""
        return f"To be eligible for {', '.join(schemes)} schemes, you must meet these criteria..."

    def _get_status_info(self, context: Optional[Dict]) -> str:
        """Get status information based on context"""
        if context and context.get('application_id'):
            return f"Your application {context['application_id']} is being processed..."
        return "Please provide your application ID to check the status."

    def _get_general_info(self, schemes: List[str]) -> str:
        """Get general information about schemes"""
        return f"Here's general information about {', '.join(schemes)} schemes..."