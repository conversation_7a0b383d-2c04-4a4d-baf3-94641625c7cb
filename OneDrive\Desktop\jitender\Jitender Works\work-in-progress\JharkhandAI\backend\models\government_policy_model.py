from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, JSON, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base
import enum

class GrievanceStatus(enum.Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    RESOLVED = "resolved"
    ESCALATED = "escalated"

class GrievanceCategory(enum.Enum):
    INFRASTRUCTURE = "infrastructure"
    PUBLIC_SERVICES = "public_services"
    HEALTHCARE = "healthcare"
    EDUCATION = "education"
    EMPLOYMENT = "employment"
    AGRICULTURE = "agriculture"
    OTHER = "other"

class Grievance(Base):
    __tablename__ = "grievances"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    category = Column(Enum(GrievanceCategory))
    subject = Column(String)
    description = Column(String)
    location = Column(String)
    status = Column(Enum(GrievanceStatus), default=GrievanceStatus.PENDING)
    priority = Column(Integer, default=1)  # 1 (low) to 5 (high)
    assigned_to = Column(Integer, ForeignKey("users.id"), nullable=True)
    resolution = Column(String, nullable=True)
    attachments = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="grievances")
    assigned_official = relationship("User", foreign_keys=[assigned_to])

class PolicyAnalytics(Base):
    __tablename__ = "policy_analytics"

    id = Column(Integer, primary_key=True, index=True)
    policy_area = Column(String)
    metrics = Column(JSON)  # Store various policy performance metrics
    insights = Column(JSON)  # Store AI-generated insights
    recommendations = Column(JSON)  # Store AI-generated recommendations
    data_sources = Column(JSON)  # Track data sources used
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class GovernmentDatabaseSync(Base):
    __tablename__ = "government_database_sync"

    id = Column(Integer, primary_key=True, index=True)
    database_name = Column(String)
    last_sync_time = Column(DateTime(timezone=True))
    sync_status = Column(String)
    error_log = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())