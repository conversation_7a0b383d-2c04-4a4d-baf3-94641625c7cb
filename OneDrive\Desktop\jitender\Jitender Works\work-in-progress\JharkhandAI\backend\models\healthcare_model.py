import json
import os
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder

class HealthcareModel:
    def __init__(self):
        self.symptom_checker_model = None
        self.symptom_encoder = None
        self.disease_encoder = None
        self.symptoms_list = []
        self.diseases_list = []
        self.disease_descriptions = {}
        self.disease_precautions = {}
        
        # Load or initialize the model
        self._load_or_initialize_model()
    
    def _load_or_initialize_model(self):
        """Load the pre-trained model or initialize a new one"""
        # Define paths
        model_dir = os.path.join(os.path.dirname(__file__), '../data')
        os.makedirs(model_dir, exist_ok=True)
        
        # Load disease descriptions and precautions
        self._load_disease_data()
        
        # Initialize the symptom checker model
        self._initialize_symptom_checker()
    
    def _load_disease_data(self):
        """Load disease descriptions and precautions"""
        # In a real implementation, this would load from a database or file
        # For now, we'll define some common diseases and their details
        
        self.disease_descriptions = {
            "malaria": "Malaria is a life-threatening disease caused by parasites that are transmitted to people through the bites of infected female Anopheles mosquitoes.",
            "dengue": "Dengue is a mosquito-borne viral infection causing a severe flu-like illness that can sometimes be fatal.",
            "typhoid": "Typhoid fever is a bacterial infection that can spread throughout the body, affecting many organs.",
            "tuberculosis": "Tuberculosis (TB) is a bacterial infection that mainly affects the lungs but can also affect other parts of the body.",
            "pneumonia": "Pneumonia is an infection that inflames the air sacs in one or both lungs.",
            "jaundice": "Jaundice is a condition in which the skin, whites of the eyes and mucous membranes turn yellow because of a high level of bilirubin.",
            "diarrhea": "Diarrhea is loose, watery stools (bowel movements) that occur more frequently than usual.",
            "anemia": "Anemia is a condition in which you lack enough healthy red blood cells to carry adequate oxygen to your body's tissues.",
            "diabetes": "Diabetes is a disease that occurs when your blood glucose, also called blood sugar, is too high.",
            "hypertension": "Hypertension, also known as high blood pressure, is a long-term medical condition in which the blood pressure in the arteries is persistently elevated."
        }
        
        self.disease_precautions = {
            "malaria": [
                "Use mosquito nets while sleeping",
                "Use insect repellent",
                "Wear long-sleeved clothing",
                "Keep surroundings clean and free from stagnant water"
            ],
            "dengue": [
                "Use mosquito repellents",
                "Wear protective clothing",
                "Use mosquito nets",
                "Keep water containers covered"
            ],
            "typhoid": [
                "Drink purified water",
                "Wash hands thoroughly",
                "Avoid street food",
                "Get vaccinated"
            ],
            "tuberculosis": [
                "Cover mouth when coughing",
                "Ensure good ventilation",
                "Get vaccinated (BCG)",
                "Complete the full course of medication if infected"
            ],
            "pneumonia": [
                "Get vaccinated",
                "Practice good hygiene",
                "Don't smoke",
                "Maintain a healthy immune system"
            ],
            "jaundice": [
                "Maintain hygiene",
                "Drink clean water",
                "Avoid alcohol",
                "Get vaccinated for hepatitis"
            ],
            "diarrhea": [
                "Drink plenty of fluids",
                "Wash hands frequently",
                "Avoid contaminated food and water",
                "Maintain good hygiene"
            ],
            "anemia": [
                "Eat iron-rich foods",
                "Take iron supplements if prescribed",
                "Eat vitamin C-rich foods to enhance iron absorption",
                "Get regular check-ups"
            ],
            "diabetes": [
                "Maintain a healthy diet",
                "Exercise regularly",
                "Monitor blood sugar levels",
                "Take medications as prescribed"
            ],
            "hypertension": [
                "Reduce salt intake",
                "Exercise regularly",
                "Maintain a healthy weight",
                "Avoid smoking and limit alcohol"
            ]
        }
        
        # Define common symptoms
        self.symptoms_list = [
            "fever", "headache", "joint_pain", "fatigue", "nausea", "vomiting", 
            "abdominal_pain", "diarrhea", "cough", "shortness_of_breath", 
            "chest_pain", "sore_throat", "runny_nose", "muscle_pain", 
            "back_pain", "chills", "sweating", "loss_of_appetite", "weight_loss", 
            "yellowing_of_eyes", "dark_urine", "rash", "swollen_glands", 
            "blurred_vision", "dizziness", "confusion", "excessive_thirst", 
            "frequent_urination", "slow_healing_wounds", "numbness", "tingling"
        ]
        
        # Define diseases
        self.diseases_list = list(self.disease_descriptions.keys())
    
    def _initialize_symptom_checker(self):
        """Initialize the symptom checker model with synthetic data"""
        # Create encoders
        self.symptom_encoder = LabelEncoder()
        self.symptom_encoder.fit(self.symptoms_list)
        
        self.disease_encoder = LabelEncoder()
        self.disease_encoder.fit(self.diseases_list)
        
        # Create synthetic training data
        # In a real implementation, this would use actual medical data
        np.random.seed(42)
        
        # Number of training examples
        n_samples = 1000
        
        # Maximum number of symptoms per disease case
        max_symptoms = 10
        
        # Create synthetic data
        X = np.zeros((n_samples, len(self.symptoms_list)))
        y = np.zeros(n_samples)
        
        for i in range(n_samples):
            # Randomly select a disease
            disease_idx = np.random.randint(0, len(self.diseases_list))
            y[i] = disease_idx
            
            # Assign symptoms based on the disease
            # This is a simplified approach; in reality, the relationship would be more complex
            if self.diseases_list[disease_idx] == "malaria":
                # Malaria symptoms
                symptom_indices = self.symptom_encoder.transform(["fever", "headache", "chills", "sweating", "fatigue"])
                # Add some random symptoms
                n_random = np.random.randint(0, 3)
                random_symptoms = np.random.choice(
                    [s for s in self.symptoms_list if s not in ["fever", "headache", "chills", "sweating", "fatigue"]], 
                    n_random, replace=False
                )
                if len(random_symptoms) > 0:
                    symptom_indices = np.append(symptom_indices, self.symptom_encoder.transform(random_symptoms))
            
            elif self.diseases_list[disease_idx] == "dengue":
                # Dengue symptoms
                symptom_indices = self.symptom_encoder.transform(["fever", "headache", "joint_pain", "muscle_pain", "rash"])
                # Add some random symptoms
                n_random = np.random.randint(0, 3)
                random_symptoms = np.random.choice(
                    [s for s in self.symptoms_list if s not in ["fever", "headache", "joint_pain", "muscle_pain", "rash"]], 
                    n_random, replace=False
                )
                if len(random_symptoms) > 0:
                    symptom_indices = np.append(symptom_indices, self.symptom_encoder.transform(random_symptoms))
            
            elif self.diseases_list[disease_idx] == "typhoid":
                # Typhoid symptoms
                symptom_indices = self.symptom_encoder.transform(["fever", "headache", "abdominal_pain", "loss_of_appetite", "fatigue"])
                # Add some random symptoms
                n_random = np.random.randint(0, 3)
                random_symptoms = np.random.choice(
                    [s for s in self.symptoms_list if s not in ["fever", "headache", "abdominal_pain", "loss_of_appetite", "fatigue"]], 
                    n_random, replace=False
                )
                if len(random_symptoms) > 0:
                    symptom_indices = np.append(symptom_indices, self.symptom_encoder.transform(random_symptoms))
            
            elif self.diseases_list[disease_idx] == "tuberculosis":
                # TB symptoms
                symptom_indices = self.symptom_encoder.transform(["cough", "fever", "weight_loss", "night_sweats", "fatigue"])
                # Add some random symptoms
                n_random = np.random.randint(0, 3)
                random_symptoms = np.random.choice(
                    [s for s in self.symptoms_list if s not in ["cough", "fever", "weight_loss", "night_sweats", "fatigue"]], 
                    n_random, replace=False
                )
                if len(random_symptoms) > 0:
                    symptom_indices = np.append(symptom_indices, self.symptom_encoder.transform(random_symptoms))
            
            # ... and so on for other diseases
            else:
                # For other diseases, assign random symptoms
                n_symptoms = np.random.randint(3, max_symptoms)
                symptom_indices = np.random.choice(len(self.symptoms_list), n_symptoms, replace=False)
            
            # Set the symptoms to 1 in the feature matrix
            X[i, symptom_indices] = 1
        
        # Train a Random Forest classifier
        self.symptom_checker_model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.symptom_checker_model.fit(X, y)
    
    def check_symptoms(self, symptoms):
        """
        Check symptoms and predict possible diseases
        
        Args:
            symptoms (list): List of symptoms experienced by the patient
            
        Returns:
            dict: Prediction results with possible diseases and their probabilities
        """
        if self.symptom_checker_model is None:
            return {"error": "Model not initialized"}
        
        # Validate symptoms
        valid_symptoms = [s for s in symptoms if s in self.symptoms_list]
        invalid_symptoms = [s for s in symptoms if s not in self.symptoms_list]
        
        if not valid_symptoms:
            return {
                "error": "No valid symptoms provided",
                "invalid_symptoms": invalid_symptoms,
                "available_symptoms": self.symptoms_list
            }
        
        # Prepare input data
        X = np.zeros((1, len(self.symptoms_list)))
        for symptom in valid_symptoms:
            idx = self.symptom_encoder.transform([symptom])[0]
            X[0, idx] = 1
        
        # Get prediction probabilities
        probabilities = self.symptom_checker_model.predict_proba(X)[0]
        
        # Get top 3 diseases
        top_indices = np.argsort(probabilities)[-3:][::-1]
        top_diseases = [self.diseases_list[self.disease_encoder.inverse_transform([i])[0]] for i in top_indices]
        top_probabilities = [float(probabilities[i]) for i in top_indices]
        
        # Prepare results
        results = []
        for disease, probability in zip(top_diseases, top_probabilities):
            results.append({
                "disease": disease,
                "probability": round(probability * 100, 2),
                "description": self.disease_descriptions.get(disease, "No description available"),
                "precautions": self.disease_precautions.get(disease, ["No specific precautions available"])
            })
        
        return {
            "possible_diseases": results,
            "disclaimer": "This is not a medical diagnosis. Please consult a healthcare professional for proper diagnosis and treatment.",
            "symptoms_analyzed": valid_symptoms,
            "unrecognized_symptoms": invalid_symptoms
        }
    
    def get_all_symptoms(self):
        """Return the list of all symptoms recognized by the system"""
        return self.symptoms_list
    
    def get_disease_info(self, disease):
        """Get information about a specific disease"""
        if disease not in self.diseases_list:
            return {"error": f"Disease '{disease}' not found"}
        
        return {
            "disease": disease,
            "description": self.disease_descriptions.get(disease, "No description available"),
            "precautions": self.disease_precautions.get(disease, ["No specific precautions available"])
        }

# Singleton instance
healthcare_model = HealthcareModel()
