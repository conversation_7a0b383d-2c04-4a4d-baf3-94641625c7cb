import torch
from transformers import AutoTokenizer, AutoModel
from typing import Dict, List, Optional, Union
import logging
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from datetime import datetime

logger = logging.getLogger(__name__)

class JobModel:
    """
    AI-powered job matching and employment system with support for:
    - Intelligent job matching
    - Resume analysis and building
    - Local job board for unorganized sectors
    - Virtual job fair management
    """
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize models
        self.skill_model = self._load_skill_model()
        self.resume_model = self._load_resume_model()
        
        # Initialize tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained("bert-base-multilingual-cased")
        
        # Supported languages for local job board
        self.supported_languages = {
            "en": "English",
            "hi": "Hindi",
            "sa": "Santhali",
            "ho": "Ho",
            "mu": "<PERSON><PERSON><PERSON>",
            "ku": "<PERSON><PERSON><PERSON>",
            "kh": "Kharia"
        }
    
    def _load_skill_model(self) -> AutoModel:
        """Load pre-trained model for skill matching"""
        try:
            model = AutoModel.from_pretrained("bert-base-multilingual-cased").to(self.device)
            return model
        except Exception as e:
            logger.error(f"Error loading skill model: {str(e)}")
            raise
    
    def _load_resume_model(self) -> AutoModel:
        """Load pre-trained model for resume analysis"""
        try:
            model = AutoModel.from_pretrained("bert-base-multilingual-cased").to(self.device)
            return model
        except Exception as e:
            logger.error(f"Error loading resume model: {str(e)}")
            raise
    
    def match_jobs(self, candidate_profile: Dict, job_listings: List[Dict], 
                   threshold: float = 0.7) -> List[Dict]:
        """Match candidate with suitable jobs using AI"""
        try:
            # Extract candidate skills and experience
            candidate_skills = self._extract_skills(candidate_profile["skills"])
            candidate_exp = self._encode_experience(candidate_profile["experience"])
            
            matches = []
            for job in job_listings:
                # Calculate skill match score
                required_skills = self._extract_skills(job["required_skills"])
                skill_score = self._calculate_similarity(candidate_skills, required_skills)
                
                # Calculate experience match score
                job_exp = self._encode_experience(job["required_experience"])
                exp_score = self._calculate_similarity(candidate_exp, job_exp)
                
                # Calculate overall match score
                match_score = (skill_score + exp_score) / 2
                
                if match_score >= threshold:
                    matches.append({
                        "job_id": job["id"],
                        "title": job["title"],
                        "match_score": float(match_score),
                        "skill_match": float(skill_score),
                        "experience_match": float(exp_score)
                    })
            
            return sorted(matches, key=lambda x: x["match_score"], reverse=True)
        
        except Exception as e:
            logger.error(f"Error in job matching: {str(e)}")
            return []
    
    def analyze_resume(self, resume_text: str, target_job: Optional[Dict] = None) -> Dict:
        """Analyze resume and provide improvement suggestions"""
        try:
            # Extract key components
            skills = self._extract_skills(resume_text)
            experience = self._extract_experience(resume_text)
            education = self._extract_education(resume_text)
            
            analysis = {
                "skills_identified": skills,
                "experience_summary": experience,
                "education_summary": education,
                "completeness_score": self._calculate_completeness(skills, experience, education),
                "improvement_suggestions": []
            }
            
            # If target job provided, analyze match and suggest improvements
            if target_job:
                job_skills = self._extract_skills(target_job["required_skills"])
                missing_skills = [s for s in job_skills if s not in skills]
                if missing_skills:
                    analysis["improvement_suggestions"].append({
                        "type": "missing_skills",
                        "details": missing_skills
                    })
            
            return analysis
        
        except Exception as e:
            logger.error(f"Error in resume analysis: {str(e)}")
            return {"error": str(e)}
    
    def create_virtual_job_fair(self, fair_details: Dict) -> Dict:
        """Create and manage virtual job fair"""
        try:
            fair_id = self._generate_fair_id()
            
            virtual_fair = {
                "fair_id": fair_id,
                "title": fair_details["title"],
                "date": fair_details["date"],
                "employers": [],
                "candidates": [],
                "sessions": [],
                "status": "scheduled"
            }
            
            return virtual_fair
        
        except Exception as e:
            logger.error(f"Error creating virtual job fair: {str(e)}")
            return {"error": str(e)}
    
    def post_local_job(self, job_details: Dict, language: str = "en") -> Dict:
        """Post job to local job board with language support"""
        try:
            if language not in self.supported_languages:
                return {"error": f"Language {language} not supported"}
            
            job_post = {
                "id": self._generate_job_id(),
                "title": job_details["title"],
                "description": job_details["description"],
                "location": job_details["location"],
                "sector": job_details["sector"],
                "language": language,
                "posted_date": datetime.now().isoformat(),
                "status": "active"
            }
            
            return job_post
        
        except Exception as e:
            logger.error(f"Error posting local job: {str(e)}")
            return {"error": str(e)}
    
    def _extract_skills(self, text: str) -> List[str]:
        """Extract skills from text using NLP"""
        try:
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the skill model
            with torch.no_grad():
                outputs = self.skill_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            # Extract skills using the embeddings
            # For now, using a simple keyword-based approach
            # This can be enhanced with a more sophisticated ML model
            words = text.lower().split()
            common_skills = [
                "python", "java", "javascript", "react", "node.js", "sql",
                "machine learning", "data analysis", "project management",
                "communication", "leadership", "problem solving",
                "microsoft office", "sales", "marketing", "customer service",
                "accounting", "design", "research", "writing"
            ]
            
            extracted_skills = []
            for word in words:
                if word in common_skills:
                    extracted_skills.append(word)
            
            # Remove duplicates and sort
            extracted_skills = sorted(list(set(extracted_skills)))
            
            return extracted_skills
        except Exception as e:
            logger.error(f"Error extracting skills: {str(e)}")
            return []
    
    def _extract_experience(self, text: str) -> List[Dict]:
        """Extract work experience from text"""
        try:
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            # Split text into sentences
            sentences = text.split('.')
            experience_entries = []
            
            # Keywords for experience detection
            exp_keywords = ['worked', 'work', 'experience', 'position', 'role', 'job']
            date_patterns = ['20\d{2}', '19\d{2}', 'present', 'current']
            
            for sentence in sentences:
                sentence = sentence.strip().lower()
                if any(keyword in sentence for keyword in exp_keywords):
                    # Extract dates
                    dates = []
                    for pattern in date_patterns:
                        if pattern in sentence:
                            dates.append(pattern)
                    
                    # Extract role/title
                    role = 'Unknown'
                    if 'as' in sentence:
                        role = sentence.split('as')[-1].strip()
                    
                    experience_entries.append({
                        'role': role,
                        'dates': dates,
                        'description': sentence
                    })
            
            return experience_entries
        except Exception as e:
            logger.error(f"Error extracting experience: {str(e)}")
            return []
    
    def _extract_education(self, text: str) -> List[Dict]:
        """Extract education details from text"""
        try:
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            # Split text into sentences
            sentences = text.split('.')
            education_entries = []
            
            # Keywords for education detection
            edu_keywords = ['degree', 'university', 'college', 'school', 'education', 'graduated', 'studied']
            degree_types = ['phd', 'master', 'bachelor', 'diploma', 'certificate', 'mba', 'btech', 'mtech']
            
            for sentence in sentences:
                sentence = sentence.strip().lower()
                if any(keyword in sentence for keyword in edu_keywords):
                    # Extract degree type
                    degree = 'Unknown'
                    for d_type in degree_types:
                        if d_type in sentence:
                            degree = d_type.upper()
                    
                    # Extract institution
                    institution = 'Unknown'
                    if 'from' in sentence:
                        institution = sentence.split('from')[-1].strip()
                    
                    # Extract year
                    year = None
                    year_pattern = '20\\d{2}|19\\d{2}'
                    if year_pattern in sentence:
                        year = year_pattern
                    
                    education_entries.append({
                        'degree': degree,
                        'institution': institution,
                        'year': year,
                        'description': sentence
                    })
            
            return education_entries
        except Exception as e:
            logger.error(f"Error extracting education: {str(e)}")
            return []
    
    def _calculate_similarity(self, vec1: torch.Tensor, vec2: torch.Tensor) -> float:
        """Calculate cosine similarity between two vectors"""
        return float(cosine_similarity(vec1.reshape(1, -1), vec2.reshape(1, -1))[0][0])
    
    def _calculate_completeness(self, skills: List[str], experience: List[Dict], 
                               education: List[Dict]) -> float:
        """Calculate resume completeness score"""
        try:
            score = 0.0
            total_weight = 100.0
            
            # Skills score (30%)
            skills_weight = 30.0
            min_skills = 3
            max_skills = 10
            if len(skills) >= min_skills:
                skills_score = min(len(skills), max_skills) / max_skills
                score += skills_weight * skills_score
            
            # Experience score (40%)
            exp_weight = 40.0
            min_exp = 1
            max_exp = 5
            if len(experience) >= min_exp:
                exp_score = min(len(experience), max_exp) / max_exp
                score += exp_weight * exp_score
            
            # Education score (30%)
            edu_weight = 30.0
            min_edu = 1
            max_edu = 3
            if len(education) >= min_edu:
                edu_score = min(len(education), max_edu) / max_edu
                score += edu_weight * edu_score
            
            # Normalize score to 0-1 range
            normalized_score = score / total_weight
            
            return round(normalized_score, 2)
        except Exception as e:
            logger.error(f"Error calculating completeness: {str(e)}")
            return 0.0
    
    def _generate_fair_id(self) -> str:
        """Generate unique ID for virtual job fair"""
        return f"fair_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def _generate_job_id(self) -> str:
        """Generate unique ID for job posting"""
        return f"job_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[Dict]]) -> torch.Tensor:
        """Encode experience data into vector representation"""
        try:
            # Convert experience to text if it's a list of dictionaries
            if isinstance(experience, list):
                text = ' '.join([f"{exp['role']} {exp['description']}" for exp in experience])
            else:
                text = experience
            
            # Tokenize and encode the text
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get embeddings from the model
            with torch.no_grad():
                outputs = self.resume_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)
            
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding experience: {str(e)}")
            # Return zero tensor as fallback
            return torch.zeros(768).to(self.device)
    
    def _encode_experience(self, experience: Union[str, List[