import os
import torch
from transformers import AutoModelForSeq2SeqLM, AutoTokenizer, Trainer, TrainingArguments
from datasets import Dataset
import pandas as pd
import logging
from typing import List, Dict, Any, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LanguageModel:
    """
    Class for fine-tuning and using language models for local languages of Jharkhand.
    Supports translation, text generation, and other NLP tasks.
    """
    
    def __init__(self, model_name="google/mt5-small", cache_dir="./models/cache"):
        """
        Initialize the language model with a pre-trained model.
        
        Args:
            model_name: Name of the pre-trained model to use
            cache_dir: Directory to cache models
        """
        self.model_name = model_name
        self.cache_dir = cache_dir
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
        
        # Load tokenizer and model
        logger.info(f"Loading model {model_name} on {self.device}")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name, cache_dir=cache_dir)
        self.model = AutoModelForSeq2SeqLM.from_pretrained(model_name, cache_dir=cache_dir)
        self.model.to(self.device)
        
        # Language code mapping for Jharkhand languages
        self.language_codes = {
            "en": "English",
            "hi": "Hindi",
            "sa": "Santhali",
            "ho": "Ho",
            "mu": "Mundari",
            "ku": "Kurukh",
            "kh": "Kharia"
        }
        
        # Fine-tuned model paths
        self.fine_tuned_models = {}
        
    def translate(self, text: str, source_lang: str, target_lang: str) -> str:
        """
        Translate text from source language to target language.
        
        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code
            
        Returns:
            Translated text
        """
        # Check if we have a fine-tuned model for this language pair
        model_key = f"{source_lang}-{target_lang}"
        
        if model_key in self.fine_tuned_models:
            # Use fine-tuned model
            model = self.fine_tuned_models[model_key]["model"]
            tokenizer = self.fine_tuned_models[model_key]["tokenizer"]
        else:
            # Use base model
            model = self.model
            tokenizer = self.tokenizer
        
        # Prepare input for the model
        prefix = f"translate {source_lang} to {target_lang}: "
        input_text = prefix + text
        
        # Tokenize input
        inputs = tokenizer(input_text, return_tensors="pt", padding=True).to(self.device)
        
        # Generate translation
        outputs = model.generate(
            inputs.input_ids,
            max_length=512,
            num_beams=4,
            early_stopping=True
        )
        
        # Decode and return translation
        translation = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        return translation
    
    def fine_tune(self, 
                 source_lang: str, 
                 target_lang: str, 
                 dataset_path: str,
                 output_dir: str = "./models/fine_tuned",
                 epochs: int = 3,
                 batch_size: int = 8) -> Dict[str, Any]:
        """
        Fine-tune the model for a specific language pair.
        
        Args:
            source_lang: Source language code
            target_lang: Target language code
            dataset_path: Path to the dataset CSV file with 'source' and 'target' columns
            output_dir: Directory to save the fine-tuned model
            epochs: Number of training epochs
            batch_size: Training batch size
            
        Returns:
            Dictionary with training metrics
        """
        # Create output directory
        model_dir = os.path.join(output_dir, f"{source_lang}-{target_lang}")
        os.makedirs(model_dir, exist_ok=True)
        
        # Load dataset
        logger.info(f"Loading dataset from {dataset_path}")
        df = pd.read_csv(dataset_path)
        
        # Prepare dataset
        def preprocess_function(examples):
            prefix = f"translate {source_lang} to {target_lang}: "
            inputs = [prefix + text for text in examples["source"]]
            targets = examples["target"]
            
            model_inputs = self.tokenizer(inputs, max_length=128, truncation=True, padding="max_length")
            with self.tokenizer.as_target_tokenizer():
                labels = self.tokenizer(targets, max_length=128, truncation=True, padding="max_length")
            
            model_inputs["labels"] = labels["input_ids"]
            return model_inputs
        
        # Convert DataFrame to Hugging Face Dataset
        dataset = Dataset.from_pandas(df)
        
        # Split dataset into train and validation
        dataset = dataset.train_test_split(test_size=0.1)
        
        # Preprocess dataset
        tokenized_datasets = dataset.map(preprocess_function, batched=True)
        
        # Define training arguments
        training_args = TrainingArguments(
            output_dir=model_dir,
            evaluation_strategy="epoch",
            learning_rate=5e-5,
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            weight_decay=0.01,
            save_total_limit=3,
            num_train_epochs=epochs,
            predict_with_generate=True,
            fp16=torch.cuda.is_available(),
            report_to="none"
        )
        
        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=tokenized_datasets["train"],
            eval_dataset=tokenized_datasets["test"],
            tokenizer=self.tokenizer
        )
        
        # Train model
        logger.info(f"Fine-tuning model for {source_lang} to {target_lang}")
        trainer.train()
        
        # Save model
        trainer.save_model(model_dir)
        self.tokenizer.save_pretrained(model_dir)
        
        # Load fine-tuned model
        self.load_fine_tuned_model(source_lang, target_lang, model_dir)
        
        # Evaluate model
        metrics = trainer.evaluate()
        
        return {
            "model_dir": model_dir,
            "metrics": metrics,
            "language_pair": f"{source_lang}-{target_lang}"
        }
    
    def load_fine_tuned_model(self, source_lang: str, target_lang: str, model_dir: str) -> None:
        """
        Load a fine-tuned model for a specific language pair.
        
        Args:
            source_lang: Source language code
            target_lang: Target language code
            model_dir: Directory containing the fine-tuned model
        """
        model_key = f"{source_lang}-{target_lang}"
        
        logger.info(f"Loading fine-tuned model for {model_key} from {model_dir}")
        
        # Load tokenizer and model
        tokenizer = AutoTokenizer.from_pretrained(model_dir)
        model = AutoModelForSeq2SeqLM.from_pretrained(model_dir)
        model.to(self.device)
        
        # Store model and tokenizer
        self.fine_tuned_models[model_key] = {
            "model": model,
            "tokenizer": tokenizer
        }
        
        logger.info(f"Fine-tuned model for {model_key} loaded successfully")
    
    def generate_text(self, prompt: str, max_length: int = 100) -> str:
        """
        Generate text based on a prompt.
        
        Args:
            prompt: Text prompt
            max_length: Maximum length of generated text
            
        Returns:
            Generated text
        """
        # Tokenize input
        inputs = self.tokenizer(prompt, return_tensors="pt", padding=True).to(self.device)
        
        # Generate text
        outputs = self.model.generate(
            inputs.input_ids,
            max_length=max_length,
            num_beams=5,
            temperature=0.7,
            top_p=0.9,
            do_sample=True,
            early_stopping=True
        )
        
        # Decode and return generated text
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        return generated_text
    
    def create_training_data(self, 
                           source_texts: List[str], 
                           target_texts: List[str], 
                           output_path: str) -> str:
        """
        Create a training dataset from source and target texts.
        
        Args:
            source_texts: List of source texts
            target_texts: List of target texts
            output_path: Path to save the dataset
            
        Returns:
            Path to the created dataset
        """
        # Create DataFrame
        df = pd.DataFrame({
            "source": source_texts,
            "target": target_texts
        })
        
        # Save to CSV
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        df.to_csv(output_path, index=False)
        
        logger.info(f"Training dataset created at {output_path}")
        
        return output_path

# Initialize language model
language_model = LanguageModel()
