from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON>, Integer, String, Text, DateTime, Float, JSON, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
import datetime
from backend.models.database_models import Base, BaseModel, User

class CrimeType(str, enum.Enum):
    THEFT = "theft"
    ASSAULT = "assault"
    CYBERCRIME = "cybercrime"
    FRAUD = "fraud"
    DOMESTIC_VIOLENCE = "domestic_violence"
    LAND_DISPUTE = "land_dispute"
    OTHER = "other"

class CaseStatus(str, enum.Enum):
    REPORTED = "reported"
    INVESTIGATING = "investigating"
    RESOLVED = "resolved"
    CLOSED = "closed"
    PENDING = "pending"

class CrimeReport(Base, BaseModel):
    """Model for crime reports and analysis"""
    __tablename__ = "crime_reports"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    crime_type = Column(String, nullable=False)
    description = Column(Text)
    location = Column(String, nullable=False)
    district = Column(String, nullable=False)
    date_of_incident = Column(DateTime, default=func.now())
    status = Column(String, default=CaseStatus.REPORTED)
    evidence_urls = Column(JSON, nullable=True)  # URLs to uploaded evidence files
    is_anonymous = Column(Boolean, default=False)
    
    # Relationships
    user = relationship("User")
    updates = relationship("CrimeReportUpdate", back_populates="crime_report")

class CrimeReportUpdate(Base, BaseModel):
    """Model for updates to crime reports"""
    __tablename__ = "crime_report_updates"
    
    crime_report_id = Column(Integer, ForeignKey("crime_reports.id"))
    update_text = Column(Text)
    updated_by = Column(String)  # Could be officer ID or system
    status = Column(String)
    
    # Relationships
    crime_report = relationship("CrimeReport", back_populates="updates")

class TrafficIncident(Base, BaseModel):
    """Model for traffic incidents and management"""
    __tablename__ = "traffic_incidents"
    
    incident_type = Column(String)  # accident, congestion, roadblock, etc.
    description = Column(Text)
    location = Column(String, nullable=False)
    district = Column(String, nullable=False)
    severity = Column(Integer)  # 1-5 scale
    status = Column(String, default="active")  # active, resolved
    reported_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relationships
    user = relationship("User")

class LegalAssistance(Base, BaseModel):
    """Model for legal assistance requests"""
    __tablename__ = "legal_assistance"
    
    user_id = Column(Integer, ForeignKey("users.id"))
    case_type = Column(String)  # land_dispute, tribal_rights, etc.
    description = Column(Text)
    district = Column(String, nullable=False)
    status = Column(String, default="pending")  # pending, assigned, in_progress, resolved
    assigned_to = Column(String, nullable=True)  # Legal aid provider ID or name
    
    # Relationships
    user = relationship("User")
    documents = relationship("LegalDocument", back_populates="legal_case")
    updates = relationship("LegalCaseUpdate", back_populates="legal_case")

class LegalDocument(Base, BaseModel):
    """Model for legal documents"""
    __tablename__ = "legal_documents"
    
    legal_case_id = Column(Integer, ForeignKey("legal_assistance.id"))
    document_type = Column(String)  # land_record, identity_proof, etc.
    document_url = Column(String)
    description = Column(Text, nullable=True)
    
    # Relationships
    legal_case = relationship("LegalAssistance", back_populates="documents")

class LegalCaseUpdate(Base, BaseModel):
    """Model for updates to legal cases"""
    __tablename__ = "legal_case_updates"
    
    legal_case_id = Column(Integer, ForeignKey("legal_assistance.id"))
    update_text = Column(Text)
    updated_by = Column(String)
    
    # Relationships
    legal_case = relationship("LegalAssistance", back_populates="updates")

class CrimeHotspot(Base, BaseModel):
    """Model for crime hotspots identified by AI analysis"""
    __tablename__ = "crime_hotspots"
    
    district = Column(String, nullable=False)
    location = Column(String, nullable=False)
    crime_types = Column(JSON)  # List of common crime types in this area
    risk_level = Column(Integer)  # 1-5 scale
    description = Column(Text)
    recommendations = Column(Text)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
