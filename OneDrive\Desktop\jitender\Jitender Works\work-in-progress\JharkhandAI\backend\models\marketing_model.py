"""
Marketing AI model for MSMEs & Business Growth features.
This module contains models for smart marketing AI for local businesses.
"""

from typing import Dict, List, Optional, Union
import numpy as np
import pandas as pd
from transformers import pipeline
import random
import os
import json

class MarketingAIModel:
    """Model for providing smart marketing AI for local businesses."""
    
    def __init__(self, model_path: Optional[str] = None):
        """Initialize the marketing AI model.
        
        Args:
            model_path: Path to the pre-trained model, if available.
        """
        # Initialize NLP components
        try:
            self.text_generator = pipeline("text-generation", 
                                          model="gpt2",
                                          max_length=100)
            
            self.sentiment_analyzer = pipeline("sentiment-analysis",
                                              model="distilbert-base-uncased-finetuned-sst-2-english")
        except Exception as e:
            print(f"Error loading NLP models: {e}")
            # Fallback to dummy generators
            self.text_generator = None
            self.sentiment_analyzer = None
        
        # Load marketing templates and data
        self.templates = self._load_templates()
        self.industry_data = self._load_industry_data()
    
    def generate_marketing_strategy(self, business_profile: Dict) -> Dict:
        """Generate a marketing strategy for a business.
        
        Args:
            business_profile: Dictionary containing business profile information.
            
        Returns:
            Dictionary with marketing strategy components.
        """
        # Extract key business attributes
        business_name = business_profile.get('business_name', '')
        business_type = business_profile.get('business_type', '')
        sector = business_profile.get('sector', '')
        target_audience = business_profile.get('target_audience', '')
        location = business_profile.get('location', '')
        
        # Get sector-specific insights
        sector_insights = self._get_sector_insights(sector)
        
        # Determine appropriate channels based on business type and target audience
        channels = self._determine_channels(business_type, target_audience)
        
        # Generate content ideas
        content_ideas = self._generate_content_ideas(business_profile)
        
        # Create budget allocation
        budget = business_profile.get('budget', 10000)
        budget_allocation = self._allocate_budget(channels, budget)
        
        # Create timeline
        timeline = self._create_timeline()
        
        # Determine performance metrics
        metrics = self._determine_metrics(business_type)
        
        # Find local opportunities
        local_opportunities = self._find_local_opportunities(location)
        
        return {
            "strategy_summary": f"Comprehensive marketing strategy for {business_name} focusing on {sector} sector with emphasis on {', '.join([c['channel'] for c in channels[:2]])}.",
            "target_channels": channels,
            "content_ideas": content_ideas,
            "budget_allocation": budget_allocation,
            "timeline": timeline,
            "performance_metrics": metrics,
            "local_opportunities": local_opportunities
        }
    
    def generate_marketing_content(self, business_profile: Dict, content_type: str, platform: str, count: int = 3) -> List[Dict]:
        """Generate marketing content for a business.
        
        Args:
            business_profile: Dictionary containing business profile information.
            content_type: Type of content to generate.
            platform: Platform for the content.
            count: Number of content pieces to generate.
            
        Returns:
            List of dictionaries with generated marketing content.
        """
        # Extract key business attributes
        business_name = business_profile.get('business_name', '')
        products_services = business_profile.get('products_services', [])
        unique_selling_points = business_profile.get('unique_selling_points', [])
        target_audience = business_profile.get('target_audience', '')
        
        # Get templates for the content type and platform
        templates = self._get_content_templates(content_type, platform)
        
        # Generate content
        content_list = []
        for i in range(count):
            # Select template
            template = random.choice(templates)
            
            # Select product/service and USP
            product = products_services[i % len(products_services)] if products_services else "our products"
            usp = unique_selling_points[i % len(unique_selling_points)] if unique_selling_points else "quality service"
            
            # Generate content text
            content_text = self._generate_content_text(template, business_name, product, usp)
            
            # Generate hashtags
            hashtags = self._generate_hashtags(business_profile, content_type)
            
            # Determine call to action
            cta = self._determine_call_to_action(content_type, platform)
            
            # Estimate reach and engagement
            reach, engagement = self._estimate_performance(platform, content_type)
            
            content = {
                "content_type": content_type,
                "platform": platform,
                "content": content_text,
                "hashtags": hashtags,
                "call_to_action": cta,
                "target_audience": target_audience,
                "posting_schedule": self._recommend_posting_time(platform),
                "estimated_reach": reach,
                "estimated_engagement": engagement
            }
            
            content_list.append(content)
        
        return content_list
    
    def analyze_competitors(self, business_profile: Dict) -> Dict:
        """Analyze competitors and provide insights.
        
        Args:
            business_profile: Dictionary containing business profile information.
            
        Returns:
            Dictionary with competitor analysis and recommendations.
        """
        # Extract competitors
        competitors = business_profile.get('competitors', [])
        if not competitors:
            return {
                "message": "No competitors provided for analysis",
                "recommendations": [
                    "Identify key competitors in your area",
                    "Research their online presence and marketing strategies",
                    "Analyze their strengths and weaknesses"
                ]
            }
        
        # Generate competitor insights
        competitor_insights = []
        for competitor in competitors:
            # Generate random strengths and weaknesses for demonstration
            strengths = self._generate_random_strengths()
            weaknesses = self._generate_random_weaknesses()
            
            competitor_insights.append({
                "competitor": competitor,
                "strengths": strengths,
                "weaknesses": weaknesses,
                "opportunities": self._generate_opportunities(weaknesses, business_profile)
            })
        
        # Determine market position
        market_position = self._determine_market_position(business_profile, competitor_insights)
        
        # Generate recommendations
        recommendations = self._generate_competitive_recommendations(business_profile, competitor_insights)
        
        return {
            "competitor_insights": competitor_insights,
            "market_position": market_position,
            "recommendations": recommendations
        }
    
    def _load_templates(self) -> Dict:
        """Load marketing content templates."""
        # This would typically load from a database or file
        # Using hardcoded templates for demonstration
        return {
            "social_media": {
                "facebook": [
                    "Looking for {product}? {business_name} offers the best {usp} in town!",
                    "At {business_name}, we pride ourselves on {usp}. Check out our {product} today!",
                    "Need {product}? {business_name} has got you covered with our {usp}!"
                ],
                "instagram": [
                    "✨ Discover the difference with {business_name}'s {product} - known for {usp} ✨",
                    "🔥 {business_name} brings you the finest {product} with {usp} 🔥",
                    "💯 Experience {usp} with {business_name}'s amazing {product} 💯"
                ],
                "twitter": [
                    "Looking for quality {product}? {business_name} delivers with {usp}!",
                    "{business_name}: Where {usp} meets {product}. Visit us today!",
                    "Discover why our customers love {business_name}'s {product}. Hint: It's our {usp}!"
                ]
            },
            "email": {
                "newsletter": [
                    "Dear Valued Customer,\n\nAt {business_name}, we're excited to share our latest {product} with you. Known for {usp}, our products are designed with you in mind.\n\nVisit us today!",
                    "Hello from {business_name}!\n\nHave you tried our {product} yet? With {usp}, it's been a customer favorite. Drop by and see for yourself!\n\nBest regards,\n{business_name} Team"
                ],
                "promotional": [
                    "SPECIAL OFFER from {business_name}!\n\nFor a limited time, get amazing deals on our {product}. Experience the {usp} that sets us apart!\n\nDon't miss out!",
                    "FLASH SALE at {business_name}!\n\nOur {product}, known for {usp}, is now available at a special price. Hurry, offer ends soon!"
                ]
            },
            "blog": {
                "article": [
                    "# Why {business_name}'s {product} Stands Out\n\nIn today's competitive market, finding {product} with {usp} can be challenging. At {business_name}, we've made it our mission to deliver exactly that...",
                    "# The Secret Behind {business_name}'s Success\n\nWhen it comes to {product}, {usp} matters more than you might think. Here's how {business_name} ensures the highest standards..."
                ]
            }
        }
    
    def _load_industry_data(self) -> Dict:
        """Load industry-specific marketing data."""
        # This would typically load from a database or file
        # Using hardcoded data for demonstration
        return {
            "retail": {
                "key_channels": ["social_media", "local_seo", "email_marketing"],
                "content_focus": ["product_highlights", "promotions", "customer_testimonials"],
                "best_practices": ["regular social media updates", "loyalty programs", "seasonal campaigns"]
            },
            "manufacturing": {
                "key_channels": ["linkedin", "industry_publications", "trade_shows"],
                "content_focus": ["product_specifications", "case_studies", "industry_insights"],
                "best_practices": ["technical content marketing", "b2b partnerships", "industry event sponsorship"]
            },
            "service": {
                "key_channels": ["local_seo", "social_media", "referral_marketing"],
                "content_focus": ["service_benefits", "customer_success_stories", "how_to_guides"],
                "best_practices": ["customer review management", "service packages", "educational content"]
            },
            "food": {
                "key_channels": ["instagram", "local_seo", "food_delivery_apps"],
                "content_focus": ["food_photography", "special_offers", "behind_the_scenes"],
                "best_practices": ["visual content marketing", "loyalty programs", "influencer partnerships"]
            },
            "technology": {
                "key_channels": ["content_marketing", "social_media", "seo"],
                "content_focus": ["product_features", "industry_trends", "how_to_guides"],
                "best_practices": ["technical blog posts", "video demonstrations", "webinars"]
            }
        }
    
    def _get_sector_insights(self, sector: str) -> Dict:
        """Get marketing insights for a specific sector."""
        # Get sector data or default to retail
        sector_data = self.industry_data.get(sector.lower(), self.industry_data.get("retail", {}))
        
        return sector_data
    
    def _determine_channels(self, business_type: str, target_audience: str) -> List[Dict]:
        """Determine appropriate marketing channels."""
        # Map business types to sectors
        sector_map = {
            "retail store": "retail",
            "online shop": "retail",
            "restaurant": "food",
            "cafe": "food",
            "manufacturing": "manufacturing",
            "consulting": "service",
            "tech startup": "technology",
            "service provider": "service"
        }
        
        # Get sector or default to service
        sector = sector_map.get(business_type.lower(), "service")
        sector_data = self.industry_data.get(sector, self.industry_data.get("service", {}))
        
        # Base channels from sector data
        key_channels = sector_data.get("key_channels", ["social_media", "local_seo", "email_marketing"])
        
        # Define all possible channels with details
        all_channels = {
            "social_media": {"priority": "High", "reason": "Direct engagement with customers"},
            "local_seo": {"priority": "High", "reason": "Improve visibility in local searches"},
            "email_marketing": {"priority": "Medium", "reason": "Nurture existing customer relationships"},
            "content_marketing": {"priority": "Medium", "reason": "Establish expertise in the sector"},
            "paid_advertising": {"priority": "Medium", "reason": "Targeted reach to potential customers"},
            "influencer_marketing": {"priority": "Low", "reason": "Leverage local influencers for promotion"},
            "referral_marketing": {"priority": "Medium", "reason": "Leverage satisfied customers for growth"},
            "trade_shows": {"priority": "Low", "reason": "Industry networking and visibility"},
            "linkedin": {"priority": "Medium", "reason": "Professional networking and B2B marketing"},
            "industry_publications": {"priority": "Low", "reason": "Establish authority in the industry"},
            "food_delivery_apps": {"priority": "High", "reason": "Reach customers through delivery platforms"}
        }
        
        # Select and prioritize channels
        channels = []
        
        # First add key channels from sector data
        for channel in key_channels:
            if channel in all_channels:
                channel_info = all_channels[channel].copy()
                channel_info["channel"] = channel.replace("_", " ").title()
                channels.append(channel_info)
        
        # Then add other potentially relevant channels
        for channel, info in all_channels.items():
            if channel not in key_channels:
                # Check if channel might be relevant based on target audience
                if "young" in target_audience.lower() and channel == "social_media":
                    info = info.copy()
                    info["priority"] = "High"
                elif "professional" in target_audience.lower() and channel == "linkedin":
                    info = info.copy()
                    info["priority"] = "High"
                
                # Add if not already added
                if not any(c["channel"] == channel.replace("_", " ").title() for c in channels):
                    channel_info = info.copy()
                    channel_info["channel"] = channel.replace("_", " ").title()
                    channels.append(channel_info)
        
        # Sort by priority
        priority_order = {"High": 0, "Medium": 1, "Low": 2}
        channels.sort(key=lambda x: priority_order[x["priority"]])
        
        return channels[:5]  # Return top 5 channels
    
    def _generate_content_ideas(self, business_profile: Dict) -> List[Dict]:
        """Generate content ideas based on business profile."""
        # Extract key business attributes
        business_type = business_profile.get('business_type', '')
        sector = business_profile.get('sector', '')
        products_services = business_profile.get('products_services', [])
        
        # Get sector insights
        sector_data = self._get_sector_insights(sector)
        content_focus = sector_data.get("content_focus", ["product_highlights", "promotions", "customer_testimonials"])
        
        # Define content types and topics
        content_types = {
            "Social Media Posts": [
                "Customer success stories",
                "Behind-the-scenes",
                "Product highlights",
                "Special offers",
                "Industry news",
                "Tips and tricks",
                "Employee spotlights",
                "Customer testimonials"
            ],
            "Blog Articles": [
                "Industry trends",
                "How-to guides",
                "Customer pain points",
                "Product comparisons",
                "Expert interviews",
                "Case studies",
                "Frequently asked questions",
                "Industry insights"
            ],
            "Email Campaigns": [
                "Seasonal offers",
                "New product announcements",
                "Customer appreciation",
                "Educational content",
                "Event invitations",
                "Feedback requests",
                "Loyalty rewards",
                "Re-engagement campaigns"
            ],
            "Video Content": [
                "Product demonstrations",
                "Customer testimonials",
                "How-to tutorials",
                "Behind-the-scenes",
                "Expert interviews",
                "Company story",
                "Event highlights",
                "Q&A sessions"
            ]
        }
        
        # Generate content ideas
        content_ideas = []
        
        for content_type, topics in content_types.items():
            # Filter topics based on content focus from sector data
            relevant_topics = []
            for topic in topics:
                for focus in content_focus:
                    if focus.replace("_", " ") in topic.lower():
                        relevant_topics.append(topic)
                        break
            
            # If no relevant topics found, use some default topics
            if not relevant_topics:
                relevant_topics = topics[:3]
            
            # Add product-specific topics if products/services are provided
            if products_services:
                for product in products_services[:2]:
                    product_topic = f"{product} spotlight"
                    if product_topic not in relevant_topics:
                        relevant_topics.append(product_topic)
            
            content_ideas.append({
                "type": content_type,
                "topics": relevant_topics[:5]  # Limit to 5 topics per content type
            })
        
        return content_ideas
    
    def _allocate_budget(self, channels: List[Dict], total_budget: float) -> Dict:
        """Allocate marketing budget across channels."""
        # Define base allocation percentages based on priority
        priority_allocation = {
            "High": 0.4,
            "Medium": 0.2,
            "Low": 0.1
        }
        
        # Calculate initial allocation
        allocation = {}
        total_allocation = 0
        
        for channel in channels:
            channel_name = channel["channel"].lower().replace(" ", "_")
            priority = channel["priority"]
            allocation[channel_name] = priority_allocation[priority]
            total_allocation += allocation[channel_name]
        
        # Normalize to ensure total is 100%
        for channel in allocation:
            allocation[channel] = f"{int(allocation[channel] / total_allocation * 100)}%"
        
        return allocation
    
    def _create_timeline(self) -> Dict:
        """Create a marketing timeline."""
        return {
            "month_1": "Setup and baseline establishment",
            "month_2_3": "Content creation and initial campaigns",
            "month_4_6": "Optimization based on performance data",
            "month_7_12": "Scaling successful channels"
        }
    
    def _determine_metrics(self, business_type: str) -> List[Dict]:
        """Determine appropriate performance metrics."""
        # Base metrics for all business types
        base_metrics = [
            {"metric": "Website Traffic", "target": "30% increase in 6 months"},
            {"metric": "Conversion Rate", "target": "Improve by 15% in 6 months"},
            {"metric": "Customer Acquisition Cost", "target": "Reduce by 20% in 12 months"},
            {"metric": "Customer Retention", "target": "Improve by 25% in 12 months"}
        ]
        
        # Additional metrics based on business type
        additional_metrics = {
            "retail store": [
                {"metric": "Foot Traffic", "target": "20% increase in 3 months"},
                {"metric": "Average Transaction Value", "target": "10% increase in 6 months"}
            ],
            "online shop": [
                {"metric": "Cart Abandonment Rate", "target": "Reduce by 15% in 3 months"},
                {"metric": "Repeat Purchase Rate", "target": "Increase by 20% in 6 months"}
            ],
            "restaurant": [
                {"metric": "Table Turnover Rate", "target": "Optimize for peak hours"},
                {"metric": "Online Order Volume", "target": "30% increase in 3 months"}
            ],
            "service provider": [
                {"metric": "Lead Response Time", "target": "Reduce to under 2 hours"},
                {"metric": "Service Booking Rate", "target": "Increase by 25% in 6 months"}
            ]
        }
        
        # Get additional metrics for this business type or default to empty list
        business_metrics = additional_metrics.get(business_type.lower(), [])
        
        # Combine base and business-specific metrics
        return base_metrics + business_metrics
    
    def _find_local_opportunities(self, location: str) -> List[Dict]:
        """Find local marketing opportunities."""
        # Base opportunities for all locations
        opportunities = [
            {"opportunity": "Local Business Partnerships", "description": "Collaborate with complementary local businesses"},
            {"opportunity": "Community Events", "description": "Sponsor or participate in local community events"},
            {"opportunity": "Local Influencers", "description": "Partner with local influencers for authentic promotion"},
            {"opportunity": "Google My Business", "description": "Optimize GMB listing for local visibility"}
        ]
        
        # Add location-specific opportunities if available
        # This would typically involve a database lookup or API call
        # Using placeholder for demonstration
        
        return opportunities
    
    def _get_content_templates(self, content_type: str, platform: str) -> List[str]:
        """Get content templates for a specific type and platform."""
        # Map content_type to template category
        type_map = {
            "post": "social_media",
            "ad": "social_media",
            "article": "blog",
            "newsletter": "email",
            "promotion": "email"
        }
        
        # Get template category or default to social_media
        category = type_map.get(content_type.lower(), "social_media")
        
        # Get templates for the platform or default to first platform in category
        templates = self.templates.get(category, {})
        platform_templates = templates.get(platform.lower(), [])
        
        if not platform_templates and templates:
            # Use first available platform if specific platform not found
            platform_templates = next(iter(templates.values()), [])
        
        # Return templates or default template if none found
        if not platform_templates:
            return ["Check out {business_name}'s amazing {product} known for {usp}!"]
        
        return platform_templates
    
    def _generate_content_text(self, template: str, business_name: str, product: str, usp: str) -> str:
        """Generate content text from template."""
        # Use text generator if available
        if self.text_generator:
            try:
                prompt = template.format(business_name=business_name, product=product, usp=usp)
                generated_texts = self.text_generator(prompt, num_return_sequences=1)
                return generated_texts[0]['generated_text']
            except Exception:
                pass
        
        # Fallback to template filling
        return template.format(business_name=business_name, product=product, usp=usp)
    
    def _generate_hashtags(self, business_profile: Dict, content_type: str) -> List[str]:
        """Generate hashtags for content."""
        # Extract key business attributes
        business_name = business_profile.get('business_name', '')
        sector = business_profile.get('sector', '')
        location = business_profile.get('location', '')
        
        # Base hashtags
        hashtags = [
            f"#{sector.replace(' ', '')}",
            "#LocalBusiness",
            f"#{location.replace(' ', '')}"
        ]
        
        # Add business name hashtag
        business_hashtag = f"#{business_name.replace(' ', '')}"
        if business_hashtag not in hashtags:
            hashtags.append(business_hashtag)
        
        # Add content type specific hashtags
        if content_type.lower() == "post":
            hashtags.append("#SmallBusiness")
        elif content_type.lower() == "promotion":
            hashtags.append("#SpecialOffer")
        elif content_type.lower() == "article":
            hashtags.append("#BusinessTips")
        
        return hashtags
    
    def _determine_call_to_action(self, content_type: str, platform: str) -> str:
        """Determine appropriate call to action."""
        # CTA based on content type
        cta_map = {
            "post": "Visit our store today!",
            "ad": "Shop now and save!",
            "article": "Learn more on our website!",
            "newsletter": "Reply to this email for more information!",
            "promotion": "Limited time offer - act now!"
        }
        
        # Get CTA or default
        return cta_map.get(content_type.lower(), "Contact us for more information!")
    
    def _recommend_posting_time(self, platform: str) -> str:
        """Recommend optimal posting time."""
        # Platform-specific recommendations
        time_map = {
            "facebook": "Weekdays between 1pm and 3pm",
            "instagram": "Weekdays at 11am or 7pm",
            "twitter": "Weekdays between 9am and 11am",
            "linkedin": "Weekdays between 10am and 12pm",
            "email": "Tuesday or Thursday morning"
        }
        
        # Get recommendation or default
        return time_map.get(platform.lower(), "Weekdays during business hours")
    
    def _estimate_performance(self, platform: str, content_type: str) -> tuple:
        """Estimate reach and engagement for content."""
        # Base estimates
        base_reach = 500
        base_engagement = 0.05
        
        # Platform multipliers
        platform_reach = {
            "facebook": 1.5,
            "instagram": 2.0,
            "twitter": 1.2,
            "linkedin": 0.8,
            "email": 0.6
        }
        
        # Content type multipliers
        content_engagement = {
            "post": 1.0,
            "ad": 0.8,
            "article": 0.6,
            "newsletter": 0.7,
            "promotion": 1.2
        }
        
        # Calculate estimates
        reach_multiplier = platform_reach.get(platform.lower(), 1.0)
        engagement_multiplier = content_engagement.get(content_type.lower(), 1.0)
        
        reach = int(base_reach * reach_multiplier)
        engagement = round(base_engagement * engagement_multiplier, 3)
        
        return reach, engagement
    
    def _generate_random_strengths(self) -> List[str]:
        """Generate random strengths for competitor analysis."""
        all_strengths = [
            "Strong social media presence",
            "Effective local advertising",
            "Good customer service reputation",
            "Competitive pricing",
            "Wide product range",
            "Established brand recognition",
            "Convenient location",
            "Strong online reviews",
            "Loyalty program",
            "Innovative products/services"
        ]
        
        # Select 2-3 random strengths
        count = random.randint(2, 3)
        return random.sample(all_strengths, count)
    
    def _generate_random_weaknesses(self) -> List[str]:
        """Generate random weaknesses for competitor analysis."""
        all_weaknesses = [
            "Limited product range",
            "Higher pricing",
            "Poor online presence",
            "Inconsistent customer service",
            "Limited business hours",
            "Outdated marketing",
            "Poor location",
            "Negative reviews",
            "No loyalty program",
            "Slow to innovate"
        ]
        
        # Select 2-3 random weaknesses
        count = random.randint(2, 3)
        return random.sample(all_weaknesses, count)
    
    def _generate_opportunities(self, competitor_weaknesses: List[str], business_profile: Dict) -> List[str]:
        """Generate opportunities based on competitor weaknesses."""
        opportunities = []
        
        # Map weaknesses to opportunities
        weakness_opportunity_map = {
            "Limited product range": "Differentiate with broader product selection",
            "Higher pricing": "Offer competitive pricing or better value",
            "Poor online presence": "Establish stronger digital marketing",
            "Inconsistent customer service": "Focus on exceptional customer service",
            "Limited business hours": "Extend hours to capture more customers",
            "Outdated marketing": "Implement modern marketing strategies",
            "Poor location": "Emphasize convenience or delivery options",
            "Negative reviews": "Build positive review management strategy",
            "No loyalty program": "Develop customer loyalty initiatives",
            "Slow to innovate": "Highlight innovative products or services"
        }
        
        # Generate opportunities from weaknesses
        for weakness in competitor_weaknesses:
            if weakness in weakness_opportunity_map:
                opportunities.append(weakness_opportunity_map[weakness])
        
        # Add general opportunity if needed
        if not opportunities:
            opportunities.append("Differentiate with better customer service")
        
        return opportunities
    
    def _determine_market_position(self, business_profile: Dict, competitor_insights: List[Dict]) -> str:
        """Determine market position based on competitor analysis."""
        # Extract unique selling points
        usps = business_profile.get('unique_selling_points', [])
        
        # Extract all competitor strengths
        all_strengths = []
        for competitor in competitor_insights:
            all_strengths.extend(competitor['strengths'])
        
        # Check if USPs are unique compared to competitor strengths
        unique_advantages = []
        for usp in usps:
            is_unique = True
            for strength in all_strengths:
                if usp.lower() in strength.lower() or strength.lower() in usp.lower():
                    is_unique = False
                    break
            if is_unique:
                unique_advantages.append(usp)
        
        # Determine position based on unique advantages
        if len(unique_advantages) >= 2:
            return f"Your business has strong differentiation through {', '.join(unique_advantages[:2])}. This gives you a competitive advantage in the market."
        elif len(unique_advantages) == 1:
            return f"Your business can differentiate through {unique_advantages[0]}. Focus on this advantage in your marketing."
        else:
            return "Your business needs to develop stronger differentiation from competitors. Consider developing unique selling points."
    
    def _generate_competitive_recommendations(self, business_profile: Dict, competitor_insights: List[Dict]) -> List[str]:
        """Generate recommendations based on competitive analysis."""
        # Base recommendations
        recommendations = [
            "Focus on your unique selling points in marketing materials",
            "Target underserved segments of the market"
        ]
        
        # Extract all competitor weaknesses
        all_weaknesses = []
        for competitor in competitor_insights:
            all_weaknesses.extend(competitor['weaknesses'])
        
        # Generate recommendations based on common weaknesses
        weakness_count = {}
        for weakness in all_weaknesses:
            weakness_count[weakness] = weakness_count.get(weakness, 0) + 1
        
        # Find common weaknesses
        common_weaknesses = [w for w, c in weakness_count.items() if c > 1]
        
        # Add recommendations based on common weaknesses
        if "Poor online presence" in common_weaknesses:
            recommendations.append("Improve online presence to gain competitive advantage")
        
        if "Higher pricing" in common_weaknesses:
            recommendations.append("Emphasize value rather than competing solely on price")
        
        if "Limited product range" in common_weaknesses:
            recommendations.append("Highlight your diverse product/service offerings")
        
        if "Inconsistent customer service" in common_weaknesses:
            recommendations.append("Make exceptional customer service a cornerstone of your brand")
        
        # Add general recommendations if needed
        if len(recommendations) < 4:
            additional_recs = [
                "Develop a clear brand message that differentiates from competitors",
                "Consider price-matching for key products",
                "Implement a customer loyalty program if competitors lack one",
                "Leverage technology to improve customer experience"
            ]
            
            recommendations.extend(additional_recs[:4-len(recommendations)])
        
        return recommendations
