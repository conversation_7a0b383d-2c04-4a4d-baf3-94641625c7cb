import os
import torch
import torchaudio
import numpy as np
from transformers import Wav2Vec2ForCTC, Wav2Vec2Processor, AutoProcessor, AutoModelForSpeechSeq2Seq
import librosa
from typing import Dict, List, Optional, Union, Tuple, BinaryIO
import logging
import json
import tempfile
import subprocess
from gtts import gTTS

from ..utils.language_utils import get_language_code, get_language_name, is_tribal_language

logger = logging.getLogger(__name__)

class SpeechModel:
    """
    Speech model for speech-to-text and text-to-speech
    Supports tribal languages of Jharkhand: Santhali, Ho, Mundari, Kurukh, and Kharia
    """
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize models dictionary
        self.stt_models = {}
        self.stt_processors = {}
        
        # Define supported languages
        self.languages = {
            # Standard languages
            "en": "English",
            "hi": "Hindi",
            "bn": "Bengali",
            
            # Tribal languages
            "sa": "<PERSON><PERSON>",
            "ho": "Ho",
            "kru": "<PERSON>rukh",
            "mun": "<PERSON><PERSON><PERSON>",
            "khr": "Kharia"
        }
        
        # Define model paths for different languages
        self.stt_model_paths = {
            "en": "facebook/wav2vec2-large-960h-lv60-self",
            "hi": "ai4bharat/indicwav2vec-hindi",
            "bn": "ai4bharat/indicwav2vec-bengali",
            "sa": "ai4bharat/indicwav2vec-multilingual-dravidian"  # Best available for Santhali
            # Other tribal languages will use language detection and fallback to closest model
        }
        
        # Define voices for TTS
        self.voices = self._load_voice_config()
    
    def _load_voice_config(self) -> Dict:
        """Load voice configuration for TTS"""
        voices = {}
        
        try:
            voice_path = os.path.join(os.path.dirname(__file__), "../data/voice_config.json")
            
            if os.path.exists(voice_path):
                with open(voice_path, "r", encoding="utf-8") as f:
                    voices = json.load(f)
            else:
                # Create default voice config if file doesn't exist
                voices = {
                    "en": [
                        {"id": "en_female_1", "name": "English Female 1", "gender": "female"},
                        {"id": "en_male_1", "name": "English Male 1", "gender": "male"}
                    ],
                    "hi": [
                        {"id": "hi_female_1", "name": "Hindi Female 1", "gender": "female"},
                        {"id": "hi_male_1", "name": "Hindi Male 1", "gender": "male"}
                    ],
                    "bn": [
                        {"id": "bn_female_1", "name": "Bengali Female 1", "gender": "female"},
                        {"id": "bn_male_1", "name": "Bengali Male 1", "gender": "male"}
                    ],
                    "sa": [
                        {"id": "sa_female_1", "name": "Santhali Female 1", "gender": "female"},
                        {"id": "sa_male_1", "name": "Santhali Male 1", "gender": "male"}
                    ],
                    "ho": [
                        {"id": "ho_female_1", "name": "Ho Female 1", "gender": "female"},
                        {"id": "ho_male_1", "name": "Ho Male 1", "gender": "male"}
                    ],
                    "kru": [
                        {"id": "kru_female_1", "name": "Kurukh Female 1", "gender": "female"},
                        {"id": "kru_male_1", "name": "Kurukh Male 1", "gender": "male"}
                    ],
                    "mun": [
                        {"id": "mun_female_1", "name": "Mundari Female 1", "gender": "female"},
                        {"id": "mun_male_1", "name": "Mundari Male 1", "gender": "male"}
                    ],
                    "khr": [
                        {"id": "khr_female_1", "name": "Kharia Female 1", "gender": "female"},
                        {"id": "khr_male_1", "name": "Kharia Male 1", "gender": "male"}
                    ]
                }
                
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(voice_path), exist_ok=True)
                
                # Save default voice config
                with open(voice_path, "w", encoding="utf-8") as f:
                    json.dump(voices, f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            logger.error(f"Error loading voice configuration: {str(e)}")
        
        return voices
    
    def _load_stt_model(self, language_code: str) -> Tuple[torch.nn.Module, AutoProcessor]:
        """Load speech-to-text model for a specific language"""
        if language_code in self.stt_models and language_code in self.stt_processors:
            return self.stt_models[language_code], self.stt_processors[language_code]
        
        # Use language-specific model if available, otherwise use multilingual model
        model_path = self.stt_model_paths.get(language_code)
        
        if not model_path:
            # For tribal languages without specific models, use the closest available model
            if is_tribal_language(language_code):
                model_path = self.stt_model_paths.get("hi", "facebook/wav2vec2-large-xlsr-53")
            else:
                model_path = "facebook/wav2vec2-large-xlsr-53"  # Multilingual model
        
        try:
            processor = Wav2Vec2Processor.from_pretrained(model_path)
            model = Wav2Vec2ForCTC.from_pretrained(model_path).to(self.device)
            
            self.stt_processors[language_code] = processor
            self.stt_models[language_code] = model
            
            return model, processor
        except Exception as e:
            logger.error(f"Error loading STT model for {language_code}: {str(e)}")
            
            # Fall back to multilingual model
            processor = Wav2Vec2Processor.from_pretrained("facebook/wav2vec2-large-xlsr-53")
            model = Wav2Vec2ForCTC.from_pretrained("facebook/wav2vec2-large-xlsr-53").to(self.device)
            
            self.stt_processors[language_code] = processor
            self.stt_models[language_code] = model
            
            return model, processor
    
    def _detect_language(self, audio_path: str) -> str:
        """Detect language from audio"""
        # This is a placeholder for actual language detection
        # In a real implementation, you would use a language identification model
        
        # For now, default to Hindi as it's close to many tribal languages
        return "hi"
    
    def transcribe(self, audio_path: str, language: Optional[str] = None) -> Dict:
        """
        Transcribe audio to text
        
        Args:
            audio_path: Path to audio file
            language: Language code (optional, will be detected if not provided)
            
        Returns:
            Dictionary with transcribed text, detected language, and confidence
        """
        try:
            # Detect language if not provided
            language_code = language if language else self._detect_language(audio_path)
            
            # Load appropriate model
            model, processor = self._load_stt_model(language_code)
            
            # Load and preprocess audio
            speech_array, sampling_rate = librosa.load(audio_path, sr=16000)
            
            # Process audio with model
            inputs = processor(speech_array, sampling_rate=16000, return_tensors="pt").to(self.device)
            
            with torch.no_grad():
                logits = model(inputs.input_values).logits
            
            # Get predicted IDs and convert to text
            predicted_ids = torch.argmax(logits, dim=-1)
            transcription = processor.batch_decode(predicted_ids)[0]
            
            # Calculate confidence (mean of max probabilities)
            probs = torch.nn.functional.softmax(logits, dim=-1)
            confidence = torch.mean(torch.max(probs, dim=-1).values).item()
            
            return {
                "text": transcription,
                "language": language_code,
                "confidence": confidence
            }
        
        except Exception as e:
            logger.error(f"Error transcribing audio: {str(e)}")
            raise ValueError(f"Transcription failed: {str(e)}")
    
    def synthesize(self, text: str, language: str, voice_id: Optional[str] = None, speed: float = 1.0) -> bytes:
        """
        Synthesize text to speech
        
        Args:
            text: Text to synthesize
            language: Language code
            voice_id: Voice ID (optional)
            speed: Speech speed (default: 1.0)
            
        Returns:
            Audio data as bytes
        """
        try:
            language_code = get_language_code(language)
            
            # For tribal languages, we'll use gTTS with the closest available language
            # This is a temporary solution until better TTS models for tribal languages are available
            
            # Map tribal languages to closest available language in gTTS
            language_map = {
                "sa": "hi",  # Santhali -> Hindi
                "ho": "hi",  # Ho -> Hindi
                "kru": "hi",  # Kurukh -> Hindi
                "mun": "hi",  # Mundari -> Hindi
                "khr": "hi"   # Kharia -> Hindi
            }
            
            tts_lang = language_map.get(language_code, language_code)
            
            # Use gTTS for synthesis
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_audio:
                temp_audio_path = temp_audio.name
            
            # Create TTS
            tts = gTTS(text=text, lang=tts_lang, slow=(speed < 1.0))
            tts.save(temp_audio_path)
            
            # Adjust speed if needed
            if abs(speed - 1.0) > 0.01:
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_output:
                    temp_output_path = temp_output.name
                
                # Use ffmpeg to adjust speed
                subprocess.run([
                    "ffmpeg", "-i", temp_audio_path, 
                    "-filter:a", f"atempo={speed}", 
                    "-y", temp_output_path
                ], check=True, capture_output=True)
                
                # Read adjusted audio
                with open(temp_output_path, "rb") as f:
                    audio_data = f.read()
                
                # Clean up
                os.remove(temp_output_path)
            else:
                # Read original audio
                with open(temp_audio_path, "rb") as f:
                    audio_data = f.read()
            
            # Clean up
            os.remove(temp_audio_path)
            
            return audio_data
        
        except Exception as e:
            logger.error(f"Error synthesizing speech: {str(e)}")
            raise ValueError(f"Speech synthesis failed: {str(e)}")
    
    def get_available_voices(self, language: Optional[str] = None) -> Dict:
        """
        Get available voices for TTS
        
        Args:
            language: Language code (optional, to filter voices)
            
        Returns:
            Dictionary of available voices
        """
        if language:
            language_code = get_language_code(language)
            return {language_code: self.voices.get(language_code, [])}
        else:
            return self.voices
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get dictionary of supported languages"""
        return self.languages
