import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from typing import List, Dict
import joblib
import os
import json
import logging

logger = logging.getLogger(__name__)

class SymptomCheckerModel:
    def __init__(self):
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.symptom_encoder = LabelEncoder()
        self.disease_encoder = LabelEncoder()
        self.symptoms_list = []
        self.diseases_list = []
        self.disease_descriptions = {}
        self.disease_precautions = {}
        self.model_path = os.path.join(os.path.dirname(__file__), '../data/symptom_checker_model.joblib')
        self.data_path = os.path.join(os.path.dirname(__file__), '../data/disease_data.json')
        
        # Load or initialize the model
        self._load_or_train_model()
    
    def _load_disease_data(self):
        """Load disease data from JSON file"""
        try:
            if os.path.exists(self.data_path):
                with open(self.data_path, 'r') as f:
                    data = json.load(f)
                    self.symptoms_list = data.get('symptoms', [])
                    self.diseases_list = data.get('diseases', [])
                    self.disease_descriptions = data.get('descriptions', {})
                    self.disease_precautions = data.get('precautions', {})
                    return True
            return False
        except Exception as e:
            logger.error(f"Error loading disease data: {str(e)}")
            return False
    
    def _load_or_train_model(self):
        """Load pre-trained model or train a new one"""
        if os.path.exists(self.model_path):
            try:
                loaded_model = joblib.load(self.model_path)
                self.model = loaded_model['model']
                self.symptom_encoder = loaded_model['symptom_encoder']
                self.disease_encoder = loaded_model['disease_encoder']
                logger.info("Loaded pre-trained symptom checker model")
                return
            except Exception as e:
                logger.error(f"Error loading model: {str(e)}")
        
        # If loading fails or model doesn't exist, train a new one
        self._train_model()
    
    def _train_model(self):
        """Train the symptom checker model"""
        # Load training data
        if not self._load_disease_data():
            raise Exception("No training data available")
        
        # Prepare training data (simplified example)
        X = np.random.rand(100, len(self.symptoms_list))  # Replace with real symptom data
        y = np.random.randint(0, len(self.diseases_list), 100)  # Replace with real disease labels
        
        # Train the model
        self.model.fit(X, y)
        
        # Save the trained model
        model_data = {
            'model': self.model,
            'symptom_encoder': self.symptom_encoder,
            'disease_encoder': self.disease_encoder
        }
        joblib.dump(model_data, self.model_path)
        logger.info("Trained and saved new symptom checker model")
    
    def predict_disease(self, symptoms: List[str]) -> Dict:
        """Predict diseases based on symptoms"""
        try:
            # Encode symptoms
            symptom_vector = np.zeros(len(self.symptoms_list))
            for symptom in symptoms:
                if symptom in self.symptoms_list:
                    idx = self.symptoms_list.index(symptom)
                    symptom_vector[idx] = 1
            
            # Make prediction
            prediction = self.model.predict_proba([symptom_vector])[0]
            
            # Get top 3 predictions
            top_indices = prediction.argsort()[-3:][::-1]
            
            results = []
            for idx in top_indices:
                if prediction[idx] > 0.1:  # Only include predictions with >10% probability
                    disease = self.diseases_list[idx]
                    results.append({
                        'disease': disease,
                        'probability': round(prediction[idx] * 100, 2),
                        'description': self.disease_descriptions.get(disease, ''),
                        'precautions': self.disease_precautions.get(disease, [])
                    })
            
            return {
                'predictions': results,
                'confidence_score': round(max(prediction) * 100, 2)
            }
            
        except Exception as e:
            logger.error(f"Error predicting disease: {str(e)}")
            return {'error': str(e)}
    
    def get_available_symptoms(self) -> List[str]:
        """Return list of available symptoms"""
        return self.symptoms_list