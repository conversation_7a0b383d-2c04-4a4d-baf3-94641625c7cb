from sqlalchemy import Column, Integer, String, Float, Boolean, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from datetime import datetime

from ..database import Base

class Destination(Base):
    """Tourism destination model"""
    __tablename__ = "tourism_destinations"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=False)
    location = Column(String(100), nullable=False)
    category = Column(String(50), nullable=False)  # e.g., 'waterfall', 'hill', 'temple', 'park'
    attractions = Column(JSON, nullable=True)  # List of attractions at this destination
    best_time = Column(String(100), nullable=True)
    how_to_reach = Column(Text, nullable=True)
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    image_url = Column(String(255), nullable=True)
    gallery_urls = Column(JSON, nullable=True)  # List of additional image URLs
    is_featured = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    events = relationship("TourismEvent", back_populates="destination")
    reviews = relationship("TourismReview", back_populates="destination")

class TribalHeritageSite(Base):
    """Tribal heritage site model"""
    __tablename__ = "tribal_heritage_sites"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=False)
    tribe = Column(String(50), nullable=False)  # e.g., 'Santhal', 'Munda', 'Ho', 'Oraon'
    location = Column(String(100), nullable=False)
    cultural_significance = Column(Text, nullable=True)
    historical_context = Column(Text, nullable=True)
    visiting_hours = Column(String(100), nullable=True)
    entry_fee = Column(String(50), nullable=True)
    contact_info = Column(String(100), nullable=True)
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    image_url = Column(String(255), nullable=True)
    panoramic_image_url = Column(String(255), nullable=True)  # For AR/VR experiences
    has_ar_tour = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    points_of_interest = relationship("PointOfInterest", back_populates="site")
    ar_tour = relationship("ARTour", back_populates="site", uselist=False)

class PointOfInterest(Base):
    """Points of interest within a tribal heritage site"""
    __tablename__ = "points_of_interest"
    
    id = Column(Integer, primary_key=True, index=True)
    site_id = Column(Integer, ForeignKey("tribal_heritage_sites.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=False)
    cultural_significance = Column(Text, nullable=True)
    historical_context = Column(Text, nullable=True)
    image_url = Column(String(255), nullable=True)
    model_url = Column(String(255), nullable=True)  # 3D model URL for AR
    has_3d_model = Column(Boolean, default=False)
    position_x = Column(Float, nullable=True)  # For positioning in AR
    position_y = Column(Float, nullable=True)
    position_z = Column(Float, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    site = relationship("TribalHeritageSite", back_populates="points_of_interest")

class TourismEvent(Base):
    """Tourism events and festivals model"""
    __tablename__ = "tourism_events"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=False)
    event_type = Column(String(50), nullable=False)  # e.g., 'festival', 'fair', 'exhibition'
    location = Column(String(100), nullable=False)
    destination_id = Column(Integer, ForeignKey("tourism_destinations.id"), nullable=True)
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    image_url = Column(String(255), nullable=True)
    website = Column(String(255), nullable=True)
    contact_info = Column(String(100), nullable=True)
    is_recurring = Column(Boolean, default=False)
    recurrence_pattern = Column(String(50), nullable=True)  # e.g., 'annual', 'monthly'
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    destination = relationship("Destination", back_populates="events")

class ARTour(Base):
    """Augmented Reality tour model"""
    __tablename__ = "ar_tours"
    
    id = Column(Integer, primary_key=True, index=True)
    site_id = Column(Integer, ForeignKey("tribal_heritage_sites.id"), nullable=False, unique=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=False)
    tour_type = Column(String(50), nullable=False)  # e.g., 'cultural', 'historical', 'natural'
    duration_minutes = Column(Integer, nullable=True)
    instructions = Column(Text, nullable=True)
    ar_marker_url = Column(String(255), nullable=True)  # URL for AR marker image
    scene_data = Column(JSON, nullable=True)  # JSON data for AR scene configuration
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    site = relationship("TribalHeritageSite", back_populates="ar_tour")

class TourismReview(Base):
    """Tourism destination reviews model"""
    __tablename__ = "tourism_reviews"
    
    id = Column(Integer, primary_key=True, index=True)
    destination_id = Column(Integer, ForeignKey("tourism_destinations.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    rating = Column(Integer, nullable=False)  # 1-5 star rating
    review_text = Column(Text, nullable=True)
    visit_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    destination = relationship("Destination", back_populates="reviews")
    user = relationship("User", back_populates="tourism_reviews")

class TourismItinerary(Base):
    """User-generated or AI-generated tourism itineraries"""
    __tablename__ = "tourism_itineraries"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Nullable for AI-generated itineraries
    title = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    days = Column(Integer, nullable=False)
    start_location = Column(String(100), nullable=False)
    interests = Column(JSON, nullable=True)  # List of interests
    budget = Column(String(50), nullable=True)  # e.g., 'budget', 'medium', 'luxury'
    travel_mode = Column(String(50), nullable=True)  # e.g., 'public', 'private', 'taxi'
    itinerary_data = Column(JSON, nullable=False)  # Detailed itinerary data
    is_public = Column(Boolean, default=False)
    is_ai_generated = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="tourism_itineraries")

# Add this relationship to the User model in user_model.py
"""
# In user_model.py:
tourism_reviews = relationship("TourismReview", back_populates="user")
tourism_itineraries = relationship("TourismItinerary", back_populates="user")
"""
