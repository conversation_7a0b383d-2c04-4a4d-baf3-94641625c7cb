import os
import json
import torch
from transformers import AutoModelForSeq2SeqLM, AutoTokenizer, MarianMTModel, MarianTokenizer
from typing import Dict, List, Optional, Union, Tuple
import logging
import datetime

from utils.language_utils import get_language_code, get_language_name, is_tribal_language

logger = logging.getLogger(__name__)

class TranslationModel:
    """
    Translation model for multiple languages including tribal languages of Jharkhand
    Supports Santhali, Ho, Mundari, Kurukh, and Kharia languages
    """
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize models dictionary
        self.models = {}
        self.tokenizers = {}
        
        # Define supported languages
        self.languages = {
            # Standard languages
            "en": "English",
            "hi": "Hindi",
            "bn": "Bengali",
            
            # Tribal languages
            "sa": "Santhali",
            "ho": "Ho",
            "kru": "Kurukh",
            "mun": "Mundari",
            "khr": "Kharia"
        }
        
        # Define model paths for different language pairs
        self.model_paths = {
            # Standard language pairs
            "en-hi": "Helsinki-NLP/opus-mt-en-hi",
            "hi-en": "Helsinki-NLP/opus-mt-hi-en",
            "en-bn": "Helsinki-NLP/opus-mt-en-bn",
            "bn-en": "Helsinki-NLP/opus-mt-bn-en",
            "hi-bn": "ai4bharat/indictrans-hi-bn",
            "bn-hi": "ai4bharat/indictrans-bn-hi",
            
            # Tribal language pairs (using AI4Bharat's IndicTrans models)
            "en-sa": "ai4bharat/indictrans-en-san",
            "sa-en": "ai4bharat/indictrans-san-en",
            "hi-sa": "ai4bharat/indictrans-hi-san",
            "sa-hi": "ai4bharat/indictrans-san-hi",
            
            # For other tribal languages, we'll use pivot translation through Hindi
            # This is a fallback approach until direct models are available
        }
        
        # Initialize feedback storage
        self.feedback_dir = os.path.join(os.path.dirname(__file__), "../data/translation_feedback")
        os.makedirs(self.feedback_dir, exist_ok=True)
    
    def _load_model(self, source_lang: str, target_lang: str) -> Tuple[torch.nn.Module, AutoTokenizer]:
        """Load translation model for a specific language pair"""
        model_key = f"{source_lang}-{target_lang}"
        
        # Check if model is already loaded
        if model_key in self.models and model_key in self.tokenizers:
            return self.models[model_key], self.tokenizers[model_key]
        
        # Check if we have a direct model for this language pair
        if model_key in self.model_paths:
            model_path = self.model_paths[model_key]
            
            try:
                # Load tokenizer and model
                tokenizer = AutoTokenizer.from_pretrained(model_path)
                model = AutoModelForSeq2SeqLM.from_pretrained(model_path).to(self.device)
                
                self.tokenizers[model_key] = tokenizer
                self.models[model_key] = model
                
                return model, tokenizer
            except Exception as e:
                logger.error(f"Error loading model for {model_key}: {str(e)}")
                # Fall back to pivot translation if direct model fails
                pass
        
        # If no direct model or loading failed, check if we can use English as pivot
        if source_lang != "en" and target_lang != "en":
            logger.info(f"No direct model for {model_key}, using English as pivot")
            return None, None
        
        # If all else fails, use a generic model
        logger.warning(f"No suitable model found for {model_key}, using generic model")
        tokenizer = AutoTokenizer.from_pretrained("Helsinki-NLP/opus-mt-mul-en")
        model = AutoModelForSeq2SeqLM.from_pretrained("Helsinki-NLP/opus-mt-mul-en").to(self.device)
        
        self.tokenizers[model_key] = tokenizer
        self.models[model_key] = model
        
        return model, tokenizer
    
    def translate(self, text: str, source_language: str, target_language: str) -> Dict:
        """
        Translate text from source language to target language
        
        Args:
            text: Text to translate
            source_language: Source language code
            target_language: Target language code
            
        Returns:
            Dictionary with translated text and confidence score
        """
        source_code = get_language_code(source_language)
        target_code = get_language_code(target_language)
        
        # If source and target are the same, return the original text
        if source_code == target_code:
            return {
                "translated_text": text,
                "confidence": 1.0
            }
        
        # Try direct translation
        model, tokenizer = self._load_model(source_code, target_code)
        
        if model is not None and tokenizer is not None:
            # Direct translation
            try:
                inputs = tokenizer(text, return_tensors="pt").to(self.device)
                
                with torch.no_grad():
                    outputs = model.generate(**inputs, max_length=1000)
                
                translated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
                
                return {
                    "translated_text": translated_text,
                    "confidence": 0.8  # Arbitrary confidence for direct translation
                }
            except Exception as e:
                logger.error(f"Error in direct translation: {str(e)}")
                # Fall back to pivot translation
        
        # Pivot translation through English or Hindi
        try:
            # Determine pivot language (English for most cases, Hindi for tribal languages)
            pivot_lang = "hi" if is_tribal_language(source_code) or is_tribal_language(target_code) else "en"
            
            # First translation: source -> pivot
            if source_code != pivot_lang:
                pivot_model, pivot_tokenizer = self._load_model(source_code, pivot_lang)
                
                if pivot_model is None:
                    raise ValueError(f"No model available for {source_code} to {pivot_lang}")
                
                inputs = pivot_tokenizer(text, return_tensors="pt").to(self.device)
                
                with torch.no_grad():
                    outputs = pivot_model.generate(**inputs, max_length=1000)
                
                pivot_text = pivot_tokenizer.decode(outputs[0], skip_special_tokens=True)
            else:
                pivot_text = text
            
            # Second translation: pivot -> target
            if pivot_lang != target_code:
                target_model, target_tokenizer = self._load_model(pivot_lang, target_code)
                
                if target_model is None:
                    raise ValueError(f"No model available for {pivot_lang} to {target_code}")
                
                inputs = target_tokenizer(pivot_text, return_tensors="pt").to(self.device)
                
                with torch.no_grad():
                    outputs = target_model.generate(**inputs, max_length=1000)
                
                final_text = target_tokenizer.decode(outputs[0], skip_special_tokens=True)
            else:
                final_text = pivot_text
            
            return {
                "translated_text": final_text,
                "confidence": 0.6  # Lower confidence for pivot translation
            }
        
        except Exception as e:
            logger.error(f"Error in pivot translation: {str(e)}")
            raise ValueError(f"Translation failed: {str(e)}")
    
    def store_feedback(self, original_text: str, translated_text: str, corrected_text: str,
                      source_language: str, target_language: str, feedback_notes: Optional[str] = None):
        """
        Store translation feedback for future improvements
        
        Args:
            original_text: Original text
            translated_text: Machine translated text
            corrected_text: Human corrected translation
            source_language: Source language code
            target_language: Target language code
            feedback_notes: Additional notes from user
        """
        try:
            # Create feedback entry
            feedback = {
                "original_text": original_text,
                "translated_text": translated_text,
                "corrected_text": corrected_text,
                "source_language": source_language,
                "target_language": target_language,
                "feedback_notes": feedback_notes,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
            # Generate filename based on language pair and timestamp
            filename = f"{source_language}-{target_language}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.json"
            filepath = os.path.join(self.feedback_dir, filename)
            
            # Save feedback to file
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(feedback, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Translation feedback stored: {filepath}")
            
            # TODO: Implement mechanism to periodically retrain models with feedback
            
        except Exception as e:
            logger.error(f"Error storing translation feedback: {str(e)}")
            raise ValueError(f"Failed to store feedback: {str(e)}")
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get dictionary of supported languages"""
        return self.languages
