import os
import json
import logging
from typing import Dict, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)

class TribalDataCollectionModel:
    """
    Model for collecting and managing tribal language data, user feedback,
    and dialect variations.
    
    Features:
    - Tribal language corpus collection and validation
    - User feedback management
    - Dialect variation tracking and analysis
    - Data quality assessment
    """
    
    def __init__(self, data_dir: str = "data/tribal_corpus"):
        self.data_dir = data_dir
        self.supported_languages = {
            "sa": "Santhali",
            "ho": "Ho",
            "kru": "<PERSON>ruk<PERSON>",
            "mun": "Mu<PERSON>ri",
            "khr": "Kharia"
        }
        
        # Ensure data directories exist
        self._init_data_directories()
    
    def _init_data_directories(self):
        """Initialize necessary data directories"""
        os.makedirs(self.data_dir, exist_ok=True)
        for lang in self.supported_languages:
            os.makedirs(os.path.join(self.data_dir, lang), exist_ok=True)
            os.makedirs(os.path.join(self.data_dir, lang, "feedback"), exist_ok=True)
            os.makedirs(os.path.join(self.data_dir, lang, "dialects"), exist_ok=True)
    
    def add_language_data(self, language: str, text: str, dialect: Optional[str] = None,
                         metadata: Optional[Dict] = None) -> Dict:
        """Add new text data to the tribal language corpus"""
        try:
            if language not in self.supported_languages:
                return {"error": f"Unsupported language: {language}"}
            
            # Validate and clean the text data
            cleaned_text = self._validate_and_clean_text(text)
            if not cleaned_text:
                return {"error": "Invalid or empty text data"}
            
            # Prepare data entry
            entry = {
                "text": cleaned_text,
                "dialect": dialect,
                "metadata": metadata or {},
                "timestamp": datetime.now().isoformat(),
                "verified": False
            }
            
            # Save to appropriate directory
            file_path = self._get_data_file_path(language, dialect)
            self._save_data_entry(file_path, entry)
            
            return {"success": True, "message": "Data added successfully"}
            
        except Exception as e:
            logger.error(f"Error adding language data: {str(e)}")
            return {"error": str(e)}
    
    def add_user_feedback(self, language: str, text_id: str, feedback_type: str,
                         feedback_content: str, user_id: Optional[str] = None) -> Dict:
        """Collect and store user feedback for language model improvement"""
        try:
            if language not in self.supported_languages:
                return {"error": f"Unsupported language: {language}"}
            
            feedback = {
                "text_id": text_id,
                "feedback_type": feedback_type,
                "content": feedback_content,
                "user_id": user_id,
                "timestamp": datetime.now().isoformat(),
                "processed": False
            }
            
            # Save feedback
            feedback_file = os.path.join(self.data_dir, language, "feedback", f"{text_id}.json")
            self._save_data_entry(feedback_file, feedback)
            
            return {"success": True, "message": "Feedback recorded successfully"}
            
        except Exception as e:
            logger.error(f"Error adding feedback: {str(e)}")
            return {"error": str(e)}
    
    def register_dialect_variation(self, language: str, base_text: str,
                                 dialect_text: str, dialect_name: str,
                                 region: Optional[str] = None) -> Dict:
        """Register a new dialect variation for a tribal language"""
        try:
            if language not in self.supported_languages:
                return {"error": f"Unsupported language: {language}"}
            
            variation = {
                "base_text": base_text,
                "dialect_text": dialect_text,
                "dialect_name": dialect_name,
                "region": region,
                "timestamp": datetime.now().isoformat(),
                "verified": False
            }
            
            # Save dialect variation
            dialect_file = os.path.join(self.data_dir, language, "dialects", f"{dialect_name}.json")
            self._save_data_entry(dialect_file, variation)
            
            return {"success": True, "message": "Dialect variation registered successfully"}
            
        except Exception as e:
            logger.error(f"Error registering dialect variation: {str(e)}")
            return {"error": str(e)}
    
    def _validate_and_clean_text(self, text: str) -> str:
        """Validate and clean input text data"""
        if not text or not isinstance(text, str):
            return ""
        
        # Remove extra whitespace
        text = " ".join(text.split())
        
        # Basic validation
        if len(text.strip()) < 2:
            return ""
        
        return text.strip()
    
    def _get_data_file_path(self, language: str, dialect: Optional[str] = None) -> str:
        """Generate appropriate file path for storing data"""
        base_dir = os.path.join(self.data_dir, language)
        if dialect:
            base_dir = os.path.join(base_dir, "dialects", dialect)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return os.path.join(base_dir, f"data_{timestamp}.json")
    
    def _save_data_entry(self, file_path: str, data: Dict) -> None:
        """Save data entry to JSON file"""
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def get_language_statistics(self, language: str) -> Dict:
        """Get statistics about collected data for a specific language"""
        try:
            if language not in self.supported_languages:
                return {"error": f"Unsupported language: {language}"}
            
            lang_dir = os.path.join(self.data_dir, language)
            
            stats = {
                "total_entries": 0,
                "total_feedback": 0,
                "dialect_variations": 0,
                "last_updated": None
            }
            
            # Count main data entries
            for root, _, files in os.walk(lang_dir):
                if "feedback" not in root and "dialects" not in root:
                    stats["total_entries"] += len([f for f in files if f.endswith('.json')])
            
            # Count feedback entries
            feedback_dir = os.path.join(lang_dir, "feedback")
            if os.path.exists(feedback_dir):
                stats["total_feedback"] = len([f for f in os.listdir(feedback_dir) if f.endswith('.json')])
            
            # Count dialect variations
            dialect_dir = os.path.join(lang_dir, "dialects")
            if os.path.exists(dialect_dir):
                stats["dialect_variations"] = len([f for f in os.listdir(dialect_dir) if f.endswith('.json')])
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting language statistics: {str(e)}")
            return {"error": str(e)}