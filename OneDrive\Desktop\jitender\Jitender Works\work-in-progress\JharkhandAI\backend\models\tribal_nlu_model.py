import os
import torch
import numpy as np
from transformers import AutoModelForSequenceClassification, AutoTokenizer, AutoModelForTokenClassification
from typing import Dict, List, Optional, Union, Tuple
import logging
import json

from utils.language_utils import get_language_code, is_tribal_language

logger = logging.getLogger(__name__)

class TribalNLUModel:
    """
    Natural Language Understanding model specialized for tribal languages of Jharkhand
    Supports Santhali, Ho, Mundari, Kurukh, and Kharia languages
    
    Features:
    - Intent recognition
    - Entity extraction
    - Sentiment analysis
    - Cultural context understanding
    """
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize models dictionary
        self.intent_models = {}
        self.entity_models = {}
        self.sentiment_models = {}
        self.tokenizers = {}
        self.context_models = {}
        
        # Define supported tribal languages
        self.tribal_languages = {
            "sa": "Santhali",
            "ho": "Ho",
            "kru": "<PERSON><PERSON><PERSON>",
            "mun": "<PERSON><PERSON><PERSON>",
            "khr": "Kharia"
        }
        
        # Load cultural context data
        self.cultural_contexts = self._load_cultural_contexts()
        
        # Initialize advanced NLU components
        self._init_advanced_nlu()
        
        # Load models on demand to save memory
    
    def _load_cultural_contexts(self) -> Dict[str, Dict]:
        """Load cultural context data for each language"""
        contexts = {}
        
        try:
            context_path = os.path.join(os.path.dirname(__file__), "../data/cultural_contexts.json")
            
            if os.path.exists(context_path):
                with open(context_path, "r", encoding="utf-8") as f:
                    contexts = json.load(f)
            else:
                # Create default context if file doesn't exist
                contexts = {
                    "sa": {
                        "greeting_phrases": ["Johar", "Namaste"],
                        "cultural_entities": ["Sarna", "Karam", "Sohrai"],
                        "respect_markers": ["Hạṛạm", "Buḍhi"],
                        "festivals": ["Sohrai", "Karam", "Baha"],
                        "customs": ["Sarna Dharma", "Tribal Dance", "Traditional Music"]
                    },
                    "ho": {
                        "greeting_phrases": ["Johar", "Dular"],
                        "cultural_entities": ["Maghe", "Ba", "Desauli"],
                        "respect_markers": ["Apu", "Dada"],
                        "festivals": ["Maghe", "Baa Parab", "Horo Jom Sim"],
                        "customs": ["Ho Dance", "Traditional Hunting", "Community Rituals"]
                    },
                    "kru": {
                        "greeting_phrases": ["Johar", "Ram Ram"],
                        "cultural_entities": ["Jatra", "Sarhul", "Karma"],
                        "respect_markers": ["Dado", "Dadi"],
                        "festivals": ["Karma", "Sarhul", "Kadleta"],
                        "customs": ["Oraon Dance", "Traditional Medicine", "Harvest Rituals"]
                    },
                    "mun": {
                        "greeting_phrases": ["Johar", "Dobor"],
                        "cultural_entities": ["Mage Porob", "Sohorai", "Baha"],
                        "respect_markers": ["Haram", "Buri"],
                        "festivals": ["Mage Parab", "Phagu", "Hero"],
                        "customs": ["Munda Dance", "Sacred Groves", "Village Council"]
                    },
                    "khr": {
                        "greeting_phrases": ["Johar", "Namaskar"],
                        "cultural_entities": ["Jani Shikar", "Sarhul", "Sohrai"],
                        "respect_markers": ["Aba", "Ayo"],
                        "festivals": ["Jani Shikar", "Sarhul", "Tussu"],
                        "customs": ["Kharia Dance", "Forest Worship", "Community Fishing"]
                    }
                }
                
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(context_path), exist_ok=True)
                
                # Save default context
                with open(context_path, "w", encoding="utf-8") as f:
                    json.dump(contexts, f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            logger.error(f"Error loading cultural contexts: {str(e)}")
        
        return contexts

    def _init_advanced_nlu(self):
        """Initialize advanced NLU components for tribal languages"""
        try:
            # Initialize context-aware models for each tribal language
            for lang_code in self.tribal_languages.keys():
                # Load specialized context model if available
                context_model_path = f"./models/nlu/{lang_code}_context_model"
                if os.path.exists(context_model_path):
                    self.context_models[lang_code] = AutoModelForSequenceClassification.from_pretrained(
                        context_model_path
                    ).to(self.device)
                else:
                    # Use base model with cultural adaptation
                    self.context_models[lang_code] = AutoModelForSequenceClassification.from_pretrained(
                        "ai4bharat/indic-bert"
                    ).to(self.device)
                    logger.info(f"Using base model with cultural adaptation for {lang_code}")

            logger.info("Successfully initialized advanced NLU components")
        except Exception as e:
            logger.error(f"Error initializing advanced NLU components: {str(e)}")
    
    def _load_intent_model(self, language_code: str):
        """Load intent recognition model for a specific language"""
        if language_code in self.intent_models and language_code in self.tokenizers:
            return self.intent_models[language_code], self.tokenizers[language_code]
        
        # Map language code to model path
        model_paths = {
            "sa": "ai4bharat/indic-bert-santhali",
            "ho": "ai4bharat/indic-bert",
            "kru": "ai4bharat/indic-bert",
            "mun": "ai4bharat/indic-bert",
            "khr": "ai4bharat/indic-bert"
        }
        
        # Use default model if specific one not available
        model_path = model_paths.get(language_code, "ai4bharat/indic-bert")
        
        try:
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForSequenceClassification.from_pretrained(model_path).to(self.device)
            
            self.tokenizers[language_code] = tokenizer
            self.intent_models[language_code] = model
            
            return model, tokenizer
        except Exception as e:
            logger.error(f"Error loading intent model for {language_code}: {str(e)}")
            # Fall back to default model
            tokenizer = AutoTokenizer.from_pretrained("ai4bharat/indic-bert")
            model = AutoModelForSequenceClassification.from_pretrained("ai4bharat/indic-bert").to(self.device)
            
            self.tokenizers[language_code] = tokenizer
            self.intent_models[language_code] = model
            
            return model, tokenizer
    
    def _load_entity_model(self, language_code: str):
        """Load entity extraction model for a specific language"""
        if language_code in self.entity_models and language_code in self.tokenizers:
            return self.entity_models[language_code], self.tokenizers[language_code]
        
        # Map language code to model path
        model_paths = {
            "sa": "ai4bharat/indic-bert-santhali",
            "ho": "ai4bharat/indic-bert",
            "kru": "ai4bharat/indic-bert",
            "mun": "ai4bharat/indic-bert",
            "khr": "ai4bharat/indic-bert"
        }
        
        # Use default model if specific one not available
        model_path = model_paths.get(language_code, "ai4bharat/indic-bert")
        
        try:
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForTokenClassification.from_pretrained(model_path).to(self.device)
            
            if language_code not in self.tokenizers:
                self.tokenizers[language_code] = tokenizer
                
            self.entity_models[language_code] = model
            
            return model, tokenizer
        except Exception as e:
            logger.error(f"Error loading entity model for {language_code}: {str(e)}")
            # Fall back to default model
            tokenizer = AutoTokenizer.from_pretrained("ai4bharat/indic-bert")
            model = AutoModelForTokenClassification.from_pretrained("ai4bharat/indic-bert").to(self.device)
            
            if language_code not in self.tokenizers:
                self.tokenizers[language_code] = tokenizer
                
            self.entity_models[language_code] = model
            
            return model, tokenizer
    
    def recognize_intent(self, text: str, language: str) -> Dict[str, float]:
        """
        Recognize intent from text
        
        Args:
            text: Input text
            language: Language code or name
            
        Returns:
            Dictionary of intent labels and confidence scores
        """
        language_code = get_language_code(language)
        
        if not is_tribal_language(language_code):
            raise ValueError(f"Language {language} is not supported by TribalNLUModel")
        
        # Load model and tokenizer
        model, tokenizer = self._load_intent_model(language_code)
        
        # Tokenize
        inputs = tokenizer(text, return_tensors="pt").to(self.device)
        
        # Get predictions
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            
        # Convert to probabilities
        probs = torch.nn.functional.softmax(logits, dim=-1)[0].cpu().numpy()
        
        # Map to intent labels
        intent_labels = ["greeting", "question", "command", "information", "farewell"]
        
        # Create dictionary of intent labels and scores
        intents = {label: float(prob) for label, prob in zip(intent_labels, probs)}
        
        # Enhance with cultural context
        intents = self._enhance_with_cultural_context(text, intents, language_code)
        
        return intents
    
    def extract_entities(self, text: str, language: str) -> List[Dict[str, Union[str, float]]]:
        """
        Extract entities from text
        
        Args:
            text: Input text
            language: Language code or name
            
        Returns:
            List of entities with type, value, and confidence
        """
        language_code = get_language_code(language)
        
        if not is_tribal_language(language_code):
            raise ValueError(f"Language {language} is not supported by TribalNLUModel")
        
        # Load model and tokenizer
        model, tokenizer = self._load_entity_model(language_code)
        
        # Tokenize
        inputs = tokenizer(text, return_tensors="pt").to(self.device)
        
        # Get predictions
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            
        # Get token predictions
        predictions = torch.argmax(logits, dim=2)[0].cpu().numpy()
        
        # Get token probabilities
        probs = torch.nn.functional.softmax(logits, dim=2)[0].cpu().numpy()
        
        # Get tokens
        tokens = tokenizer.convert_ids_to_tokens(inputs.input_ids[0].cpu().numpy())
        
        # Entity labels
        entity_labels = ["O", "B-PER", "I-PER", "B-ORG", "I-ORG", "B-LOC", "I-LOC", "B-MISC", "I-MISC"]
        
        # Extract entities
        entities = []
        current_entity = None
        
        for i, (token, prediction, prob) in enumerate(zip(tokens, predictions, probs)):
            if token.startswith("##"):
                if current_entity:
                    current_entity["value"] += token[2:]
                continue
                
            entity_label = entity_labels[prediction]
            confidence = float(prob[prediction])
            
            if entity_label.startswith("B-"):
                # Start of a new entity
                if current_entity:
                    entities.append(current_entity)
                    
                entity_type = entity_label[2:]
                current_entity = {
                    "type": entity_type,
                    "value": token,
                    "confidence": confidence
                }
            elif entity_label.startswith("I-") and current_entity:
                # Continuation of current entity
                current_entity["value"] += " " + token
                current_entity["confidence"] = min(current_entity["confidence"], confidence)
            elif current_entity:
                # End of current entity
                entities.append(current_entity)
                current_entity = None
        
        # Add final entity if exists
        if current_entity:
            entities.append(current_entity)
        
        # Enhance with cultural context
        entities = self._enhance_entities_with_cultural_context(entities, language_code)
        
        return entities
    
    def analyze_sentiment(self, text: str, language: str) -> Dict[str, float]:
        """
        Analyze sentiment of text
        
        Args:
            text: Input text
            language: Language code or name
            
        Returns:
            Dictionary of sentiment labels and scores
        """
        language_code = get_language_code(language)
        
        if not is_tribal_language(language_code):
            raise ValueError(f"Language {language} is not supported by TribalNLUModel")
        
        # For now, use intent model for sentiment analysis
        model, tokenizer = self._load_intent_model(language_code)
        
        # Tokenize
        inputs = tokenizer(text, return_tensors="pt").to(self.device)
        
        # Get predictions
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            
        # Convert to probabilities
        probs = torch.nn.functional.softmax(logits, dim=-1)[0].cpu().numpy()
        
        # Map to sentiment labels
        sentiment_labels = ["negative", "neutral", "positive"]
        
        # Create dictionary of sentiment labels and scores
        sentiments = {label: float(prob) for label, prob in zip(sentiment_labels, probs[:3])}
        
        return sentiments
    
    def _enhance_with_cultural_context(self, text: str, intents: Dict[str, float], language_code: str) -> Dict[str, float]:
        """Enhance intent recognition with cultural context"""
        if language_code not in self.cultural_contexts:
            return intents
        
        context = self.cultural_contexts[language_code]
        
        # Check for greeting phrases
        for phrase in context.get("greeting_phrases", []):
            if phrase.lower() in text.lower():
                intents["greeting"] = max(intents.get("greeting", 0), 0.9)
                break
        
        # Add more cultural context enhancements as needed
        
        return intents
    
    def _enhance_entities_with_cultural_context(self, entities: List[Dict], language_code: str) -> List[Dict]:
        """Enhance entity extraction with cultural context"""
        if language_code not in self.cultural_contexts:
            return entities
        
        context = self.cultural_contexts[language_code]
        
        # Check for cultural entities
        for entity in entities:
            entity_value = entity["value"].lower()
            
            for cultural_entity in context.get("cultural_entities", []):
                if cultural_entity.lower() in entity_value:
                    entity["type"] = "CULTURAL"
                    entity["confidence"] = max(entity["confidence"], 0.9)
                    break
            
            for respect_marker in context.get("respect_markers", []):
                if respect_marker.lower() in entity_value:
                    entity["type"] = "RESPECT"
                    entity["confidence"] = max(entity["confidence"], 0.9)
                    break
        
        return entities
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get dictionary of supported languages"""
        return self.tribal_languages
