import os
import numpy as np
import torch
import torchaudio
from transformers import Wav2Vec2ForCTC, Wav2Vec2Processor, AutoTokenizer, AutoModelForSeq2SeqLM
import librosa
from typing import Dict, List, Optional, Union, Tuple
import logging

from ..utils.language_utils import get_language_code, is_tribal_language

logger = logging.getLogger(__name__)

class TribalSpeechModel:
    """
    Speech model specialized for tribal languages of Jharkhand
    Supports Santhali, Ho, Mundari, Kurukh, and Kharia languages
    """
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize models dictionary
        self.asr_models = {}
        self.asr_processors = {}
        self.tts_models = {}
        self.tts_tokenizers = {}
        
        # Define supported tribal languages
        self.tribal_languages = {
            "sa": "Santhali",
            "ho": "Ho",
            "kru": "<PERSON>ruk<PERSON>",
            "mun": "<PERSON><PERSON><PERSON>",
            "khr": "Kharia"
        }
        
        # Load models on demand to save memory
        
    def _load_asr_model(self, language_code: str) -> Tuple[Wav2Vec2ForCTC, Wav2Vec2Processor]:
        """Load ASR model for a specific language"""
        if language_code in self.asr_models and language_code in self.asr_processors:
            return self.asr_models[language_code], self.asr_processors[language_code]
        
        # Map language code to model path
        model_paths = {
            "sa": "ai4bharat/indicwav2vec-santhali",
            "ho": "ai4bharat/indicwav2vec-tribal",
            "kru": "ai4bharat/indicwav2vec-tribal",
            "mun": "ai4bharat/indicwav2vec-tribal",
            "khr": "ai4bharat/indicwav2vec-tribal"
        }
        
        # Use default model if specific one not available
        model_path = model_paths.get(language_code, "ai4bharat/indicwav2vec-tribal")
        
        try:
            processor = Wav2Vec2Processor.from_pretrained(model_path)
            model = Wav2Vec2ForCTC.from_pretrained(model_path).to(self.device)
            
            self.asr_processors[language_code] = processor
            self.asr_models[language_code] = model
            
            return model, processor
        except Exception as e:
            logger.error(f"Error loading ASR model for {language_code}: {str(e)}")
            # Fall back to default model
            processor = Wav2Vec2Processor.from_pretrained("ai4bharat/indicwav2vec-tribal")
            model = Wav2Vec2ForCTC.from_pretrained("ai4bharat/indicwav2vec-tribal").to(self.device)
            
            self.asr_processors[language_code] = processor
            self.asr_models[language_code] = model
            
            return model, processor
    
    def _load_tts_model(self, language_code: str) -> Tuple[AutoModelForSeq2SeqLM, AutoTokenizer]:
        """Load TTS model for a specific language"""
        if language_code in self.tts_models and language_code in self.tts_tokenizers:
            return self.tts_models[language_code], self.tts_tokenizers[language_code]
        
        # Map language code to model path
        model_paths = {
            "sa": "ai4bharat/indictts-santhali",
            "ho": "ai4bharat/indictts-tribal",
            "kru": "ai4bharat/indictts-tribal",
            "mun": "ai4bharat/indictts-tribal",
            "khr": "ai4bharat/indictts-tribal"
        }
        
        # Use default model if specific one not available
        model_path = model_paths.get(language_code, "ai4bharat/indictts-tribal")
        
        try:
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForSeq2SeqLM.from_pretrained(model_path).to(self.device)
            
            self.tts_tokenizers[language_code] = tokenizer
            self.tts_models[language_code] = model
            
            return model, tokenizer
        except Exception as e:
            logger.error(f"Error loading TTS model for {language_code}: {str(e)}")
            # Fall back to default model
            tokenizer = AutoTokenizer.from_pretrained("ai4bharat/indictts-tribal")
            model = AutoModelForSeq2SeqLM.from_pretrained("ai4bharat/indictts-tribal").to(self.device)
            
            self.tts_tokenizers[language_code] = tokenizer
            self.tts_models[language_code] = model
            
            return model, tokenizer
    
    def transcribe(self, audio_path: str, language: str) -> str:
        """
        Transcribe audio to text for tribal languages
        
        Args:
            audio_path: Path to audio file
            language: Language code or name
            
        Returns:
            Transcribed text
        """
        language_code = get_language_code(language)
        
        if not is_tribal_language(language_code):
            raise ValueError(f"Language {language} is not supported by TribalSpeechModel")
        
        # Load audio
        speech_array, sampling_rate = librosa.load(audio_path, sr=16000)
        
        # Load model and processor
        model, processor = self._load_asr_model(language_code)
        
        # Process audio
        inputs = processor(speech_array, sampling_rate=16000, return_tensors="pt").to(self.device)
        
        with torch.no_grad():
            logits = model(inputs.input_values).logits
        
        # Decode
        predicted_ids = torch.argmax(logits, dim=-1)
        transcription = processor.batch_decode(predicted_ids)[0]
        
        # Apply language-specific post-processing
        transcription = self._post_process_text(transcription, language_code)
        
        return transcription
    
    def synthesize(self, text: str, language: str) -> bytes:
        """
        Synthesize text to speech for tribal languages
        
        Args:
            text: Text to synthesize
            language: Language code or name
            
        Returns:
            Audio content as bytes
        """
        language_code = get_language_code(language)
        
        if not is_tribal_language(language_code):
            raise ValueError(f"Language {language} is not supported by TribalSpeechModel")
        
        # Load model and tokenizer
        model, tokenizer = self._load_tts_model(language_code)
        
        # Preprocess text
        text = self._pre_process_text(text, language_code)
        
        # Tokenize
        inputs = tokenizer(text, return_tensors="pt").to(self.device)
        
        # Generate mel-spectrograms
        with torch.no_grad():
            outputs = model.generate(
                input_ids=inputs.input_ids,
                attention_mask=inputs.attention_mask,
                max_length=1000
            )
        
        # Convert to audio
        mel_outputs = outputs[0].cpu().numpy()
        audio_array = self._mel_to_audio(mel_outputs, language_code)
        
        # Convert to bytes
        audio_bytes = self._audio_to_bytes(audio_array)
        
        return audio_bytes
    
    def _pre_process_text(self, text: str, language_code: str) -> str:
        """Apply language-specific preprocessing to text"""
        # Language-specific text normalization and preprocessing
        if language_code == "sa":
            # Santhali-specific preprocessing
            text = self._normalize_santhali_text(text)
        elif language_code in ["ho", "kru", "mun", "khr"]:
            # General tribal language preprocessing
            text = self._normalize_tribal_text(text)
        return text

    def _post_process_text(self, text: str, language_code: str) -> str:
        """Apply language-specific postprocessing to transcribed text"""
        # Language-specific text cleanup and enhancement
        if language_code == "sa":
            # Santhali-specific postprocessing
            text = self._enhance_santhali_text(text)
        elif language_code in ["ho", "kru", "mun", "khr"]:
            # General tribal language postprocessing
            text = self._enhance_tribal_text(text)
        return text

    def _normalize_santhali_text(self, text: str) -> str:
        """Normalize Santhali text for better TTS quality"""
        # Add Santhali-specific text normalization rules
        # Handle special characters, numbers, and abbreviations
        return text

    def _normalize_tribal_text(self, text: str) -> str:
        """Normalize tribal language text for better TTS quality"""
        # Add general tribal language text normalization rules
        # Handle common patterns and special cases
        return text

    def _enhance_santhali_text(self, text: str) -> str:
        """Enhance Santhali text output quality"""
        # Improve text quality with Santhali-specific rules
        # Fix common recognition errors
        return text

    def _enhance_tribal_text(self, text: str) -> str:
        """Enhance tribal language text output quality"""
        # Improve text quality with tribal language rules
        # Fix common recognition patterns
        return text

    def _mel_to_audio(self, mel_outputs: np.ndarray, language_code: str) -> np.ndarray:
        """Convert mel-spectrograms to audio waveform with improved quality"""
        # Use HiFi-GAN vocoder for better audio quality
        try:
            from parallel_wavegan.utils import load_model
            vocoder = load_model(f"models/vocoders/{language_code}_vocoder.pth")
            audio_array = vocoder.inference(mel_outputs)
        except Exception as e:
            logger.warning(f"Failed to use HiFi-GAN vocoder: {str(e)}. Falling back to basic conversion.")
            audio_array = librosa.feature.inverse.mel_to_audio(mel_outputs)
        
        # Apply audio enhancement
        audio_array = self._enhance_audio_quality(audio_array, language_code)
        return audio_array

    def _enhance_audio_quality(self, audio_array: np.ndarray, language_code: str) -> np.ndarray:
        """Enhance the audio quality for specific languages"""
        # Apply language-specific audio enhancements
        # This could include noise reduction, volume normalization, etc.
        return audio_array

    def _audio_to_bytes(self, audio_array: np.ndarray, sample_rate: int = 22050) -> bytes:
        """Convert audio array to bytes with compression for low bandwidth"""
        # Convert to int16 for reduced size
        audio_int16 = (audio_array * 32767).astype(np.int16)
        
        # Use efficient audio compression
        import io
        import soundfile as sf
        
        buffer = io.BytesIO()
        sf.write(buffer, audio_int16, sample_rate, format='ogg', subtype='vorbis')
        return buffer.getvalue()
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get dictionary of supported languages"""
        return self.tribal_languages