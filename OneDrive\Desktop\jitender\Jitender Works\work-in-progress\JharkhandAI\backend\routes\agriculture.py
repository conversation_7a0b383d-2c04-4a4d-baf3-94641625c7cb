from fastapi import APIRouter, Depends, HTTPException, Body, File, UploadFile
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from database import get_db, AgricultureRequest, CropRecommendation
from utils.auth import get_current_user
import logging
import json
from datetime import datetime
import os
import uuid
import numpy as np
from PIL import Image
import io

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/agriculture",
    tags=["agriculture"],
    responses={404: {"description": "Not found"}},
)

# Helper functions for agriculture services
async def get_crop_recommendation(
    nitrogen: float,
    phosphorus: float,
    potassium: float,
    temperature: float,
    humidity: float,
    ph: float,
    rainfall: float
) -> Dict[str, Any]:
    """
    Get crop recommendation based on soil and climate parameters.
    This is a placeholder - in a real implementation, you would use a trained ML model.
    """
    # In a real implementation, you would load a trained model and make predictions
    # For now, use a simple rule-based system
    
    # Define crop suitability based on parameters
    crops = {
        "rice": {
            "n_range": (80, 120),
            "p_range": (40, 60),
            "k_range": (40, 60),
            "temp_range": (22, 32),
            "humidity_range": (80, 95),
            "ph_range": (5.5, 6.5),
            "rainfall_range": (200, 300)
        },
        "wheat": {
            "n_range": (100, 140),
            "p_range": (50, 70),
            "k_range": (50, 70),
            "temp_range": (15, 25),
            "humidity_range": (60, 80),
            "ph_range": (6.0, 7.0),
            "rainfall_range": (75, 150)
        },
        "maize": {
            "n_range": (80, 160),
            "p_range": (50, 80),
            "k_range": (40, 80),
            "temp_range": (20, 30),
            "humidity_range": (50, 80),
            "ph_range": (5.5, 7.5),
            "rainfall_range": (50, 200)
        },
        "pulses": {
            "n_range": (20, 60),
            "p_range": (40, 80),
            "k_range": (20, 60),
            "temp_range": (18, 30),
            "humidity_range": (40, 70),
            "ph_range": (6.0, 7.5),
            "rainfall_range": (60, 150)
        },
        "vegetables": {
            "n_range": (80, 120),
            "p_range": (60, 100),
            "k_range": (80, 120),
            "temp_range": (15, 35),
            "humidity_range": (60, 90),
            "ph_range": (6.0, 7.0),
            "rainfall_range": (50, 200)
        }
    }
    
    # Calculate suitability scores for each crop
    scores = {}
    for crop, params in crops.items():
        # Calculate how well each parameter fits within the ideal range
        n_score = 1.0 - min(1.0, abs(nitrogen - sum(params["n_range"])/2) / (params["n_range"][1] - params["n_range"][0]))
        p_score = 1.0 - min(1.0, abs(phosphorus - sum(params["p_range"])/2) / (params["p_range"][1] - params["p_range"][0]))
        k_score = 1.0 - min(1.0, abs(potassium - sum(params["k_range"])/2) / (params["k_range"][1] - params["k_range"][0]))
        temp_score = 1.0 - min(1.0, abs(temperature - sum(params["temp_range"])/2) / (params["temp_range"][1] - params["temp_range"][0]))
        humidity_score = 1.0 - min(1.0, abs(humidity - sum(params["humidity_range"])/2) / (params["humidity_range"][1] - params["humidity_range"][0]))
        ph_score = 1.0 - min(1.0, abs(ph - sum(params["ph_range"])/2) / (params["ph_range"][1] - params["ph_range"][0]))
        rainfall_score = 1.0 - min(1.0, abs(rainfall - sum(params["rainfall_range"])/2) / (params["rainfall_range"][1] - params["rainfall_range"][0]))
        
        # Calculate overall score (weighted average)
        overall_score = (n_score * 0.15 + p_score * 0.15 + k_score * 0.15 + 
                         temp_score * 0.2 + humidity_score * 0.1 + 
                         ph_score * 0.15 + rainfall_score * 0.1)
        
        scores[crop] = overall_score * 100  # Convert to percentage
    
    # Sort crops by score
    sorted_crops = sorted(scores.items(), key=lambda x: x[1], reverse=True)
    
    # Get top recommendation
    recommended_crop = sorted_crops[0][0]
    confidence = sorted_crops[0][1]
    
    # Prepare soil health assessment
    soil_health = {
        "nitrogen": {
            "value": nitrogen,
            "status": "Low" if nitrogen < 50 else ("High" if nitrogen > 150 else "Optimal")
        },
        "phosphorus": {
            "value": phosphorus,
            "status": "Low" if phosphorus < 30 else ("High" if phosphorus > 80 else "Optimal")
        },
        "potassium": {
            "value": potassium,
            "status": "Low" if potassium < 30 else ("High" if potassium > 80 else "Optimal")
        },
        "ph": {
            "value": ph,
            "status": "Acidic" if ph < 6.0 else ("Alkaline" if ph > 7.5 else "Optimal")
        }
    }
    
    # Prepare climate suitability assessment
    climate_suitability = {
        "temperature": {
            "value": temperature,
            "status": "Low" if temperature < 15 else ("High" if temperature > 35 else "Optimal")
        },
        "humidity": {
            "value": humidity,
            "status": "Low" if humidity < 40 else ("High" if humidity > 90 else "Optimal")
        },
        "rainfall": {
            "value": rainfall,
            "status": "Low" if rainfall < 50 else ("High" if rainfall > 300 else "Optimal")
        }
    }
    
    return {
        "recommended_crop": recommended_crop,
        "confidence": confidence,
        "alternatives": [{"crop": crop, "confidence": score} for crop, score in sorted_crops[1:4]],
        "soil_health": soil_health,
        "climate_suitability": climate_suitability
    }

async def analyze_plant_disease(image_data: bytes) -> Dict[str, Any]:
    """
    Analyze plant image for disease detection.
    This is a placeholder - in a real implementation, you would use a trained ML model.
    """
    try:
        # In a real implementation, you would:
        # 1. Load the image
        # 2. Preprocess it for your model
        # 3. Run it through a trained disease detection model
        # 4. Return the results
        
        # For now, return a placeholder response
        return {
            "status": "healthy",  # or "diseased"
            "confidence": 85.5,
            "disease": None,  # would be the disease name if detected
            "treatment": None,  # would be treatment recommendations if disease detected
            "message": "Plant appears healthy. Continue regular care and monitoring."
        }
    except Exception as e:
        logger.error(f"Error analyzing plant disease: {e}")
        raise HTTPException(status_code=500, detail="Error processing image")

async def get_weather_forecast(latitude: float, longitude: float) -> Dict[str, Any]:
    """
    Get weather forecast for a location.
    This is a placeholder - in a real implementation, you would call a weather API.
    """
    # In a real implementation, you would call a weather API like OpenWeatherMap
    # For now, return a placeholder response
    
    # Simulate a 5-day forecast
    forecast = []
    base_temp = 25 + (latitude / 10)  # Simulate temperature variation by latitude
    
    for i in range(5):
        # Simulate some variation in the forecast
        temp_variation = np.random.normal(0, 2)
        rain_chance = max(0, min(100, 30 + np.random.normal(0, 20)))
        
        forecast.append({
            "date": (datetime.now().date() + datetime.timedelta(days=i)).isoformat(),
            "temperature": round(base_temp + temp_variation, 1),
            "humidity": round(60 + np.random.normal(0, 10), 1),
            "precipitation_chance": round(rain_chance, 1),
            "wind_speed": round(5 + np.random.normal(0, 2), 1),
            "description": "Partly Cloudy"  # This would be more varied in a real implementation
        })
    
    return {
        "location": {
            "latitude": latitude,
            "longitude": longitude,
            "name": "Unknown Location"  # In a real implementation, you would reverse geocode
        },
        "current": {
            "temperature": round(base_temp, 1),
            "humidity": 65,
            "wind_speed": 4.5,
            "description": "Sunny"
        },
        "forecast": forecast
    }

# Endpoints
@router.post("/crop-recommendation")
async def crop_recommendation_endpoint(
    nitrogen: float = Body(...),
    phosphorus: float = Body(...),
    potassium: float = Body(...),
    temperature: float = Body(...),
    humidity: float = Body(...),
    ph: float = Body(...),
    rainfall: float = Body(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get crop recommendations based on soil and climate parameters.
    """
    # Validate input parameters
    if not (0 <= nitrogen <= 300):
        raise HTTPException(status_code=400, detail="Nitrogen should be between 0 and 300")
    if not (0 <= phosphorus <= 300):
        raise HTTPException(status_code=400, detail="Phosphorus should be between 0 and 300")
    if not (0 <= potassium <= 300):
        raise HTTPException(status_code=400, detail="Potassium should be between 0 and 300")
    if not (-10 <= temperature <= 60):
        raise HTTPException(status_code=400, detail="Temperature should be between -10 and 60")
    if not (0 <= humidity <= 100):
        raise HTTPException(status_code=400, detail="Humidity should be between 0 and 100")
    if not (0 <= ph <= 14):
        raise HTTPException(status_code=400, detail="pH should be between 0 and 14")
    if not (0 <= rainfall <= 1000):
        raise HTTPException(status_code=400, detail="Rainfall should be between 0 and 1000")
    
    # Get recommendation
    recommendation = await get_crop_recommendation(
        nitrogen, phosphorus, potassium, temperature, humidity, ph, rainfall
    )
    
    # Store request and result in database
    parameters = {
        "nitrogen": nitrogen,
        "phosphorus": phosphorus,
        "potassium": potassium,
        "temperature": temperature,
        "humidity": humidity,
        "ph": ph,
        "rainfall": rainfall
    }
    
    agriculture_request = AgricultureRequest(
        user_id=current_user["id"],
        request_type="crop_recommendation",
        parameters=json.dumps(parameters),
        result=json.dumps(recommendation)
    )
    
    db.add(agriculture_request)
    db.commit()
    db.refresh(agriculture_request)
    
    # Store crop recommendation
    crop_recommendation = CropRecommendation(
        request_id=agriculture_request.id,
        crop=recommendation["recommended_crop"],
        confidence=recommendation["confidence"],
        soil_health=json.dumps(recommendation["soil_health"]),
        climate_suitability=json.dumps(recommendation["climate_suitability"])
    )
    
    db.add(crop_recommendation)
    db.commit()
    
    return recommendation

@router.post("/plant-disease-detection")
async def plant_disease_detection_endpoint(
    image: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Detect plant diseases from uploaded images.
    """
    # Validate file type
    if not image.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    # Read image data
    image_data = await image.read()
    
    # Analyze image for plant disease
    analysis_result = await analyze_plant_disease(image_data)
    
    # Store request and result in database
    parameters = {
        "image_filename": image.filename
    }
    
    agriculture_request = AgricultureRequest(
        user_id=current_user["id"],
        request_type="plant_disease_detection",
        parameters=json.dumps(parameters),
        result=json.dumps(analysis_result)
    )
    
    db.add(agriculture_request)
    db.commit()
    
    return analysis_result

@router.post("/weather-forecast")
async def weather_forecast_endpoint(
    latitude: float = Body(...),
    longitude: float = Body(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get weather forecast for agricultural planning.
    """
    # Validate coordinates
    if not (-90 <= latitude <= 90):
        raise HTTPException(status_code=400, detail="Latitude should be between -90 and 90")
    if not (-180 <= longitude <= 180):
        raise HTTPException(status_code=400, detail="Longitude should be between -180 and 180")
    
    # Get weather forecast
    forecast = await get_weather_forecast(latitude, longitude)
    
    # Store request and result in database
    parameters = {
        "latitude": latitude,
        "longitude": longitude
    }
    
    agriculture_request = AgricultureRequest(
        user_id=current_user["id"],
        request_type="weather_forecast",
        parameters=json.dumps(parameters),
        result=json.dumps(forecast)
    )
    
    db.add(agriculture_request)
    db.commit()
    
    return forecast

@router.get("/history", response_model=List[dict])
async def get_agriculture_history(
    request_type: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get history of agriculture requests for the current user.
    """
    query = db.query(AgricultureRequest).filter(AgricultureRequest.user_id == current_user["id"])
    
    if request_type:
        query = query.filter(AgricultureRequest.request_type == request_type)
    
    requests = query.order_by(AgricultureRequest.created_at.desc()).all()
    
    # Format response
    result = []
    for req in requests:
        item = {
            "id": req.id,
            "request_type": req.request_type,
            "parameters": json.loads(req.parameters),
            "result": json.loads(req.result),
            "created_at": req.created_at
        }
        
        # Add crop recommendation details if available
        if req.request_type == "crop_recommendation":
            crop_rec = db.query(CropRecommendation).filter(CropRecommendation.request_id == req.id).first()
            if crop_rec:
                item["crop_recommendation"] = {
                    "crop": crop_rec.crop,
                    "confidence": crop_rec.confidence,
                    "soil_health": json.loads(crop_rec.soil_health),
                    "climate_suitability": json.loads(crop_rec.climate_suitability)
                }
        
        result.append(item)
    
    return result
