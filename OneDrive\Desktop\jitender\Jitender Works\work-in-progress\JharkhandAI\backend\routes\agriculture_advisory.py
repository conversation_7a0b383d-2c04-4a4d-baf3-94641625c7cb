from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from typing import Dict, List
from database import get_db
from utils.auth import get_current_user
from models.enhanced_agriculture_model import EnhancedAgricultureModel
from models.animal_health_model import AnimalHealthModel
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/agriculture",
    tags=["agriculture"],
    responses={404: {"description": "Not found"}},
)

# Initialize models
agriculture_model = EnhancedAgricultureModel()
animal_health_model = AnimalHealthModel()

@router.post("/organic-farming-advisory")
async def organic_farming_advisory(
    data: Dict = Body(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get organic farming advisory including practices, pest management, and fertilizer recommendations.
    """
    try:
        # Extract parameters
        crop = data.get("crop")
        soil_type = data.get("soil_type")
        season = data.get("season")
        
        if not all([crop, soil_type, season]):
            raise HTTPException(status_code=400, detail="Missing required parameters")
        
        # Get organic farming advisory
        advisory = agriculture_model.get_organic_farming_advisory(crop, soil_type, season)
        
        if "error" in advisory:
            raise HTTPException(status_code=400, detail=advisory["error"])
        
        return advisory
        
    except Exception as e:
        logger.error(f"Error in organic farming advisory: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/animal-husbandry-advisory")
async def animal_husbandry_advisory(
    data: Dict = Body(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get animal husbandry advisory including health care, nutrition, and management practices.
    """
    try:
        # Extract parameters
        animal_type = data.get("animal_type")
        purpose = data.get("purpose")
        current_conditions = data.get("current_conditions")
        
        if not all([animal_type, purpose]):
            raise HTTPException(status_code=400, detail="Missing required parameters")
        
        # Get health status and recommendations
        health_status = animal_health_model.monitor_health_status({
            "type": animal_type,
            "purpose": purpose,
            "conditions": current_conditions
        })
        
        # Get nutrition optimization
        nutrition = animal_health_model.optimize_nutrition({
            "type": animal_type,
            "purpose": purpose
        })
        
        # Get vaccination schedule
        vaccinations = animal_health_model.get_vaccination_schedule({
            "type": animal_type,
            "purpose": purpose
        })
        
        return {
            "health_care": [
                {
                    "title": "Health Status",
                    "description": f"Overall health score: {health_status.get('health_score', 0):.1f}/10",
                    "recommendations": health_status.get('recommendations', [])
                },
                {
                    "title": "Vaccination Schedule",
                    "description": "Upcoming vaccinations",
                    "schedule": vaccinations.get('upcoming_vaccinations', [])
                }
            ],
            "nutrition": nutrition.get('daily_requirements', []),
            "management": [
                {
                    "practice": "Feeding Schedule",
                    "description": "Recommended feeding times and amounts",
                    "schedule": nutrition.get('feeding_schedule', [])
                },
                {
                    "practice": "Health Monitoring",
                    "description": "Regular health checks and monitoring",
                    "schedule": "Daily observation and weekly detailed check"
                }
            ],
            "best_practices": [
                {
                    "title": "Housing",
                    "description": "Ensure proper ventilation and adequate space"
                },
                {
                    "title": "Hygiene",
                    "description": "Regular cleaning and disinfection of living areas"
                },
                {
                    "title": "Exercise",
                    "description": "Provide adequate exercise and outdoor access when appropriate"
                }
            ]
        }
        
    except Exception as e:
        logger.error(f"Error in animal husbandry advisory: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))