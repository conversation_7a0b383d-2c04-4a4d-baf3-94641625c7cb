from fastapi import APIRouter, File, UploadFile, HTTPException
from typing import Dict, Any
from models.enhanced_agriculture_model import EnhancedAgricultureModel
import logging
import io
from PIL import Image
import torch
import numpy as np

logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter(
    prefix="/api/agriculture",
    tags=["agriculture"],
    responses={404: {"description": "Not found"}},
)

# Initialize agriculture model
agri_model = EnhancedAgricultureModel()

@router.post("/soil-analysis")
async def analyze_soil(image: UploadFile = File(...)) -> Dict[str, Any]:
    """Analyze soil health from uploaded image"""
    try:
        # Read and validate image
        contents = await image.read()
        img = Image.open(io.BytesIO(contents))
        
        # Process image and get soil analysis
        result = agri_model.analyze_soil_health(img, {})
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
            
        return result
    except Exception as e:
        logger.error(f"Error in soil analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/market-prices")
async def predict_prices(crop: str, location: str) -> Dict[str, Any]:
    """Predict market prices for crops"""
    try:
        result = agri_model.predict_market_prices(crop, location)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
            
        return result
    except Exception as e:
        logger.error(f"Error in price prediction: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/pest-recognition")
async def recognize_pest(image: UploadFile = File(...)) -> Dict[str, Any]:
    """Recognize pests from uploaded image and provide treatment recommendations"""
    try:
        # Read and validate image
        contents = await image.read()
        img = Image.open(io.BytesIO(contents))
        
        # Process image for pest recognition
        result = agri_model.analyze_pest(img)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
            
        return result
    except Exception as e:
        logger.error(f"Error in pest recognition: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))