from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from database import get_db
from models.database_models import User, ServiceLog, Feedback, RegionalUsage
from sqlalchemy import func

router = APIRouter()

@router.get("/dashboard")
async def get_analytics_dashboard(
    time_range: str = Query(..., regex="^(day|week|month)$"),
    db: Session = Depends(get_db)
):
    # Calculate date range
    now = datetime.utcnow()
    if time_range == "day":
        start_date = now - timedelta(days=1)
    elif time_range == "week":
        start_date = now - timedelta(weeks=1)
    else:  # month
        start_date = now - timedelta(days=30)

    # Get usage statistics
    usage_stats = (
        db.query(
            func.date_trunc('day', ServiceLog.timestamp).label('date'),
            func.count(func.distinct(ServiceLog.user_id)).label('active_users')
        )
        .filter(ServiceLog.timestamp >= start_date)
        .group_by(func.date_trunc('day', ServiceLog.timestamp))
        .order_by(func.date_trunc('day', ServiceLog.timestamp))
        .all()
    )

    # Get service bottlenecks
    bottlenecks = (
        db.query(
            ServiceLog.service_name,
            func.avg(ServiceLog.response_time).label('avg_response_time'),
            func.count().label('request_count')
        )
        .filter(ServiceLog.timestamp >= start_date)
        .group_by(ServiceLog.service_name)
        .order_by(func.avg(ServiceLog.response_time).desc())
        .limit(5)
        .all()
    )

    # Process bottlenecks with severity
    bottleneck_data = [{
        'service': b.service_name,
        'responseTime': float(b.avg_response_time),
        'requestCount': b.request_count,
        'severity': 'high' if b.avg_response_time > 1000 else 'medium',
        'description': f'Average response time: {int(b.avg_response_time)}ms'
    } for b in bottlenecks]

    # Get citizen satisfaction metrics
    satisfaction = (
        db.query(
            Feedback.satisfaction_level,
            func.count().label('count')
        )
        .filter(Feedback.timestamp >= start_date)
        .group_by(Feedback.satisfaction_level)
        .all()
    )

    satisfaction_data = {
        'Satisfied': 0,
        'Neutral': 0,
        'Unsatisfied': 0
    }
    for level, count in satisfaction:
        satisfaction_data[level] = count

    # Get regional usage patterns
    regional_patterns = (
        db.query(
            RegionalUsage.region,
            func.count().label('usage_count')
        )
        .filter(RegionalUsage.timestamp >= start_date)
        .group_by(RegionalUsage.region)
        .order_by(func.count().desc())
        .all()
    )

    regional_data = [{
        'region': r.region,
        'usage': r.usage_count
    } for r in regional_patterns]

    return {
        "usageStats": [{
            "date": stat.date.strftime("%Y-%m-%d"),
            "activeUsers": stat.active_users
        } for stat in usage_stats],
        "bottlenecks": bottleneck_data,
        "satisfaction": satisfaction_data,
        "regionalPatterns": regional_data
    }