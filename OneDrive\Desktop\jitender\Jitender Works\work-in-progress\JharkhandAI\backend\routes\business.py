from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any, Optional
from ..schemas.business_schemas import (
    BusinessAdvisoryRequest, 
    BusinessAdvisoryResponse,
    FinancialDataRequest,
    CashFlowResponse,
    FinancialPlanResponse,
    LoanEligibilityRequest,
    LoanEligibilityResponse,
    LoanApplicationRequest,
    LoanApplicationResponse,
    LoanScheme,
    MarketingStrategyRequest,
    MarketingStrategyResponse,
    MarketingContentRequest,
    MarketingContentResponse,
    CompetitorAnalysisRequest,
    CompetitorAnalysisResponse
)
from ..models.business_model import BusinessModel
from ..utils.auth import get_current_user

router = APIRouter(prefix="/api/business", tags=["business"])

business_model = BusinessModel()

# Business Advisory
@router.post("/advisory", response_model=BusinessAdvisoryResponse)
async def get_business_advisory(
    request: BusinessAdvisoryRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Get personalized business advisory recommendations based on business data.
    """
    try:
        result = business_model.generate_business_advisory(request)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate business advisory: {str(e)}"
        )

# Financial Planning - Cash Flow
@router.post("/financial/cash-flow", response_model=CashFlowResponse)
async def predict_cash_flow(
    request: FinancialDataRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Predict cash flow based on financial data.
    """
    try:
        result = business_model.predict_cash_flow(request)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to predict cash flow: {str(e)}"
        )

# Financial Planning - Financial Plan
@router.post("/financial/plan", response_model=FinancialPlanResponse)
async def generate_financial_plan(
    request: FinancialDataRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Generate a comprehensive financial plan based on financial data.
    """
    try:
        result = business_model.generate_financial_plan(request)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate financial plan: {str(e)}"
        )

# Loan Assistance - Get Loan Schemes
@router.get("/loan/schemes", response_model=List[LoanScheme])
async def get_loan_schemes(
    current_user: dict = Depends(get_current_user)
):
    """
    Get available loan schemes for businesses.
    """
    try:
        result = business_model.get_loan_schemes()
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get loan schemes: {str(e)}"
        )

# Loan Assistance - Check Eligibility
@router.post("/loan/eligibility", response_model=LoanEligibilityResponse)
async def check_loan_eligibility(
    request: LoanEligibilityRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Check eligibility for business loans based on business data.
    """
    try:
        result = business_model.check_loan_eligibility(request)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check loan eligibility: {str(e)}"
        )

# Loan Assistance - Generate Application
@router.post("/loan/application/{scheme_id}", response_model=LoanApplicationResponse)
async def generate_loan_application(
    scheme_id: str,
    request: LoanApplicationRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Generate a loan application for a specific scheme.
    """
    try:
        result = business_model.generate_loan_application(scheme_id, request)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate loan application: {str(e)}"
        )

# Marketing - Generate Strategy
@router.post("/marketing/strategy", response_model=MarketingStrategyResponse)
async def generate_marketing_strategy(
    request: MarketingStrategyRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Generate a marketing strategy based on business profile.
    """
    try:
        result = business_model.generate_marketing_strategy(request)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate marketing strategy: {str(e)}"
        )

# Marketing - Generate Content
@router.post("/marketing/content", response_model=MarketingContentResponse)
async def generate_marketing_content(
    request: MarketingContentRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Generate marketing content based on business profile and content type.
    """
    try:
        result = business_model.generate_marketing_content(
            request.business_profile,
            request.content_type,
            request.platform,
            request.count
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate marketing content: {str(e)}"
        )

# Marketing - Analyze Competitors
@router.post("/marketing/competitors", response_model=CompetitorAnalysisResponse)
async def analyze_competitors(
    request: CompetitorAnalysisRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Analyze competitors based on business profile.
    """
    try:
        result = business_model.analyze_competitors(request)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze competitors: {str(e)}"
        )
