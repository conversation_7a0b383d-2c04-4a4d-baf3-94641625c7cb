from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from database import get_db, ChatSession, ChatMessage, User
from utils.auth import get_current_user
import logging
from datetime import datetime
from pydantic import BaseModel
import json
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/chatbot",
    tags=["chatbot"],
    responses={404: {"description": "Not found"}},
)

# Pydantic models
class MessageBase(BaseModel):
    role: str
    content: str

class MessageCreate(MessageBase):
    pass

class MessageResponse(MessageBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True

class SessionBase(BaseModel):
    title: Optional[str] = None

class SessionCreate(SessionBase):
    pass

class SessionResponse(SessionBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    messages: List[MessageResponse] = []

    class Config:
        orm_mode = True

# Helper function to get AI response
async def get_ai_response(message: str, session_history: List[dict] = None):
    """
    Get response from AI model.
    This is a placeholder - in a real implementation, you would call your language model API.
    """
    try:
        # In a real implementation, you would call your language model API
        # For now, return a simple response based on keywords
        
        # Example of integrating with a language model API:
        # response = await call_language_model_api(message, session_history)
        
        # Simple keyword-based response for demonstration
        message_lower = message.lower()
        
        if "hello" in message_lower or "hi" in message_lower or "namaste" in message_lower:
            return "Namaste! How can I assist you with information about Jharkhand today?"
        
        elif "tourism" in message_lower or "visit" in message_lower or "travel" in message_lower:
            return "Jharkhand is known for its natural beauty, waterfalls, and tribal culture. Popular destinations include Netarhat, Hundru Falls, and Betla National Park. Would you like specific information about any of these places?"
        
        elif "agriculture" in message_lower or "farming" in message_lower or "crop" in message_lower:
            return "Jharkhand's main crops include rice, maize, pulses, and vegetables. The state also has programs to support sustainable farming practices. Do you need information about specific crops or agricultural schemes?"
        
        elif "healthcare" in message_lower or "hospital" in message_lower or "medical" in message_lower:
            return "Jharkhand has various healthcare facilities across districts. Major hospitals are located in Ranchi, Jamshedpur, and Dhanbad. Would you like information about healthcare schemes or finding a hospital near you?"
        
        elif "education" in message_lower or "school" in message_lower or "college" in message_lower:
            return "Jharkhand has several educational institutions including schools, colleges, and universities. Notable institutions include Ranchi University, BIT Mesra, and XLRI Jamshedpur. What specific information about education are you looking for?"
        
        elif "job" in message_lower or "employment" in message_lower or "work" in message_lower:
            return "Jharkhand has opportunities in mining, manufacturing, agriculture, and the service sector. The state government also runs various employment schemes. Would you like information about job opportunities or skill development programs?"
        
        elif "government" in message_lower or "scheme" in message_lower or "benefit" in message_lower:
            return "The Jharkhand government offers various welfare schemes including pension schemes, housing schemes, and educational scholarships. Would you like details about any specific government scheme?"
        
        else:
            return "I'm here to help you with information about Jharkhand's tourism, agriculture, healthcare, education, employment, and government services. How can I assist you today?"
    
    except Exception as e:
        logger.error(f"Error getting AI response: {e}")
        return "I'm sorry, I encountered an error processing your request. Please try again later."

# Endpoints
@router.post("/sessions", response_model=SessionResponse)
async def create_chat_session(
    session: SessionCreate = Body(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new chat session.
    """
    db_session = ChatSession(
        user_id=current_user["id"],
        title=session.title or "New Chat"
    )
    
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    
    # Add system message to initialize the chat
    system_message = ChatMessage(
        session_id=db_session.id,
        role="system",
        content="You are chatting with the Jharkhand AI Assistant. I can help you with information about tourism, agriculture, healthcare, education, employment, and government services in Jharkhand."
    )
    
    db.add(system_message)
    db.commit()
    
    # Refresh to include the system message
    db.refresh(db_session)
    return db_session

@router.get("/sessions", response_model=List[SessionResponse])
async def get_chat_sessions(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all chat sessions for the current user.
    """
    sessions = db.query(ChatSession).filter(
        ChatSession.user_id == current_user["id"]
    ).order_by(ChatSession.updated_at.desc()).offset(skip).limit(limit).all()
    
    return sessions

@router.get("/sessions/{session_id}", response_model=SessionResponse)
async def get_chat_session(
    session_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get a specific chat session by ID.
    """
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user["id"]
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    return session

@router.post("/sessions/{session_id}/messages", response_model=MessageResponse)
async def create_chat_message(
    session_id: int,
    message: MessageCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new message in a chat session and get AI response.
    """
    # Check if session exists and belongs to user
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user["id"]
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    # Create user message
    db_message = ChatMessage(
        session_id=session_id,
        role=message.role,
        content=message.content
    )
    
    db.add(db_message)
    db.commit()
    db.refresh(db_message)
    
    # Get session history for context
    session_messages = db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).order_by(ChatMessage.created_at).all()
    
    history = [{"role": msg.role, "content": msg.content} for msg in session_messages]
    
    # Get AI response
    ai_response_text = await get_ai_response(message.content, history)
    
    # Create AI response message
    ai_message = ChatMessage(
        session_id=session_id,
        role="assistant",
        content=ai_response_text
    )
    
    db.add(ai_message)
    
    # Update session timestamp
    session.updated_at = datetime.utcnow()
    
    # Update session title if it's the first user message
    if session.title == "New Chat" and len(session_messages) <= 2:  # Only system message and this user message
        # Use first few words of user message as title
        words = message.content.split()
        title = " ".join(words[:5])
        if len(words) > 5:
            title += "..."
        session.title = title
    
    db.commit()
    db.refresh(ai_message)
    
    return ai_message

@router.delete("/sessions/{session_id}")
async def delete_chat_session(
    session_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a chat session.
    """
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user["id"]
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    # Delete all messages in the session
    db.query(ChatMessage).filter(ChatMessage.session_id == session_id).delete()
    
    # Delete the session
    db.delete(session)
    db.commit()
    
    return {"message": "Chat session deleted successfully"}

@router.post("/policy-analysis", response_model=Dict)
async def analyze_policy_impact(policy_data: Dict, db: Session = Depends(get_db)):
    """Analyze policy impact and provide insights"""
    try:
        # Use enhanced government AI for policy analysis
        analysis = await government_ai.analyze_policy_impact(policy_data)
        return analysis
    except Exception as e:
        logger.error(f"Error analyzing policy: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/land-records", response_model=Dict)
async def process_land_record_query(query: ChatbotQuery, db: Session = Depends(get_db)):
    """Handle land record related queries"""
    try:
        # Process land record query
        response = await government_ai.process_land_record_query(
            query=query.query,
            language=query.language,
            context=query.context
        )
        return response
    except Exception as e:
        logger.error(f"Error processing land record query: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/bpl-verification", response_model=Dict)
async def verify_bpl_status(data: Dict, db: Session = Depends(get_db)):
    """Verify BPL card status and eligibility"""
    try:
        # Process BPL verification
        result = await government_ai.verify_bpl_status(data)
        return result
    except Exception as e:
        logger.error(f"Error verifying BPL status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/pension-services", response_model=Dict)
async def process_pension_query(query: ChatbotQuery, db: Session = Depends(get_db)):
    """Handle pension related services and queries"""
    try:
        # Process pension related query
        response = await government_ai.process_pension_query(
            query=query.query,
            language=query.language,
            context=query.context
        )
        return response
    except Exception as e:
        logger.error(f"Error processing pension query: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
