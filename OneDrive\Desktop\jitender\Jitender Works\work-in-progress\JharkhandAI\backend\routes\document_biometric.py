from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from typing import Optional
from models.document_biometric_model import DocumentBiometricAI
from utils.auth import get_current_user
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/government/documents",
    tags=["documents"],
    responses={404: {"description": "Not found"}},
)

# Initialize AI model
doc_biometric_ai = DocumentBiometricAI()

@router.post("/digitize")
async def digitize_document(
    document: UploadFile = File(...),
    doc_type: str = None,
    current_user: dict = Depends(get_current_user)
):
    """Digitize physical document using OCR"""
    try:
        # Validate document type
        if doc_type not in doc_biometric_ai.supported_doc_types:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported document type. Supported types: {doc_biometric_ai.supported_doc_types}"
            )

        # Read document image
        image_data = await document.read()

        # Process document
        result = await doc_biometric_ai.process_document(image_data, doc_type)

        return result

    except Exception as e:
        logger.error(f"Error digitizing document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/verify-biometric")
async def verify_biometric(
    biometric_data: UploadFile = File(...),
    biometric_type: str,
    stored_template: bytes,
    current_user: dict = Depends(get_current_user)
):
    """Verify biometric data against stored template"""
    try:
        # Validate biometric type
        if biometric_type not in doc_biometric_ai.supported_biometrics:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported biometric type. Supported types: {doc_biometric_ai.supported_biometrics}"
            )

        # Read biometric data
        data = await biometric_data.read()

        # Verify biometric
        result = await doc_biometric_ai.verify_biometric(
            data,
            biometric_type,
            stored_template
        )

        return result

    except Exception as e:
        logger.error(f"Error verifying biometric: {e}")
        raise HTTPException(status_code=500, detail=str(e))