from fastapi import APIRouter, Depends, HTTPException, Body, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
from database import get_db, Course, UserCourse
from utils.auth import get_current_user, get_current_admin_user
from models.enhanced_education_model import EnhancedEducationModel
import logging
from datetime import datetime
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize education model
education_model = EnhancedEducationModel()

# Create router
router = APIRouter(
    prefix="/api/education",
    tags=["education"],
    responses={404: {"description": "Not found"}},
)

class LearningPathRequest(BaseModel):
    interests: List[str]
    current_level: str
    progress: Optional[float] = None

class PeerConnectionRequest(BaseModel):
    peer_id: str

# Pydantic models
class CourseBase(BaseModel):
    title: str
    description: str
    provider: str
    category: str
    level: str
    duration: str
    language: str
    url: str
    is_free: bool = False

class CourseCreate(CourseBase):
    pass

class CourseResponse(CourseBase):
    id: int
    created_at: datetime

    class Config:
        orm_mode = True

class UserCourseBase(BaseModel):
    course_id: int
    status: str = "enrolled"
    progress: float = 0.0

class UserCourseCreate(UserCourseBase):
    pass

class UserCourseResponse(UserCourseBase):
    id: int
    user_id: int
    enrolled_at: datetime
    completed_at: Optional[datetime] = None
    course: CourseResponse

    class Config:
        orm_mode = True

# Endpoints
@router.post('/voice-learning')
async def voice_based_learning(request: VoiceLearningRequest):
    """Process voice input for AI-powered learning"""
    result = education_model.voice_based_learning(
        audio_input=request.audio_input,
        language=request.language,
        subject=request.subject
    )
    if 'error' in result:
        raise HTTPException(status_code=400, detail=result['error'])
    return result

@router.get("/user-progress")
async def get_user_progress(current_user = Depends(get_current_user)):
    try:
        progress = education_model.get_user_progress(current_user["id"])
        return progress
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/user-interests")
async def get_user_interests(current_user = Depends(get_current_user)):
    try:
        interests = education_model.get_user_interests(current_user["id"])
        return {"interests": interests}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/peer-suggestions")
async def get_peer_suggestions(current_user = Depends(get_current_user)):
    try:
        peers = education_model.get_peer_suggestions(current_user["id"])
        return {"peers": peers}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/learning-path")
async def generate_learning_path(
    request: LearningPathRequest,
    current_user = Depends(get_current_user)
):
    try:
        path = education_model.generate_learning_path(
            user_id=current_user["id"],
            interests=request.interests,
            current_level=request.current_level,
            progress=request.progress
        )
        return path
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/connect-peer")
async def connect_with_peer(
    request: PeerConnectionRequest,
    current_user = Depends(get_current_user)
):
    try:
        result = education_model.connect_peers(current_user["id"], request.peer_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/courses", response_model=List[CourseResponse])
async def get_courses(
    category: Optional[str] = None,
    level: Optional[str] = None,
    language: Optional[str] = None,
    is_free: Optional[bool] = None,
    search: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all courses with optional filtering.
    """
    query = db.query(Course)
    
    if category:
        query = query.filter(Course.category == category)
    
    if level:
        query = query.filter(Course.level == level)
    
    if language:
        query = query.filter(Course.language == language)
    
    if is_free is not None:
        query = query.filter(Course.is_free == is_free)
    
    if search:
        query = query.filter(
            Course.title.ilike(f"%{search}%") | 
            Course.description.ilike(f"%{search}%") |
            Course.provider.ilike(f"%{search}%")
        )
    
    courses = query.offset(skip).limit(limit).all()
    return courses

@router.get("/courses/{course_id}", response_model=CourseResponse)
async def get_course(course_id: int, db: Session = Depends(get_db)):
    """
    Get a specific course by ID.
    """
    course = db.query(Course).filter(Course.id == course_id).first()
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    return course

@router.post("/courses", response_model=CourseResponse)
async def create_course(
    course: CourseCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Create a new course (admin only).
    """
    db_course = Course(**course.dict())
    db.add(db_course)
    db.commit()
    db.refresh(db_course)
    return db_course

@router.put("/courses/{course_id}", response_model=CourseResponse)
async def update_course(
    course_id: int,
    course: CourseCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Update a course (admin only).
    """
    db_course = db.query(Course).filter(Course.id == course_id).first()
    if not db_course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    for key, value in course.dict().items():
        setattr(db_course, key, value)
    
    db.commit()
    db.refresh(db_course)
    return db_course

@router.delete("/courses/{course_id}")
async def delete_course(
    course_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Delete a course (admin only).
    """
    db_course = db.query(Course).filter(Course.id == course_id).first()
    if not db_course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    db.delete(db_course)
    db.commit()
    return {"message": "Course deleted successfully"}

@router.post("/enroll", response_model=UserCourseResponse)
async def enroll_in_course(
    course_id: int = Body(..., embed=True),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Enroll in a course.
    """
    # Check if course exists
    course = db.query(Course).filter(Course.id == course_id).first()
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    
    # Check if user is already enrolled
    existing_enrollment = db.query(UserCourse).filter(
        UserCourse.course_id == course_id,
        UserCourse.user_id == current_user["id"]
    ).first()
    
    if existing_enrollment:
        raise HTTPException(status_code=400, detail="You are already enrolled in this course")
    
    # Create enrollment
    enrollment = UserCourse(
        user_id=current_user["id"],
        course_id=course_id,
        status="enrolled",
        progress=0.0
    )
    
    db.add(enrollment)
    db.commit()
    db.refresh(enrollment)
    
    # Include course details in response
    enrollment.course = course
    
    return enrollment

@router.get("/my-courses", response_model=List[UserCourseResponse])
async def get_my_courses(
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all courses the current user is enrolled in.
    """
    query = db.query(UserCourse).filter(UserCourse.user_id == current_user["id"])
    
    if status:
        query = query.filter(UserCourse.status == status)
    
    enrollments = query.all()
    
    # Include course details for each enrollment
    for enrollment in enrollments:
        enrollment.course = db.query(Course).filter(Course.id == enrollment.course_id).first()
    
    return enrollments

@router.put("/my-courses/{enrollment_id}", response_model=UserCourseResponse)
async def update_course_progress(
    enrollment_id: int,
    progress: float = Body(..., embed=True),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Update progress for an enrolled course.
    """
    # Validate progress value
    if progress < 0 or progress > 100:
        raise HTTPException(status_code=400, detail="Progress must be between 0 and 100")
    
    # Check if enrollment exists and belongs to user
    enrollment = db.query(UserCourse).filter(
        UserCourse.id == enrollment_id,
        UserCourse.user_id == current_user["id"]
    ).first()
    
    if not enrollment:
        raise HTTPException(status_code=404, detail="Enrollment not found")
    
    # Update progress
    enrollment.progress = progress
    
    # Update status if completed
    if progress >= 100:
        enrollment.status = "completed"
        enrollment.completed_at = datetime.utcnow()
    elif progress > 0:
        enrollment.status = "in_progress"
    
    db.commit()
    db.refresh(enrollment)
    
    # Include course details in response
    enrollment.course = db.query(Course).filter(Course.id == enrollment.course_id).first()
    
    return enrollment

@router.delete("/my-courses/{enrollment_id}")
async def unenroll_from_course(
    enrollment_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Unenroll from a course.
    """
    # Check if enrollment exists and belongs to user
    enrollment = db.query(UserCourse).filter(
        UserCourse.id == enrollment_id,
        UserCourse.user_id == current_user["id"]
    ).first()
    
    if not enrollment:
        raise HTTPException(status_code=404, detail="Enrollment not found")
    
    db.delete(enrollment)
    db.commit()
    
    return {"message": "Successfully unenrolled from course"}

@router.get("/categories", response_model=List[str])
async def get_course_categories(db: Session = Depends(get_db)):
    """
    Get all unique course categories.
    """
    categories = db.query(Course.category).distinct().all()
    return [cat[0] for cat in categories]

@router.get("/levels", response_model=List[str])
async def get_course_levels(db: Session = Depends(get_db)):
    """
    Get all unique course levels.
    """
    levels = db.query(Course.level).distinct().all()
    return [level[0] for level in levels]

@router.get("/languages", response_model=List[str])
async def get_course_languages(db: Session = Depends(get_db)):
    """
    Get all unique course languages.
    """
    languages = db.query(Course.language).distinct().all()
    return [lang[0] for lang in languages]

@router.get("/providers", response_model=List[str])
async def get_course_providers(db: Session = Depends(get_db)):
    """
    Get all unique course providers.
    """
    providers = db.query(Course.provider).distinct().all()
    return [provider[0] for provider in providers]
