from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from pydantic import BaseModel
import logging

from ..models.enhanced_tourism_model import EnhancedTourismModel
from ..database import get_db
from ..utils.auth import get_current_user

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/tourism/enhanced",
    tags=["enhanced-tourism"],
    responses={404: {"description": "Not found"}},
)

# Initialize models
tourism_model = EnhancedTourismModel()

# Pydantic models
class VirtualExhibitRequest(BaseModel):
    artifact_name: str
    description: str
    language: str = "en"

class ARExperienceElement(BaseModel):
    name: str
    description: str
    position: Dict
    interaction_type: str = "view"

class ARExperienceRequest(BaseModel):
    location_name: str
    cultural_elements: List[ARExperienceElement]
    language: str = "en"

class TourismPreferences(BaseModel):
    interests: List[str]
    duration: int = 3
    include_tribal: bool = True
    budget: str = "medium"
    language: str = "en"

# Endpoints
@router.post("/virtual-exhibit")
async def create_virtual_exhibit(
    request: VirtualExhibitRequest,
    current_user: dict = Depends(get_current_user)
):
    """Create a virtual museum exhibit with AI-generated content"""
    try:
        result = tourism_model.generate_virtual_exhibit(
            artifact_name=request.artifact_name,
            description=request.description,
            language=request.language
        )
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return result
    except Exception as e:
        logger.error(f"Error creating virtual exhibit: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ar-experience")
async def create_ar_experience(
    request: ARExperienceRequest,
    current_user: dict = Depends(get_current_user)
):
    """Create an AR/VR cultural immersion experience"""
    try:
        result = tourism_model.create_ar_experience(
            location_name=request.location_name,
            cultural_elements=[element.dict() for element in request.cultural_elements],
            language=request.language
        )
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return result
    except Exception as e:
        logger.error(f"Error creating AR experience: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/recommendations")
async def get_recommendations(
    preferences: TourismPreferences,
    current_user: dict = Depends(get_current_user)
):
    """Get personalized tourism recommendations with AI-driven translations"""
    try:
        result = tourism_model.get_personalized_recommendations(
            user_preferences=preferences.dict(),
            language=preferences.language
        )
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return result
    except Exception as e:
        logger.error(f"Error getting recommendations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/destinations")
async def get_destinations(
    language: str = Query("en", description="Language code for translations"),
    category: Optional[str] = Query(None, description="Filter by category"),
    include_tribal: bool = Query(True, description="Include tribal destinations"),
    db: Session = Depends(get_db)
):
    """Get list of destinations with translations"""
    try:
        destinations = tourism_model.tourism_data.get("destinations", [])
        
        # Apply filters
        if category:
            destinations = [d for d in destinations if category in d.get("tags", [])]
        if not include_tribal:
            destinations = [d for d in destinations if not d.get("is_tribal", False)]
        
        # Translate if needed
        if language != "en":
            for dest in destinations:
                dest["name"] = tourism_model.language_model.translate(
                    text=dest["name"],
                    source_lang="en",
                    target_lang=language
                )
                dest["description"] = tourism_model.language_model.translate(
                    text=dest["description"],
                    source_lang="en",
                    target_lang=language
                )
        
        return {"destinations": destinations, "language": language}
    except Exception as e:
        logger.error(f"Error getting destinations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))