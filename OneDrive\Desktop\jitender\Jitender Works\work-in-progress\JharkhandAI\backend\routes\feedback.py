from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
from ..database import get_db
from ..models.feedback_models import Feedback, FeatureUsage, UserSatisfactionSummary
from ..schemas.feedback_schemas import (
    FeedbackCreate, FeedbackResponse,
    FeatureUsageCreate, FeatureUsageResponse,
    UserSatisfactionMetrics
)

router = APIRouter()

@router.post("/feedback", response_model=FeedbackResponse)
async def submit_feedback(feedback: FeedbackCreate, db: Session = Depends(get_db)):
    db_feedback = Feedback(**feedback.dict())
    db.add(db_feedback)
    db.commit()
    db.refresh(db_feedback)
    return db_feedback

@router.post("/feature-usage", response_model=FeatureUsageResponse)
async def log_feature_usage(usage: FeatureUsageCreate, db: Session = Depends(get_db)):
    db_usage = FeatureUsage(**usage.dict())
    db.add(db_usage)
    db.commit()
    db.refresh(db_usage)
    return db_usage

@router.get("/analytics/satisfaction", response_model=UserSatisfactionMetrics)
async def get_satisfaction_metrics(
    period: str = "daily",
    start_date: Optional[datetime] = None,
    db: Session = Depends(get_db)
):
    if not start_date:
        start_date = datetime.now() - timedelta(days=30)
    
    # Query feedback data for the specified period
    feedbacks = db.query(Feedback).filter(
        Feedback.created_at >= start_date
    ).all()
    
    # Calculate metrics
    total_rating = sum(f.rating for f in feedbacks)
    total_count = len(feedbacks)
    
    # Feature-specific ratings
    feature_ratings = {}
    for feedback in feedbacks:
        if feedback.feature_id not in feature_ratings:
            feature_ratings[feedback.feature_id] = []
        feature_ratings[feedback.feature_id].append(feedback.rating)
    
    # Calculate average ratings per feature
    for feature_id in feature_ratings:
        ratings = feature_ratings[feature_id]
        feature_ratings[feature_id] = sum(ratings) / len(ratings)
    
    # Calculate sentiment distribution
    sentiment_dist = {}
    for feedback in feedbacks:
        if feedback.sentiment:
            sentiment_dist[feedback.sentiment] = sentiment_dist.get(feedback.sentiment, 0) + 1
    
    return UserSatisfactionMetrics(
        overall_rating=total_rating / total_count if total_count > 0 else 0,
        feature_ratings=feature_ratings,
        sentiment_distribution=sentiment_dist,
        total_feedback_count=total_count,
        period=period,
        start_date=start_date,
        end_date=datetime.now()
    )

@router.get("/analytics/feature-usage")
async def get_feature_usage_metrics(
    feature_id: Optional[str] = None,
    start_date: Optional[datetime] = None,
    db: Session = Depends(get_db)
):
    if not start_date:
        start_date = datetime.now() - timedelta(days=30)
    
    query = db.query(FeatureUsage).filter(FeatureUsage.timestamp >= start_date)
    if feature_id:
        query = query.filter(FeatureUsage.feature_id == feature_id)
    
    usages = query.all()
    
    # Calculate usage metrics
    metrics = {
        "total_usage_count": len(usages),
        "success_rate": sum(1 for u in usages if u.success) / len(usages) if usages else 0,
        "average_duration": sum(u.duration for u in usages if u.duration) / len(usages) if usages else 0,
        "error_distribution": {}
    }
    
    # Calculate error distribution
    for usage in usages:
        if not usage.success and usage.error_type:
            metrics["error_distribution"][usage.error_type] = \
                metrics["error_distribution"].get(usage.error_type, 0) + 1
    
    return metrics