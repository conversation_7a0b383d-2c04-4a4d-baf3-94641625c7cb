from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from database import get_db, GovernmentScheme
from utils.auth import get_current_user
import logging
from datetime import datetime
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/government",
    tags=["government"],
    responses={404: {"description": "Not found"}},
)

# Pydantic models
class SchemeBase(BaseModel):
    title: str
    description: str
    eligibility: str
    benefits: str
    documents_required: str
    application_process: str
    department: str
    contact_info: str
    website: Optional[str] = None

class SchemeCreate(SchemeBase):
    pass

class SchemeResponse(SchemeBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

# Endpoints
@router.get("/schemes", response_model=List[SchemeResponse])
async def get_schemes(
    department: Optional[str] = None,
    search: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all government schemes with optional filtering.
    """
    query = db.query(GovernmentScheme)
    
    if department:
        query = query.filter(GovernmentScheme.department == department)
    
    if search:
        query = query.filter(
            GovernmentScheme.title.ilike(f"%{search}%") | 
            GovernmentScheme.description.ilike(f"%{search}%") |
            GovernmentScheme.eligibility.ilike(f"%{search}%")
        )
    
    schemes = query.offset(skip).limit(limit).all()
    return schemes

@router.get("/schemes/{scheme_id}", response_model=SchemeResponse)
async def get_scheme(scheme_id: int, db: Session = Depends(get_db)):
    """
    Get a specific government scheme by ID.
    """
    scheme = db.query(GovernmentScheme).filter(GovernmentScheme.id == scheme_id).first()
    if not scheme:
        raise HTTPException(status_code=404, detail="Scheme not found")
    return scheme

@router.post("/schemes", response_model=SchemeResponse)
async def create_scheme(
    scheme: SchemeCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new government scheme (admin only).
    """
    if not current_user.get("is_admin", False):
        raise HTTPException(status_code=403, detail="Not authorized to create schemes")
    
    db_scheme = GovernmentScheme(**scheme.dict())
    db.add(db_scheme)
    db.commit()
    db.refresh(db_scheme)
    return db_scheme

@router.put("/schemes/{scheme_id}", response_model=SchemeResponse)
async def update_scheme(
    scheme_id: int,
    scheme: SchemeCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Update a government scheme (admin only).
    """
    if not current_user.get("is_admin", False):
        raise HTTPException(status_code=403, detail="Not authorized to update schemes")
    
    db_scheme = db.query(GovernmentScheme).filter(GovernmentScheme.id == scheme_id).first()
    if not db_scheme:
        raise HTTPException(status_code=404, detail="Scheme not found")
    
    for key, value in scheme.dict().items():
        setattr(db_scheme, key, value)
    
    db_scheme.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_scheme)
    return db_scheme

@router.delete("/schemes/{scheme_id}")
async def delete_scheme(
    scheme_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a government scheme (admin only).
    """
    if not current_user.get("is_admin", False):
        raise HTTPException(status_code=403, detail="Not authorized to delete schemes")
    
    db_scheme = db.query(GovernmentScheme).filter(GovernmentScheme.id == scheme_id).first()
    if not db_scheme:
        raise HTTPException(status_code=404, detail="Scheme not found")
    
    db.delete(db_scheme)
    db.commit()
    return {"message": "Scheme deleted successfully"}

@router.get("/departments", response_model=List[str])
async def get_departments(db: Session = Depends(get_db)):
    """
    Get all unique government departments.
    """
    departments = db.query(GovernmentScheme.department).distinct().all()
    return [dept[0] for dept in departments]

@router.get("/eligibility-check", response_model=List[SchemeResponse])
async def check_eligibility(
    age: Optional[int] = None,
    income: Optional[float] = None,
    district: Optional[str] = None,
    occupation: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Check eligibility for government schemes based on user parameters.
    """
    # This is a simplified implementation
    # In a real application, you would have more sophisticated eligibility criteria
    
    # For now, just do a simple text search in the eligibility field
    query = db.query(GovernmentScheme)
    
    if age:
        query = query.filter(GovernmentScheme.eligibility.ilike(f"%age%{age}%"))
    
    if income:
        query = query.filter(GovernmentScheme.eligibility.ilike(f"%income%{income}%"))
    
    if district:
        query = query.filter(GovernmentScheme.eligibility.ilike(f"%{district}%"))
    
    if occupation:
        query = query.filter(GovernmentScheme.eligibility.ilike(f"%{occupation}%"))
    
    # If user has a district in their profile, also check for that
    if current_user.get("district"):
        query = query.filter(GovernmentScheme.eligibility.ilike(f"%{current_user['district']}%"))
    
    schemes = query.all()
    return schemes
