from fastapi import APIRouter, Depends, HTTPException, Body, File, UploadFile
from sqlalchemy.orm import Session
from typing import List, Optional
from database import get_db
from models.government_policy_model import Grievance, GrievanceStatus, GrievanceCategory, PolicyAnalytics
from utils.auth import get_current_user
import logging
from datetime import datetime
from pydantic import BaseModel
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/grievance",
    tags=["grievance"],
    responses={404: {"description": "Not found"}},
)

# Pydantic models
class GrievanceBase(BaseModel):
    category: GrievanceCategory
    subject: str
    description: str
    location: str
    priority: Optional[int] = 1

class GrievanceCreate(GrievanceBase):
    pass

class GrievanceUpdate(BaseModel):
    status: Optional[GrievanceStatus]
    resolution: Optional[str]
    assigned_to: Optional[int]

class GrievanceResponse(GrievanceBase):
    id: int
    status: GrievanceStatus
    created_at: datetime
    updated_at: Optional[datetime]
    resolution: Optional[str]
    assigned_to: Optional[int]

    class Config:
        orm_mode = True

# Endpoints
@router.post("/", response_model=GrievanceResponse)
async def create_grievance(
    grievance: GrievanceCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Create a new grievance"""
    db_grievance = Grievance(
        **grievance.dict(),
        user_id=current_user["id"]
    )
    db.add(db_grievance)
    db.commit()
    db.refresh(db_grievance)
    return db_grievance

@router.get("/", response_model=List[GrievanceResponse])
async def list_grievances(
    status: Optional[GrievanceStatus] = None,
    category: Optional[GrievanceCategory] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """List grievances with optional filters"""
    query = db.query(Grievance)
    
    # Regular users can only see their own grievances
    # Admin users can see all grievances
    if not current_user.get("is_admin", False):
        query = query.filter(Grievance.user_id == current_user["id"])
    
    if status:
        query = query.filter(Grievance.status == status)
    if category:
        query = query.filter(Grievance.category == category)
    
    return query.offset(skip).limit(limit).all()

@router.get("/{grievance_id}", response_model=GrievanceResponse)
async def get_grievance(
    grievance_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get a specific grievance"""
    grievance = db.query(Grievance).filter(Grievance.id == grievance_id).first()
    if not grievance:
        raise HTTPException(status_code=404, detail="Grievance not found")
    
    # Check if user has access to this grievance
    if not current_user.get("is_admin", False) and grievance.user_id != current_user["id"]:
        raise HTTPException(status_code=403, detail="Not authorized to access this grievance")
    
    return grievance

@router.put("/{grievance_id}", response_model=GrievanceResponse)
async def update_grievance(
    grievance_id: int,
    grievance_update: GrievanceUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update a grievance (admin only)"""
    if not current_user.get("is_admin", False):
        raise HTTPException(status_code=403, detail="Not authorized to update grievances")
    
    db_grievance = db.query(Grievance).filter(Grievance.id == grievance_id).first()
    if not db_grievance:
        raise HTTPException(status_code=404, detail="Grievance not found")
    
    for field, value in grievance_update.dict(exclude_unset=True).items():
        setattr(db_grievance, field, value)
    
    db.commit()
    db.refresh(db_grievance)
    return db_grievance

@router.post("/{grievance_id}/attachments")
async def add_attachment(
    grievance_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Add an attachment to a grievance"""
    grievance = db.query(Grievance).filter(Grievance.id == grievance_id).first()
    if not grievance:
        raise HTTPException(status_code=404, detail="Grievance not found")
    
    if not current_user.get("is_admin", False) and grievance.user_id != current_user["id"]:
        raise HTTPException(status_code=403, detail="Not authorized to modify this grievance")
    
    # Handle file upload and update attachments
    # This is a placeholder - implement actual file storage logic
    attachment_info = {
        "filename": file.filename,
        "content_type": file.content_type,
        "upload_time": datetime.now().isoformat()
    }
    
    if not grievance.attachments:
        grievance.attachments = [attachment_info]
    else:
        grievance.attachments.append(attachment_info)
    
    db.commit()
    return {"message": "Attachment added successfully", "attachment": attachment_info}