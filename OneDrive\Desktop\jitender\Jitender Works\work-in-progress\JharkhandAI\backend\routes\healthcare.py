from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from typing import List, Optional
from database import get_db, HealthFacility, HealthRecord, SymptomCheck
from utils.auth import get_current_user, get_current_admin_user
from models.symptom_checker_model import SymptomCheckerModel
import logging
from datetime import datetime
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize symptom checker model
symptom_checker = SymptomCheckerModel()

# Create router
router = APIRouter(
    prefix="/api/healthcare",
    tags=["healthcare"],
    responses={404: {"description": "Not found"}},
)

# Pydantic models
class FacilityBase(BaseModel):
    name: str
    facility_type: str
    address: str
    district: str
    contact_number: Optional[str] = None
    email: Optional[str] = None
    services: Optional[str] = None
    operating_hours: Optional[str] = None
    emergency_services: bool = False

class FacilityCreate(FacilityBase):
    pass

class FacilityResponse(FacilityBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class HealthRecordBase(BaseModel):
    record_type: str
    date: datetime
    facility_id: Optional[int] = None
    doctor_name: Optional[str] = None
    diagnosis: Optional[str] = None
    prescription: Optional[str] = None
    notes: Optional[str] = None

class HealthRecordCreate(HealthRecordBase):
    pass

class HealthRecordResponse(HealthRecordBase):
    id: int
    user_id: int
    created_at: datetime
    facility: Optional[FacilityResponse] = None

    class Config:
        orm_mode = True

class SymptomCheckBase(BaseModel):
    symptoms: str

class SymptomCheckCreate(SymptomCheckBase):
    pass

class SymptomCheckResponse(SymptomCheckBase):
    id: int
    user_id: int
    result: str
    recommendation: str
    created_at: datetime

    class Config:
        orm_mode = True

# Helper function for symptom checking
async def check_symptoms(symptoms: str):
    """
    Analyze symptoms and provide recommendations.
    This is a placeholder - in a real implementation, you would use a medical AI model.
    """
    # In a real implementation, you would call a medical AI model
    # For now, return a simple response based on keywords
    
    symptoms_lower = symptoms.lower()
    
    # Very basic symptom checking logic - this should be replaced with a proper medical AI
    if "fever" in symptoms_lower and ("cough" in symptoms_lower or "sore throat" in symptoms_lower):
        result = "Possible respiratory infection"
        recommendation = "Rest, stay hydrated, and consider visiting a healthcare facility if symptoms persist for more than 3 days or worsen."
    
    elif "headache" in symptoms_lower and "nausea" in symptoms_lower:
        result = "Possible migraine or tension headache"
        recommendation = "Rest in a quiet, dark room. Over-the-counter pain relievers may help. If severe or recurring, consult a doctor."
    
    elif "stomach" in symptoms_lower and ("pain" in symptoms_lower or "ache" in symptoms_lower):
        result = "Possible digestive issue"
        recommendation = "Avoid spicy or heavy foods. Stay hydrated. If pain is severe or persists, consult a doctor."
    
    elif "rash" in symptoms_lower or "itching" in symptoms_lower:
        result = "Possible allergic reaction or skin condition"
        recommendation = "Avoid potential allergens. Consider over-the-counter antihistamines. If severe or spreading, seek medical attention."
    
    else:
        result = "General discomfort"
        recommendation = "Monitor your symptoms. Rest and stay hydrated. If symptoms persist or worsen, consult a healthcare professional."
    
    return result, recommendation

# Endpoints
@router.get("/facilities", response_model=List[FacilityResponse])
async def get_health_facilities(
    district: Optional[str] = None,
    facility_type: Optional[str] = None,
    emergency_services: Optional[bool] = None,
    search: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all health facilities with optional filtering.
    """
    query = db.query(HealthFacility)
    
    if district:
        query = query.filter(HealthFacility.district == district)
    
    if facility_type:
        query = query.filter(HealthFacility.facility_type == facility_type)
    
    if emergency_services is not None:
        query = query.filter(HealthFacility.emergency_services == emergency_services)
    
    if search:
        query = query.filter(
            HealthFacility.name.ilike(f"%{search}%") | 
            HealthFacility.address.ilike(f"%{search}%") |
            HealthFacility.services.ilike(f"%{search}%")
        )
    
    facilities = query.offset(skip).limit(limit).all()
    return facilities

@router.get("/facilities/{facility_id}", response_model=FacilityResponse)
async def get_health_facility(facility_id: int, db: Session = Depends(get_db)):
    """
    Get a specific health facility by ID.
    """
    facility = db.query(HealthFacility).filter(HealthFacility.id == facility_id).first()
    if not facility:
        raise HTTPException(status_code=404, detail="Health facility not found")
    return facility

@router.post("/facilities", response_model=FacilityResponse)
async def create_health_facility(
    facility: FacilityCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Create a new health facility (admin only).
    """
    db_facility = HealthFacility(**facility.dict())
    db.add(db_facility)
    db.commit()
    db.refresh(db_facility)
    return db_facility

@router.put("/facilities/{facility_id}", response_model=FacilityResponse)
async def update_health_facility(
    facility_id: int,
    facility: FacilityCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Update a health facility (admin only).
    """
    db_facility = db.query(HealthFacility).filter(HealthFacility.id == facility_id).first()
    if not db_facility:
        raise HTTPException(status_code=404, detail="Health facility not found")
    
    for key, value in facility.dict().items():
        setattr(db_facility, key, value)
    
    db_facility.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_facility)
    return db_facility

@router.delete("/facilities/{facility_id}")
async def delete_health_facility(
    facility_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Delete a health facility (admin only).
    """
    db_facility = db.query(HealthFacility).filter(HealthFacility.id == facility_id).first()
    if not db_facility:
        raise HTTPException(status_code=404, detail="Health facility not found")
    
    db.delete(db_facility)
    db.commit()
    return {"message": "Health facility deleted successfully"}

@router.post("/records", response_model=HealthRecordResponse)
async def create_health_record(
    record: HealthRecordCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new health record for the current user.
    """
    # Check if facility exists if provided
    if record.facility_id:
        facility = db.query(HealthFacility).filter(HealthFacility.id == record.facility_id).first()
        if not facility:
            raise HTTPException(status_code=404, detail="Health facility not found")
    
    # Create health record
    db_record = HealthRecord(
        **record.dict(),
        user_id=current_user["id"]
    )
    
    db.add(db_record)
    db.commit()
    db.refresh(db_record)
    
    # Include facility details if available
    if record.facility_id:
        db_record.facility = facility
    
    return db_record

@router.get("/records", response_model=List[HealthRecordResponse])
async def get_health_records(
    record_type: Optional[str] = None,
    from_date: Optional[datetime] = None,
    to_date: Optional[datetime] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all health records for the current user with optional filtering.
    """
    query = db.query(HealthRecord).filter(HealthRecord.user_id == current_user["id"])
    
    if record_type:
        query = query.filter(HealthRecord.record_type == record_type)
    
    if from_date:
        query = query.filter(HealthRecord.date >= from_date)
    
    if to_date:
        query = query.filter(HealthRecord.date <= to_date)
    
    records = query.order_by(HealthRecord.date.desc()).all()
    
    # Include facility details for each record
    for record in records:
        if record.facility_id:
            record.facility = db.query(HealthFacility).filter(HealthFacility.id == record.facility_id).first()
    
    return records

@router.get("/records/{record_id}", response_model=HealthRecordResponse)
async def get_health_record(
    record_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get a specific health record by ID.
    """
    record = db.query(HealthRecord).filter(
        HealthRecord.id == record_id,
        HealthRecord.user_id == current_user["id"]
    ).first()
    
    if not record:
        raise HTTPException(status_code=404, detail="Health record not found")
    
    # Include facility details if available
    if record.facility_id:
        record.facility = db.query(HealthFacility).filter(HealthFacility.id == record.facility_id).first()
    
    return record

@router.delete("/records/{record_id}")
async def delete_health_record(
    record_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a health record.
    """
    record = db.query(HealthRecord).filter(
        HealthRecord.id == record_id,
        HealthRecord.user_id == current_user["id"]
    ).first()
    
    if not record:
        raise HTTPException(status_code=404, detail="Health record not found")
    
    db.delete(record)
    db.commit()
    return {"message": "Health record deleted successfully"}

@router.post("/symptom-check", response_model=SymptomCheckResponse)
async def check_symptoms_endpoint(
    symptom_data: SymptomCheckCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Check symptoms and get recommendations.
    """
    # Analyze symptoms
    result, recommendation = await check_symptoms(symptom_data.symptoms)
    
    # Create symptom check record
    symptom_check = SymptomCheck(
        user_id=current_user["id"],
        symptoms=symptom_data.symptoms,
        result=result,
        recommendation=recommendation
    )
    
    db.add(symptom_check)
    db.commit()
    db.refresh(symptom_check)
    
    return symptom_check

@router.get("/symptom-checks", response_model=List[SymptomCheckResponse])
async def get_symptom_checks(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all symptom checks for the current user.
    """
    checks = db.query(SymptomCheck).filter(
        SymptomCheck.user_id == current_user["id"]
    ).order_by(SymptomCheck.created_at.desc()).all()
    
    return checks

@router.get("/facility-types", response_model=List[str])
async def get_facility_types(db: Session = Depends(get_db)):
    """
    Get all unique facility types.
    """
    types = db.query(HealthFacility.facility_type).distinct().all()
    return [type[0] for type in types]

@router.get("/districts", response_model=List[str])
async def get_districts(db: Session = Depends(get_db)):
    """
    Get all unique districts with health facilities.
    """
    districts = db.query(HealthFacility.district).distinct().all()
    return [district[0] for district in districts]

@router.get("/symptoms", response_model=List[str])
async def get_available_symptoms(current_user = Depends(get_current_user)):
    """Get list of available symptoms for the symptom checker"""
    try:
        symptoms = symptom_checker.get_available_symptoms()
        return symptoms
    except Exception as e:
        logger.error(f"Error getting symptoms: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving symptoms")

@router.post("/symptom-check")
async def check_symptoms(symptoms: List[str], current_user = Depends(get_current_user)):
    """Analyze symptoms using AI model and provide recommendations"""
    try:
        # Get predictions from the AI model
        result = symptom_checker.predict_disease(symptoms)
        
        if 'error' in result:
            raise HTTPException(status_code=500, detail=result['error'])
        
        # Save the symptom check to database
        db = next(get_db())
        symptom_check = SymptomCheck(
            user_id=current_user.id,
            symptoms=','.join(symptoms),
            result=str(result['predictions']),
            recommendation=str([pred['precautions'] for pred in result['predictions']])
        )
        db.add(symptom_check)
        db.commit()
        
        return result
    except Exception as e:
        logger.error(f"Error checking symptoms: {str(e)}")
        raise HTTPException(status_code=500, detail="Error processing symptoms")
