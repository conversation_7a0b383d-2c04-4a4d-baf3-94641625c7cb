from fastapi import APIRouter, Depends, HTTPException, Body, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from database import get_db, JobListing, JobApplication, User
from utils.auth import get_current_user, get_current_admin_user
import logging
from datetime import datetime
from pydantic import BaseModel
import os
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/jobs",
    tags=["jobs"],
    responses={404: {"description": "Not found"}},
)

# Pydantic models
class JobBase(BaseModel):
    title: str
    company: str
    location: str
    job_type: str
    description: str
    requirements: str
    salary_range: Optional[str] = None
    contact_email: str
    application_url: Optional[str] = None

class JobCreate(JobBase):
    pass

class JobUpdate(BaseModel):
    title: Optional[str] = None
    company: Optional[str] = None
    location: Optional[str] = None
    job_type: Optional[str] = None
    description: Optional[str] = None
    requirements: Optional[str] = None
    salary_range: Optional[str] = None
    contact_email: Optional[str] = None
    application_url: Optional[str] = None
    is_active: Optional[bool] = None

class JobResponse(JobBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class ApplicationBase(BaseModel):
    job_id: int
    cover_letter: Optional[str] = None

class ApplicationResponse(BaseModel):
    id: int
    job_id: int
    user_id: int
    resume_url: str
    cover_letter: Optional[str] = None
    status: str
    created_at: datetime
    updated_at: datetime
    
    # Include job details
    job: JobResponse

    class Config:
        orm_mode = True

# Helper function to save resume file
async def save_resume(file: UploadFile, user_id: int) -> str:
    """
    Save resume file to disk and return the file path.
    """
    # Create uploads directory if it doesn't exist
    upload_dir = os.path.join("uploads", "resumes")
    os.makedirs(upload_dir, exist_ok=True)
    
    # Generate unique filename
    file_extension = os.path.splitext(file.filename)[1]
    unique_filename = f"{user_id}_{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(upload_dir, unique_filename)
    
    # Save file
    with open(file_path, "wb") as buffer:
        buffer.write(await file.read())
    
    return file_path

# Endpoints
@router.get("/listings", response_model=List[JobResponse])
async def get_job_listings(
    location: Optional[str] = None,
    job_type: Optional[str] = None,
    search: Optional[str] = None,
    active_only: bool = True,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all job listings with optional filtering.
    """
    query = db.query(JobListing)
    
    if active_only:
        query = query.filter(JobListing.is_active == True)
    
    if location:
        query = query.filter(JobListing.location == location)
    
    if job_type:
        query = query.filter(JobListing.job_type == job_type)
    
    if search:
        query = query.filter(
            JobListing.title.ilike(f"%{search}%") | 
            JobListing.company.ilike(f"%{search}%") |
            JobListing.description.ilike(f"%{search}%")
        )
    
    jobs = query.order_by(JobListing.created_at.desc()).offset(skip).limit(limit).all()
    return jobs

@router.get("/listings/{job_id}", response_model=JobResponse)
async def get_job_listing(job_id: int, db: Session = Depends(get_db)):
    """
    Get a specific job listing by ID.
    """
    job = db.query(JobListing).filter(JobListing.id == job_id).first()
    if not job:
        raise HTTPException(status_code=404, detail="Job listing not found")
    return job

@router.post("/listings", response_model=JobResponse)
async def create_job_listing(
    job: JobCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Create a new job listing (admin only).
    """
    db_job = JobListing(**job.dict())
    db.add(db_job)
    db.commit()
    db.refresh(db_job)
    return db_job

@router.put("/listings/{job_id}", response_model=JobResponse)
async def update_job_listing(
    job_id: int,
    job: JobUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Update a job listing (admin only).
    """
    db_job = db.query(JobListing).filter(JobListing.id == job_id).first()
    if not db_job:
        raise HTTPException(status_code=404, detail="Job listing not found")
    
    # Update only provided fields
    for key, value in job.dict(exclude_unset=True).items():
        setattr(db_job, key, value)
    
    db_job.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_job)
    return db_job

@router.delete("/listings/{job_id}")
async def delete_job_listing(
    job_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Delete a job listing (admin only).
    """
    db_job = db.query(JobListing).filter(JobListing.id == job_id).first()
    if not db_job:
        raise HTTPException(status_code=404, detail="Job listing not found")
    
    db.delete(db_job)
    db.commit()
    return {"message": "Job listing deleted successfully"}

@router.post("/apply")
async def apply_for_job(
    job_id: int = Form(...),
    cover_letter: Optional[str] = Form(None),
    resume: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Apply for a job by uploading resume and optional cover letter.
    """
    # Check if job exists and is active
    job = db.query(JobListing).filter(
        JobListing.id == job_id,
        JobListing.is_active == True
    ).first()
    
    if not job:
        raise HTTPException(status_code=404, detail="Job listing not found or inactive")
    
    # Check if user has already applied for this job
    existing_application = db.query(JobApplication).filter(
        JobApplication.job_id == job_id,
        JobApplication.user_id == current_user["id"]
    ).first()
    
    if existing_application:
        raise HTTPException(status_code=400, detail="You have already applied for this job")
    
    # Save resume file
    resume_path = await save_resume(resume, current_user["id"])
    
    # Create job application
    application = JobApplication(
        job_id=job_id,
        user_id=current_user["id"],
        resume_url=resume_path,
        cover_letter=cover_letter,
        status="applied"
    )
    
    db.add(application)
    db.commit()
    db.refresh(application)
    
    return {
        "message": "Application submitted successfully",
        "application_id": application.id,
        "status": application.status
    }

@router.get("/applications", response_model=List[ApplicationResponse])
async def get_my_applications(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all job applications for the current user.
    """
    applications = db.query(JobApplication).filter(
        JobApplication.user_id == current_user["id"]
    ).order_by(JobApplication.created_at.desc()).all()
    
    return applications

@router.get("/admin/applications", response_model=List[dict])
async def get_all_applications(
    job_id: Optional[int] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Get all job applications (admin only).
    """
    query = db.query(JobApplication)
    
    if job_id:
        query = query.filter(JobApplication.job_id == job_id)
    
    if status:
        query = query.filter(JobApplication.status == status)
    
    applications = query.order_by(JobApplication.created_at.desc()).all()
    
    # Include user and job details
    result = []
    for app in applications:
        user = db.query(User).filter(User.id == app.user_id).first()
        job = db.query(JobListing).filter(JobListing.id == app.job_id).first()
        
        result.append({
            "id": app.id,
            "job_id": app.job_id,
            "user_id": app.user_id,
            "resume_url": app.resume_url,
            "cover_letter": app.cover_letter,
            "status": app.status,
            "created_at": app.created_at,
            "updated_at": app.updated_at,
            "user": {
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "district": user.district
            },
            "job": {
                "title": job.title,
                "company": job.company,
                "location": job.location
            }
        })
    
    return result

@router.put("/admin/applications/{application_id}")
async def update_application_status(
    application_id: int,
    status: str = Body(..., embed=True),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_admin_user)
):
    """
    Update the status of a job application (admin only).
    """
    valid_statuses = ["applied", "reviewed", "interviewed", "offered", "rejected"]
    if status not in valid_statuses:
        raise HTTPException(status_code=400, detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
    
    application = db.query(JobApplication).filter(JobApplication.id == application_id).first()
    if not application:
        raise HTTPException(status_code=404, detail="Application not found")
    
    application.status = status
    application.updated_at = datetime.utcnow()
    db.commit()
    
    return {"message": f"Application status updated to {status}"}

@router.get("/locations", response_model=List[str])
async def get_job_locations(db: Session = Depends(get_db)):
    """
    Get all unique job locations.
    """
    locations = db.query(JobListing.location).distinct().all()
    return [loc[0] for loc in locations]

@router.get("/types", response_model=List[str])
async def get_job_types(db: Session = Depends(get_db)):
    """
    Get all unique job types.
    """
    types = db.query(JobListing.job_type).distinct().all()
    return [type[0] for type in types]
