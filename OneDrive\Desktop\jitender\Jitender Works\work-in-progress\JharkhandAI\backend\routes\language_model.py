from fastapi import APIRouter, File, UploadFile, Form, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Dict, Optional
import os
import tempfile
import shutil
import logging
from models.language_model import language_model

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/language",
    tags=["language"],
    responses={404: {"description": "Not found"}},
)

class TranslationRequest(BaseModel):
    text: str
    source_lang: str
    target_lang: str

class TranslationResponse(BaseModel):
    translated_text: str
    source_lang: str
    target_lang: str

class TextGenerationRequest(BaseModel):
    prompt: str
    max_length: Optional[int] = 100

class TextGenerationResponse(BaseModel):
    generated_text: str

class FineTuneRequest(BaseModel):
    source_lang: str
    target_lang: str
    dataset_path: str
    epochs: Optional[int] = 3
    batch_size: Optional[int] = 8

class FineTuneResponse(BaseModel):
    model_dir: str
    metrics: Dict
    language_pair: str

@router.post("/translate", response_model=TranslationResponse)
async def translate_text(request: TranslationRequest):
    """
    Translate text from source language to target language.
    """
    try:
        translated_text = language_model.translate(
            text=request.text,
            source_lang=request.source_lang,
            target_lang=request.target_lang
        )
        
        return {
            "translated_text": translated_text,
            "source_lang": request.source_lang,
            "target_lang": request.target_lang
        }
    except Exception as e:
        logger.error(f"Translation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Translation error: {str(e)}")

@router.post("/generate", response_model=TextGenerationResponse)
async def generate_text(request: TextGenerationRequest):
    """
    Generate text based on a prompt.
    """
    try:
        generated_text = language_model.generate_text(
            prompt=request.prompt,
            max_length=request.max_length
        )
        
        return {
            "generated_text": generated_text
        }
    except Exception as e:
        logger.error(f"Text generation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Text generation error: {str(e)}")

@router.post("/fine-tune", response_model=FineTuneResponse)
async def fine_tune_model(request: FineTuneRequest, background_tasks: BackgroundTasks):
    """
    Fine-tune the language model for a specific language pair.
    This is a long-running task, so it runs in the background.
    """
    try:
        # Check if dataset exists
        if not os.path.exists(request.dataset_path):
            raise HTTPException(status_code=404, detail=f"Dataset not found at {request.dataset_path}")
        
        # Start fine-tuning in the background
        def fine_tune_task():
            try:
                result = language_model.fine_tune(
                    source_lang=request.source_lang,
                    target_lang=request.target_lang,
                    dataset_path=request.dataset_path,
                    epochs=request.epochs,
                    batch_size=request.batch_size
                )
                logger.info(f"Fine-tuning completed: {result}")
            except Exception as e:
                logger.error(f"Fine-tuning error: {str(e)}")
        
        background_tasks.add_task(fine_tune_task)
        
        return {
            "model_dir": f"./models/fine_tuned/{request.source_lang}-{request.target_lang}",
            "metrics": {"status": "Fine-tuning started in the background"},
            "language_pair": f"{request.source_lang}-{request.target_lang}"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Fine-tuning setup error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Fine-tuning setup error: {str(e)}")

@router.post("/upload-training-data")
async def upload_training_data(
    source_lang: str = Form(...),
    target_lang: str = Form(...),
    file: UploadFile = File(...)
):
    """
    Upload a CSV file with training data for fine-tuning.
    The CSV should have 'source' and 'target' columns.
    """
    try:
        # Create temporary file
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, file.filename)
        
        # Save uploaded file
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Create dataset directory if it doesn't exist
        dataset_dir = "./data/language_datasets"
        os.makedirs(dataset_dir, exist_ok=True)
        
        # Move file to dataset directory
        dataset_path = os.path.join(dataset_dir, f"{source_lang}-{target_lang}-{file.filename}")
        shutil.move(temp_file_path, dataset_path)
        
        # Clean up
        shutil.rmtree(temp_dir)
        
        return {
            "filename": file.filename,
            "dataset_path": dataset_path,
            "source_lang": source_lang,
            "target_lang": target_lang
        }
    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Upload error: {str(e)}")

@router.get("/languages")
async def get_languages():
    """
    Get a list of supported languages.
    """
    return {
        "languages": language_model.language_codes
    }

@router.get("/models")
async def get_models():
    """
    Get a list of available fine-tuned models.
    """
    return {
        "models": list(language_model.fine_tuned_models.keys())
    }
