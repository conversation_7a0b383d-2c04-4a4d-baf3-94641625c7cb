from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
import json
import os
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

from backend.database import get_db
from backend.models.law_security_model import (
    CrimeReport, CrimeReportUpdate, TrafficIncident, 
    LegalAssistance, LegalDocument, LegalCaseUpdate,
    CrimeHotspot, CrimeType, CaseStatus
)
from backend.schemas.law_security_schema import (
    CrimeReportCreate, CrimeReportResponse, CrimeReportUpdateCreate,
    TrafficIncidentCreate, TrafficIncidentResponse,
    LegalAssistanceCreate, LegalAssistanceResponse,
    LegalDocumentCreate, LegalCaseUpdateCreate,
    CrimeHotspotResponse, CrimeAnalyticsResponse
)
from backend.auth.auth_bearer import JWTBearer
from backend.auth.auth_handler import get_current_user_id

router = APIRouter(
    prefix="/api/law-security",
    tags=["Law & Security"]
)

# Crime Reports Endpoints
@router.post("/crime-reports", response_model=CrimeReportResponse)
async def create_crime_report(
    crime_report: CrimeReportCreate,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """Create a new crime report"""
    db_crime_report = CrimeReport(
        user_id=None if crime_report.is_anonymous else current_user_id,
        crime_type=crime_report.crime_type,
        description=crime_report.description,
        location=crime_report.location,
        district=crime_report.district,
        date_of_incident=crime_report.date_of_incident,
        is_anonymous=crime_report.is_anonymous,
        evidence_urls=crime_report.evidence_urls
    )
    db.add(db_crime_report)
    db.commit()
    db.refresh(db_crime_report)
    return db_crime_report

@router.get("/crime-reports", response_model=List[CrimeReportResponse])
async def get_crime_reports(
    skip: int = 0,
    limit: int = 100,
    district: Optional[str] = None,
    crime_type: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get all crime reports with optional filtering"""
    query = db.query(CrimeReport)
    
    if district:
        query = query.filter(CrimeReport.district == district)
    if crime_type:
        query = query.filter(CrimeReport.crime_type == crime_type)
    if status:
        query = query.filter(CrimeReport.status == status)
        
    return query.offset(skip).limit(limit).all()

@router.get("/crime-reports/{report_id}", response_model=CrimeReportResponse)
async def get_crime_report(
    report_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific crime report by ID"""
    db_report = db.query(CrimeReport).filter(CrimeReport.id == report_id).first()
    if not db_report:
        raise HTTPException(status_code=404, detail="Crime report not found")
    return db_report

@router.post("/crime-reports/{report_id}/updates")
async def add_crime_report_update(
    report_id: int,
    update: CrimeReportUpdateCreate,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """Add an update to a crime report"""
    db_report = db.query(CrimeReport).filter(CrimeReport.id == report_id).first()
    if not db_report:
        raise HTTPException(status_code=404, detail="Crime report not found")
    
    db_update = CrimeReportUpdate(
        crime_report_id=report_id,
        update_text=update.update_text,
        updated_by=update.updated_by,
        status=update.status
    )
    
    # Update the status of the crime report
    if update.status:
        db_report.status = update.status
    
    db.add(db_update)
    db.commit()
    db.refresh(db_update)
    return db_update

# Traffic Management Endpoints
@router.post("/traffic-incidents", response_model=TrafficIncidentResponse)
async def create_traffic_incident(
    incident: TrafficIncidentCreate,
    db: Session = Depends(get_db),
    current_user_id: Optional[int] = Depends(get_current_user_id)
):
    """Create a new traffic incident report"""
    db_incident = TrafficIncident(
        incident_type=incident.incident_type,
        description=incident.description,
        location=incident.location,
        district=incident.district,
        severity=incident.severity,
        reported_by=current_user_id
    )
    db.add(db_incident)
    db.commit()
    db.refresh(db_incident)
    return db_incident

@router.get("/traffic-incidents", response_model=List[TrafficIncidentResponse])
async def get_traffic_incidents(
    skip: int = 0,
    limit: int = 100,
    district: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get all traffic incidents with optional filtering"""
    query = db.query(TrafficIncident)
    
    if district:
        query = query.filter(TrafficIncident.district == district)
    if status:
        query = query.filter(TrafficIncident.status == status)
        
    return query.offset(skip).limit(limit).all()

@router.put("/traffic-incidents/{incident_id}")
async def update_traffic_incident(
    incident_id: int,
    incident_update: dict,
    db: Session = Depends(get_db)
):
    """Update a traffic incident status"""
    db_incident = db.query(TrafficIncident).filter(TrafficIncident.id == incident_id).first()
    if not db_incident:
        raise HTTPException(status_code=404, detail="Traffic incident not found")
    
    for key, value in incident_update.items():
        setattr(db_incident, key, value)
    
    db.commit()
    db.refresh(db_incident)
    return db_incident

# Legal Assistance Endpoints
@router.post("/legal-assistance", response_model=LegalAssistanceResponse)
async def create_legal_assistance_request(
    request: LegalAssistanceCreate,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """Create a new legal assistance request"""
    db_request = LegalAssistance(
        user_id=current_user_id,
        case_type=request.case_type,
        description=request.description,
        district=request.district
    )
    db.add(db_request)
    db.commit()
    db.refresh(db_request)
    return db_request

@router.get("/legal-assistance", response_model=List[LegalAssistanceResponse])
async def get_legal_assistance_requests(
    skip: int = 0,
    limit: int = 100,
    district: Optional[str] = None,
    case_type: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get all legal assistance requests with optional filtering"""
    query = db.query(LegalAssistance)
    
    if district:
        query = query.filter(LegalAssistance.district == district)
    if case_type:
        query = query.filter(LegalAssistance.case_type == case_type)
    if status:
        query = query.filter(LegalAssistance.status == status)
        
    return query.offset(skip).limit(limit).all()

@router.post("/legal-assistance/{case_id}/documents")
async def add_legal_document(
    case_id: int,
    document: LegalDocumentCreate,
    db: Session = Depends(get_db)
):
    """Add a document to a legal assistance case"""
    db_case = db.query(LegalAssistance).filter(LegalAssistance.id == case_id).first()
    if not db_case:
        raise HTTPException(status_code=404, detail="Legal assistance case not found")
    
    db_document = LegalDocument(
        legal_case_id=case_id,
        document_type=document.document_type,
        document_url=document.document_url,
        description=document.description
    )
    
    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    return db_document

@router.post("/legal-assistance/{case_id}/updates")
async def add_legal_case_update(
    case_id: int,
    update: LegalCaseUpdateCreate,
    db: Session = Depends(get_db)
):
    """Add an update to a legal assistance case"""
    db_case = db.query(LegalAssistance).filter(LegalAssistance.id == case_id).first()
    if not db_case:
        raise HTTPException(status_code=404, detail="Legal assistance case not found")
    
    db_update = LegalCaseUpdate(
        legal_case_id=case_id,
        update_text=update.update_text,
        updated_by=update.updated_by
    )
    
    # Update the status of the legal case if provided
    if update.status:
        db_case.status = update.status
    
    db.add(db_update)
    db.commit()
    db.refresh(db_update)
    return db_update

# AI Crime Analysis Endpoints
@router.get("/crime-analytics", response_model=CrimeAnalyticsResponse)
async def get_crime_analytics(
    district: Optional[str] = None,
    time_period: str = "month",  # day, week, month, year
    db: Session = Depends(get_db)
):
    """Get AI-driven crime analytics and predictions"""
    # Define time period for analysis
    now = datetime.now()
    if time_period == "day":
        start_date = now - timedelta(days=1)
    elif time_period == "week":
        start_date = now - timedelta(weeks=1)
    elif time_period == "year":
        start_date = now - timedelta(days=365)
    else:  # default to month
        start_date = now - timedelta(days=30)
    
    # Query crime reports
    query = db.query(CrimeReport).filter(CrimeReport.date_of_incident >= start_date)
    if district:
        query = query.filter(CrimeReport.district == district)
    
    crime_reports = query.all()
    
    # Perform analytics
    crime_types = {}
    locations = {}
    
    for report in crime_reports:
        # Count crime types
        if report.crime_type in crime_types:
            crime_types[report.crime_type] += 1
        else:
            crime_types[report.crime_type] = 1
            
        # Count locations
        if report.location in locations:
            locations[report.location] += 1
        else:
            locations[report.location] = 1
    
    # Sort by frequency
    sorted_crime_types = dict(sorted(crime_types.items(), key=lambda item: item[1], reverse=True))
    sorted_locations = dict(sorted(locations.items(), key=lambda item: item[1], reverse=True))
    
    # Get hotspots
    hotspots = db.query(CrimeHotspot)
    if district:
        hotspots = hotspots.filter(CrimeHotspot.district == district)
    hotspots = hotspots.all()
    
    # Prepare response
    response = {
        "total_crimes": len(crime_reports),
        "crime_types_distribution": sorted_crime_types,
        "location_distribution": sorted_locations,
        "hotspots": hotspots,
        "time_period": time_period,
        "district": district
    }
    
    return response

@router.get("/crime-hotspots", response_model=List[CrimeHotspotResponse])
async def get_crime_hotspots(
    district: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get AI-identified crime hotspots"""
    query = db.query(CrimeHotspot)
    if district:
        query = query.filter(CrimeHotspot.district == district)
    
    return query.all()

# AI Crime Prediction (mock implementation)
@router.post("/predict-crime-risk")
async def predict_crime_risk(
    location: str = Form(...),
    district: str = Form(...),
    time_of_day: str = Form(...),  # morning, afternoon, evening, night
    day_of_week: str = Form(...),  # monday, tuesday, etc.
    db: Session = Depends(get_db)
):
    """Predict crime risk for a given location and time using AI"""
    # This is a simplified mock implementation
    # In a real system, this would use a trained ML model
    
    # Get historical data for the location
    crime_reports = db.query(CrimeReport).filter(
        CrimeReport.district == district
    ).all()
    
    # Mock risk calculation
    # In reality, this would use features from the request and historical data
    # to make a prediction using a trained model
    
    # For demonstration, we'll return a random risk score
    import random
    risk_score = random.uniform(0, 1)
    risk_level = "Low" if risk_score < 0.3 else "Medium" if risk_score < 0.7 else "High"
    
    common_crimes = ["theft", "assault", "fraud"]
    if risk_score > 0.7:
        common_crimes.append("robbery")
    
    response = {
        "location": location,
        "district": district,
        "time_of_day": time_of_day,
        "day_of_week": day_of_week,
        "risk_score": risk_score,
        "risk_level": risk_level,
        "common_crimes": common_crimes,
        "recommendations": [
            "Avoid carrying valuables in plain sight",
            "Stay in well-lit areas",
            "Travel in groups when possible"
        ]
    }
    
    return response
