"""
Model Management API Routes for JharkhandAI

This module provides API endpoints for managing AI models, including:
- Model training pipeline
- Offline model support
- Model versioning
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query, Path, Body
from typing import Dict, List, Any, Optional
import logging
from pydantic import BaseModel, Field
import datetime

# Import AI modules
from backend.ai.model_training import ModelTrainingPipeline, schedule_training_job
from backend.ai.offline_model import OfflineModelManager
from backend.ai.model_versioning import ModelRegistry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/models",
    tags=["model-management"],
    responses={404: {"description": "Not found"}},
)

# Initialize components
model_registry = ModelRegistry()
offline_manager = OfflineModelManager()

# Pydantic models for request/response validation
class TrainingRequest(BaseModel):
    model_type: str = Field(..., description="Type of model (language, speech, vision)")
    model_name: str = Field(..., description="Name of the model")
    data_source: str = Field(..., description="Path or URL to the data source")
    data_format: str = Field(..., description="Format of the data (csv, json, etc.)")
    model_params: Dict[str, Any] = Field(default={}, description="Dictionary of model parameters")

class VersionTransitionRequest(BaseModel):
    model_type: str = Field(..., description="Type of model (language, speech, vision)")
    model_name: str = Field(..., description="Name of the model")
    version: int = Field(..., description="Model version")
    stage: str = Field(..., description="Target stage (production, staging, archived)")

class SyncRequest(BaseModel):
    force_sync: bool = Field(default=False, description="Whether to force synchronization")

# API endpoints for model training pipeline
@router.post("/train", response_model=Dict[str, Any])
async def train_model(
    request: TrainingRequest,
    background_tasks: BackgroundTasks
):
    """
    Schedule a model training job.
    """
    try:
        # Schedule training job as a background task
        job_info = {
            "model_type": request.model_type,
            "model_name": request.model_name,
            "data_source": request.data_source,
            "data_format": request.data_format,
            "model_params": request.model_params,
            "status": "scheduled",
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        # Add task to background tasks
        background_tasks.add_task(
            schedule_training_job,
            request.model_type,
            request.model_name,
            request.data_source,
            request.data_format,
            request.model_params
        )
        
        return job_info
    except Exception as e:
        logger.error(f"Error scheduling training job: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/training/history/{model_type}/{model_name}", response_model=Dict[str, Any])
async def get_training_history(
    model_type: str = Path(..., description="Type of model (language, speech, vision)"),
    model_name: str = Path(..., description="Name of the model")
):
    """
    Get training history for a model.
    """
    try:
        pipeline = ModelTrainingPipeline(model_type, model_name)
        return {
            "model_type": model_type,
            "model_name": model_name,
            "versions": pipeline.get_model_versions(),
            "current_version": pipeline.get_current_version()
        }
    except Exception as e:
        logger.error(f"Error getting training history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# API endpoints for model versioning
@router.get("/registry", response_model=List[Dict[str, Any]])
async def list_models():
    """
    List all models in the registry.
    """
    try:
        return model_registry.list_models()
    except Exception as e:
        logger.error(f"Error listing models: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/registry/{model_type}/{model_name}", response_model=List[Dict[str, Any]])
async def list_model_versions(
    model_type: str = Path(..., description="Type of model (language, speech, vision)"),
    model_name: str = Path(..., description="Name of the model")
):
    """
    List all versions of a model.
    """
    try:
        return model_registry.list_model_versions(model_type, model_name)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error listing model versions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/registry/{model_type}/{model_name}/{version}", response_model=Dict[str, Any])
async def get_model_version(
    model_type: str = Path(..., description="Type of model (language, speech, vision)"),
    model_name: str = Path(..., description="Name of the model"),
    version: int = Path(..., description="Model version")
):
    """
    Get information about a model version.
    """
    try:
        return model_registry.get_model_version(model_type, model_name, version)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting model version: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/registry/transition", response_model=Dict[str, Any])
async def transition_model_version(request: VersionTransitionRequest):
    """
    Transition a model version to a different stage.
    """
    try:
        return model_registry.transition_model_version(
            request.model_type,
            request.model_name,
            request.version,
            request.stage
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error transitioning model version: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/registry/{model_type}/{model_name}/{version}", response_model=Dict[str, Any])
async def delete_model_version(
    model_type: str = Path(..., description="Type of model (language, speech, vision)"),
    model_name: str = Path(..., description="Name of the model"),
    version: int = Path(..., description="Model version")
):
    """
    Delete a model version from the registry.
    """
    try:
        return model_registry.delete_model_version(model_type, model_name, version)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error deleting model version: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# API endpoints for offline model support
@router.get("/offline/cached", response_model=Dict[str, Any])
async def get_cached_models():
    """
    Get information about cached models for offline use.
    """
    try:
        return offline_manager.get_cached_models()
    except Exception as e:
        logger.error(f"Error getting cached models: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/offline/sync", response_model=Dict[str, Any])
async def sync_offline_models(
    request: SyncRequest = Body(default=SyncRequest())
):
    """
    Synchronize offline models with the model registry.
    """
    try:
        # Get models from registry
        registry_models = {}
        for model in model_registry.list_models():
            model_key = f"{model['model_type']}_{model['model_name']}"
            
            # Get production version if available
            if model["production_version"] is not None:
                version = model["production_version"]
                version_info = model_registry.get_model_version(
                    model["model_type"], 
                    model["model_name"], 
                    version
                )
                registry_models[f"{model_key}_v{version}"] = version_info
        
        # Sync models
        return offline_manager.sync_models(registry_models, request.force_sync)
    except Exception as e:
        logger.error(f"Error syncing offline models: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
