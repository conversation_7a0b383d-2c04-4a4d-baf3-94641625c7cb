from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException
from typing import Dict, List
from sqlalchemy.orm import Session
from database import get_db
from models.enhanced_policy_analytics import EnhancedPolicyAnalytics

router = APIRouter()
policy_analytics = EnhancedPolicyAnalytics()

@router.post("/analyze")
async def analyze_policy(policy_data: Dict, db: Session = Depends(get_db)):
    """Analyze policy impact and generate insights"""
    try:
        analysis_result = await policy_analytics.analyze_policy_impact(policy_data)
        return analysis_result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/visualizations/{analysis_id}")
async def get_policy_visualizations(analysis_id: int, db: Session = Depends(get_db)):
    """Get visualization data for a specific policy analysis"""
    try:
        # Retrieve analysis from database
        analysis = db.query(PolicyAnalytics).filter(PolicyAnalytics.id == analysis_id).first()
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
            
        # Generate visualization data
        visualizations = {
            'time_series': analysis.metrics,
            'impact_heatmap': analysis.insights,
            'recommendations': analysis.recommendations
        }
        
        return visualizations
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/feedback/{analysis_id}")
async def get_policy_feedback(analysis_id: int, db: Session = Depends(get_db)):
    """Get feedback and effectiveness metrics for a policy"""
    try:
        analysis = db.query(PolicyAnalytics).filter(PolicyAnalytics.id == analysis_id).first()
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
            
        # Get feedback metrics
        feedback = {
            'effectiveness_score': analysis.metrics.get('effectiveness_score'),
            'implementation_progress': analysis.metrics.get('implementation_progress'),
            'public_response': analysis.insights.get('public_response'),
            'improvement_suggestions': analysis.recommendations
        }
        
        return feedback
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/feedback/{analysis_id}")
async def submit_policy_feedback(analysis_id: int, feedback_data: Dict, db: Session = Depends(get_db)):
    """Submit feedback for policy effectiveness"""
    try:
        analysis = db.query(PolicyAnalytics).filter(PolicyAnalytics.id == analysis_id).first()
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
            
        # Update metrics with feedback
        updated_metrics = analysis.metrics.copy()
        updated_metrics['feedback'] = feedback_data
        
        # Update analysis record
        analysis.metrics = updated_metrics
        db.commit()
        
        return {"status": "success", "message": "Feedback submitted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))