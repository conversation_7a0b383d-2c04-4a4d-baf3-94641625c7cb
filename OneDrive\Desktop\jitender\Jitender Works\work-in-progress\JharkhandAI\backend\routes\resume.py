from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from typing import Dict, List, Optional
from database import get_db
from models.job_model import JobModel
from pydantic import BaseModel
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/jobs",
    tags=["resume"],
    responses={404: {"description": "Not found"}},
)

# Initialize job model
job_model = JobModel()

class ResumeData(BaseModel):
    personalInfo: Dict
    education: List[Dict]
    experience: List[Dict]
    skills: List[str]
    certifications: List[Dict]

class TargetJob(BaseModel):
    id: int
    title: str
    required_skills: List[str]
    description: str

class ResumeGenerationRequest(BaseModel):
    resumeData: ResumeData
    targetJob: Optional[TargetJob] = None

@router.post("/analyze-resume")
async def analyze_resume(
    resume_data: ResumeData,
    db: Session = Depends(get_db)
):
    try:
        # Convert resume data to text format for analysis
        resume_text = format_resume_text(resume_data)
        
        # Analyze resume using job model
        analysis = job_model.analyze_resume(resume_text)
        
        return {
            "completenessScore": analysis["completeness_score"],
            "suggestions": format_suggestions(analysis["improvement_suggestions"]),
            "skills": analysis["skills_identified"],
            "experience": analysis["experience_summary"],
            "education": analysis["education_summary"]
        }
    except Exception as e:
        logger.error(f"Error analyzing resume: {str(e)}")
        raise HTTPException(status_code=500, detail="Error analyzing resume")

@router.post("/generate-resume")
async def generate_resume(
    request: ResumeGenerationRequest,
    db: Session = Depends(get_db)
):
    try:
        resume_text = format_resume_text(request.resumeData)
        
        # Get resume analysis
        analysis = job_model.analyze_resume(
            resume_text,
            target_job=request.targetJob.dict() if request.targetJob else None
        )
        
        # Generate optimized resume
        resume_pdf = generate_resume_pdf(
            request.resumeData,
            analysis,
            request.targetJob
        )
        
        return resume_pdf
    except Exception as e:
        logger.error(f"Error generating resume: {str(e)}")
        raise HTTPException(status_code=500, detail="Error generating resume")

def format_resume_text(resume_data: ResumeData) -> str:
    """Convert resume data to text format for analysis"""
    sections = []
    
    # Personal Information
    personal = resume_data.personalInfo
    sections.append(f"Name: {personal.get('name', '')}")
    sections.append(f"Email: {personal.get('email', '')}")
    sections.append(f"Phone: {personal.get('phone', '')}")
    sections.append(f"Address: {personal.get('address', '')}")
    sections.append(f"Languages: {', '.join(personal.get('languages', []))}")
    
    # Education
    sections.append("\nEducation:")
    for edu in resume_data.education:
        sections.append(
            f"\n{edu.get('degree', '')} in {edu.get('field', '')} "
            f"from {edu.get('institution', '')}"
            f"\n{edu.get('startDate', '')} - {edu.get('endDate', '')}"
            f"\nGrade: {edu.get('grade', '')}"
        )
    
    # Experience
    sections.append("\nExperience:")
    for exp in resume_data.experience:
        sections.append(
            f"\n{exp.get('position', '')} at {exp.get('company', '')}"
            f"\n{exp.get('startDate', '')} - {exp.get('endDate', '')}"
            f"\n{exp.get('description', '')}"
            f"\nSkills: {', '.join(exp.get('skills', []))}"
        )
    
    # Skills
    sections.append(f"\nSkills:\n{', '.join(resume_data.skills)}")
    
    # Certifications
    sections.append("\nCertifications:")
    for cert in resume_data.certifications:
        sections.append(
            f"\n{cert.get('name', '')} from {cert.get('issuer', '')}"
            f"\nIssued: {cert.get('issueDate', '')}"
        )
    
    return "\n".join(sections)

def format_suggestions(suggestions: List[Dict]) -> List[str]:
    """Format improvement suggestions into readable strings"""
    formatted = []
    
    for suggestion in suggestions:
        if suggestion["type"] == "missing_skills":
            skills = ", ".join(suggestion["details"])
            formatted.append(f"Consider adding these skills: {skills}")
        # Add more suggestion types as needed
    
    return formatted

def generate_resume_pdf(resume_data: ResumeData, analysis: Dict, target_job: Optional[TargetJob]) -> bytes:
    """Generate optimized PDF resume"""
    # TODO: Implement PDF generation using a library like ReportLab
    # This is a placeholder that should be implemented based on your PDF generation needs
    pass