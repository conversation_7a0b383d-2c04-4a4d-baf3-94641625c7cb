from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from pydantic import BaseModel
from ..models.enhanced_sentiment_model import JharkhandSentimentModel

router = APIRouter()
sentiment_model = JharkhandSentimentModel()

class SentimentRequest(BaseModel):
    text: str
    language: str = 'en'

@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_sentiment(request: SentimentRequest):
    """Analyze sentiment of the provided text.
    
    Args:
        request: SentimentRequest object containing text and language
        
    Returns:
        Dictionary containing sentiment analysis results
    """
    try:
        result = sentiment_model.analyze_sentiment(
            text=request.text,
            language=request.language
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to analyze sentiment")

@router.post("/analyze-batch", response_model=Dict[str, Any])
async def analyze_sentiment_batch(texts: list[str], language: str = 'en'):
    """Analyze sentiment for a batch of texts.
    
    Args:
        texts: List of texts to analyze
        language: Language code for the input texts
        
    Returns:
        Dictionary containing batch sentiment analysis results
    """
    try:
        results = sentiment_model.analyze_batch(texts=texts, language=language)
        return {"results": results}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to analyze sentiment batch")