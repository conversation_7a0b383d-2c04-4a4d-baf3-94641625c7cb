from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Dict, List, Optional, Union
import io
import tempfile
import os

from ..models.speech_model import SpeechModel
from ..utils.language_utils import get_language_code, is_tribal_language

router = APIRouter(prefix="/api/speech", tags=["speech"])

# Initialize speech model
speech_model = SpeechModel()

class TextToSpeechRequest(BaseModel):
    text: str
    language: str
    voice_id: Optional[str] = None
    speed: Optional[float] = 1.0

@router.post("/transcribe")
async def transcribe_audio(
    audio: UploadFile = File(...),
    language: Optional[str] = Form(None)
):
    """
    Convert speech to text (Speech-to-Text)
    Supports tribal languages of Jharkhand
    """
    try:
        # Save uploaded audio to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_audio:
            temp_audio.write(await audio.read())
            temp_audio_path = temp_audio.name
        
        try:
            # Process language code
            language_code = get_language_code(language) if language else None
            
            # Transcribe audio
            result = speech_model.transcribe(
                audio_path=temp_audio_path,
                language=language_code
            )
            
            return {
                "text": result["text"],
                "language": result["language"],
                "confidence": result["confidence"]
            }
        
        finally:
            # Clean up temporary file
            if os.path.exists(temp_audio_path):
                os.remove(temp_audio_path)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Transcription error: {str(e)}")

@router.post("/synthesize")
async def synthesize_speech(request: TextToSpeechRequest):
    """
    Convert text to speech (Text-to-Speech)
    Supports tribal languages of Jharkhand
    """
    try:
        # Process language code
        language_code = get_language_code(request.language)
        
        # Synthesize speech
        audio_data = speech_model.synthesize(
            text=request.text,
            language=language_code,
            voice_id=request.voice_id,
            speed=request.speed
        )
        
        # Return audio as streaming response
        return StreamingResponse(
            io.BytesIO(audio_data),
            media_type="audio/mpeg",
            headers={"Content-Disposition": "attachment; filename=speech.mp3"}
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Speech synthesis error: {str(e)}")

@router.get("/voices")
async def get_available_voices(language: Optional[str] = None):
    """
    Get available voices for text-to-speech
    Optionally filter by language
    """
    try:
        language_code = get_language_code(language) if language else None
        voices = speech_model.get_available_voices(language_code)
        return voices
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving voices: {str(e)}")

@router.get("/supported-languages")
async def get_supported_languages():
    """
    Get list of supported languages for speech services
    """
    return speech_model.get_supported_languages()

@router.get("/tribal-languages")
async def get_tribal_languages():
    """
    Get list of supported tribal languages for speech services
    """
    all_languages = speech_model.get_supported_languages()
    tribal_languages = {code: name for code, name in all_languages.items() if is_tribal_language(code)}
    return tribal_languages
