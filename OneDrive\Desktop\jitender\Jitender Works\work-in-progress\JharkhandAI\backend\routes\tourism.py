from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from database import get_db, TourismDestination, TribalHeritageSite, TourismEvent, TourismReview, User
from utils.auth import get_current_user
import logging
from datetime import datetime
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/tourism",
    tags=["tourism"],
    responses={404: {"description": "Not found"}},
)

# Pydantic models
class DestinationBase(BaseModel):
    name: str
    description: str
    location: str
    district: str
    category: str
    image_url: Optional[str] = None
    highlights: Optional[str] = None
    best_time_to_visit: Optional[str] = None
    how_to_reach: Optional[str] = None

class DestinationCreate(DestinationBase):
    pass

class DestinationResponse(DestinationBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class ReviewBase(BaseModel):
    destination_id: int
    rating: int
    comment: Optional[str] = None

class ReviewCreate(ReviewBase):
    pass

class ReviewResponse(ReviewBase):
    id: int
    user_id: int
    created_at: datetime

    class Config:
        orm_mode = True

# Endpoints
@router.get("/destinations", response_model=List[DestinationResponse])
async def get_destinations(
    district: Optional[str] = None,
    category: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all tourism destinations with optional filtering.
    """
    query = db.query(TourismDestination)
    
    if district:
        query = query.filter(TourismDestination.district == district)
    
    if category:
        query = query.filter(TourismDestination.category == category)
    
    destinations = query.offset(skip).limit(limit).all()
    return destinations

@router.get("/destinations/{destination_id}", response_model=DestinationResponse)
async def get_destination(destination_id: int, db: Session = Depends(get_db)):
    """
    Get a specific tourism destination by ID.
    """
    destination = db.query(TourismDestination).filter(TourismDestination.id == destination_id).first()
    if not destination:
        raise HTTPException(status_code=404, detail="Destination not found")
    return destination

@router.post("/destinations", response_model=DestinationResponse)
async def create_destination(
    destination: DestinationCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new tourism destination (admin only).
    """
    if not current_user.get("is_admin", False):
        raise HTTPException(status_code=403, detail="Not authorized to create destinations")
    
    db_destination = TourismDestination(**destination.dict())
    db.add(db_destination)
    db.commit()
    db.refresh(db_destination)
    return db_destination

@router.put("/destinations/{destination_id}", response_model=DestinationResponse)
async def update_destination(
    destination_id: int,
    destination: DestinationCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Update a tourism destination (admin only).
    """
    if not current_user.get("is_admin", False):
        raise HTTPException(status_code=403, detail="Not authorized to update destinations")
    
    db_destination = db.query(TourismDestination).filter(TourismDestination.id == destination_id).first()
    if not db_destination:
        raise HTTPException(status_code=404, detail="Destination not found")
    
    for key, value in destination.dict().items():
        setattr(db_destination, key, value)
    
    db_destination.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_destination)
    return db_destination

@router.delete("/destinations/{destination_id}")
async def delete_destination(
    destination_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a tourism destination (admin only).
    """
    if not current_user.get("is_admin", False):
        raise HTTPException(status_code=403, detail="Not authorized to delete destinations")
    
    db_destination = db.query(TourismDestination).filter(TourismDestination.id == destination_id).first()
    if not db_destination:
        raise HTTPException(status_code=404, detail="Destination not found")
    
    db.delete(db_destination)
    db.commit()
    return {"message": "Destination deleted successfully"}

@router.post("/reviews", response_model=ReviewResponse)
async def create_review(
    review: ReviewCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new review for a tourism destination.
    """
    # Check if destination exists
    destination = db.query(TourismDestination).filter(TourismDestination.id == review.destination_id).first()
    if not destination:
        raise HTTPException(status_code=404, detail="Destination not found")
    
    # Check if user has already reviewed this destination
    existing_review = db.query(TourismReview).filter(
        TourismReview.destination_id == review.destination_id,
        TourismReview.user_id == current_user["id"]
    ).first()
    
    if existing_review:
        raise HTTPException(status_code=400, detail="You have already reviewed this destination")
    
    # Validate rating
    if review.rating < 1 or review.rating > 5:
        raise HTTPException(status_code=400, detail="Rating must be between 1 and 5")
    
    # Create review
    db_review = TourismReview(
        **review.dict(),
        user_id=current_user["id"]
    )
    
    db.add(db_review)
    db.commit()
    db.refresh(db_review)
    return db_review

@router.get("/reviews/{destination_id}", response_model=List[ReviewResponse])
async def get_destination_reviews(
    destination_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all reviews for a specific tourism destination.
    """
    # Check if destination exists
    destination = db.query(TourismDestination).filter(TourismDestination.id == destination_id).first()
    if not destination:
        raise HTTPException(status_code=404, detail="Destination not found")
    
    reviews = db.query(TourismReview).filter(
        TourismReview.destination_id == destination_id
    ).offset(skip).limit(limit).all()
    
    return reviews

@router.get("/events", response_model=List[dict])
async def get_tourism_events(
    district: Optional[str] = None,
    category: Optional[str] = None,
    from_date: Optional[datetime] = None,
    to_date: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all tourism events with optional filtering.
    """
    query = db.query(TourismEvent)
    
    if district:
        query = query.filter(TourismEvent.district == district)
    
    if category:
        query = query.filter(TourismEvent.category == category)
    
    if from_date:
        query = query.filter(TourismEvent.start_date >= from_date)
    
    if to_date:
        query = query.filter(TourismEvent.end_date <= to_date)
    
    events = query.offset(skip).limit(limit).all()
    
    # Convert to dict for response
    result = []
    for event in events:
        result.append({
            "id": event.id,
            "name": event.name,
            "description": event.description,
            "location": event.location,
            "district": event.district,
            "start_date": event.start_date,
            "end_date": event.end_date,
            "category": event.category,
            "image_url": event.image_url,
            "created_at": event.created_at
        })
    
    return result

@router.get("/tribal-heritage", response_model=List[dict])
async def get_tribal_heritage_sites(
    district: Optional[str] = None,
    tribe: Optional[str] = None,
    ar_tour_available: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all tribal heritage sites with optional filtering.
    """
    query = db.query(TribalHeritageSite)
    
    if district:
        query = query.filter(TribalHeritageSite.district == district)
    
    if tribe:
        query = query.filter(TribalHeritageSite.tribe == tribe)
    
    if ar_tour_available is not None:
        query = query.filter(TribalHeritageSite.ar_tour_available == ar_tour_available)
    
    sites = query.offset(skip).limit(limit).all()
    
    # Convert to dict for response
    result = []
    for site in sites:
        result.append({
            "id": site.id,
            "name": site.name,
            "description": site.description,
            "location": site.location,
            "district": site.district,
            "tribe": site.tribe,
            "cultural_significance": site.cultural_significance,
            "image_url": site.image_url,
            "ar_tour_available": site.ar_tour_available,
            "created_at": site.created_at
        })
    
    return result

@router.get("/recommendations", response_model=List[DestinationResponse])
async def get_personalized_recommendations(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get personalized tourism recommendations based on user preferences and history.
    """
    # This is a placeholder for a more sophisticated recommendation algorithm
    # In a real implementation, you would use user preferences, past reviews, etc.
    
    # For now, just return some destinations from the user's district if available
    if current_user.get("district"):
        destinations = db.query(TourismDestination).filter(
            TourismDestination.district == current_user["district"]
        ).limit(5).all()
        
        if destinations:
            return destinations
    
    # If no district or no destinations in district, return top-rated destinations
    return db.query(TourismDestination).limit(5).all()
