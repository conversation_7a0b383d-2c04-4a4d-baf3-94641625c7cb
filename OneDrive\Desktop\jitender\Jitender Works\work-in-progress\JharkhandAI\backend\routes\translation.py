from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Optional, Union

from models.translation_model import TranslationModel
from utils.language_utils import get_language_code, get_language_name, is_tribal_language

router = APIRouter(prefix="/api/translation", tags=["translation"])

# Initialize translation model
translation_model = TranslationModel()

class TranslationRequest(BaseModel):
    text: str
    source_language: str
    target_language: str

class TranslationResponse(BaseModel):
    translated_text: str
    source_language: str
    target_language: str
    confidence: float

class FeedbackRequest(BaseModel):
    original_text: str
    translated_text: str
    corrected_text: str
    source_language: str
    target_language: str
    feedback_notes: Optional[str] = None

@router.post("/translate", response_model=TranslationResponse)
async def translate_text(request: TranslationRequest):
    """
    Translate text between languages
    Supports tribal languages of Jharkhand
    """
    try:
        source_code = get_language_code(request.source_language)
        target_code = get_language_code(request.target_language)
        
        # Translate text
        result = translation_model.translate(
            text=request.text,
            source_language=source_code,
            target_language=target_code
        )
        
        return {
            "translated_text": result["translated_text"],
            "source_language": get_language_name(source_code),
            "target_language": get_language_name(target_code),
            "confidence": result["confidence"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/feedback")
async def submit_translation_feedback(request: FeedbackRequest):
    """
    Submit feedback for translation to improve future translations
    """
    try:
        source_code = get_language_code(request.source_language)
        target_code = get_language_code(request.target_language)
        
        # Store feedback
        translation_model.store_feedback(
            original_text=request.original_text,
            translated_text=request.translated_text,
            corrected_text=request.corrected_text,
            source_language=source_code,
            target_language=target_code,
            feedback_notes=request.feedback_notes
        )
        
        return {"message": "Feedback submitted successfully"}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error submitting feedback: {str(e)}")

@router.get("/supported-languages")
async def get_supported_languages():
    """
    Get list of supported languages for translation
    """
    return translation_model.get_supported_languages()

@router.get("/tribal-languages")
async def get_tribal_languages():
    """
    Get list of supported tribal languages for translation
    """
    all_languages = translation_model.get_supported_languages()
    tribal_languages = {code: name for code, name in all_languages.items() if is_tribal_language(code)}
    return tribal_languages
