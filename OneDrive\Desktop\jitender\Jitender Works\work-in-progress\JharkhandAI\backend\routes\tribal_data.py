from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Optional
from ..models.tribal_data_collection_model import TribalDataCollectionModel
from ..utils.auth import get_current_user
from pydantic import BaseModel

router = APIRouter(prefix="/api/tribal-data", tags=["tribal-data"])
data_collection_model = TribalDataCollectionModel()

class LanguageDataInput(BaseModel):
    language: str
    text: str
    dialect: Optional[str] = None
    metadata: Optional[Dict] = None

class FeedbackInput(BaseModel):
    language: str
    text_id: str
    feedback_type: str
    feedback_content: str

class DialectInput(BaseModel):
    language: str
    base_text: str
    dialect_text: str
    dialect_name: str
    region: Optional[str] = None

@router.post("/submit")
async def submit_language_data(data: LanguageDataInput, user = Depends(get_current_user)):
    """Submit new tribal language data"""
    result = data_collection_model.add_language_data(
        language=data.language,
        text=data.text,
        dialect=data.dialect,
        metadata={
            **data.metadata or {},
            "user_id": user.id if user else None
        }
    )
    
    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])
    return result

@router.post("/feedback")
async def submit_feedback(feedback: FeedbackInput, user = Depends(get_current_user)):
    """Submit feedback for language model improvement"""
    result = data_collection_model.add_user_feedback(
        language=feedback.language,
        text_id=feedback.text_id,
        feedback_type=feedback.feedback_type,
        feedback_content=feedback.feedback_content,
        user_id=user.id if user else None
    )
    
    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])
    return result

@router.post("/dialect")
async def submit_dialect_variation(dialect: DialectInput, user = Depends(get_current_user)):
    """Submit dialect variation for a tribal language"""
    result = data_collection_model.register_dialect_variation(
        language=dialect.language,
        base_text=dialect.base_text,
        dialect_text=dialect.dialect_text,
        dialect_name=dialect.dialect_name,
        region=dialect.region
    )
    
    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])
    return result

@router.get("/stats/{language}")
async def get_language_stats(language: str):
    """Get statistics about collected data for a specific language"""
    result = data_collection_model.get_language_statistics(language)
    
    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])
    return result