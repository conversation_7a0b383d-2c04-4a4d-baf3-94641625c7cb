from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Optional, Union

from models.tribal_nlu_model import TribalNLUModel
from utils.language_utils import is_tribal_language, get_language_code

router = APIRouter(prefix="/api/tribal-nlu", tags=["tribal-nlu"])

# Initialize tribal NLU model
tribal_nlu_model = TribalNLUModel()

class TextRequest(BaseModel):
    text: str
    language: str

class IntentResponse(BaseModel):
    intents: Dict[str, float]
    language: str

class EntityResponse(BaseModel):
    entities: List[Dict[str, Union[str, float]]]
    language: str

class SentimentResponse(BaseModel):
    sentiment: Dict[str, float]
    language: str

class FullAnalysisResponse(BaseModel):
    intents: Dict[str, float]
    entities: List[Dict[str, Union[str, float]]]
    sentiment: Dict[str, float]
    language: str

@router.post("/intent", response_model=IntentResponse)
async def recognize_intent(request: TextRequest):
    """
    Recognize intent from text in tribal languages
    """
    try:
        language_code = get_language_code(request.language)
        
        if not is_tribal_language(language_code):
            raise HTTPException(status_code=400, detail=f"Language {request.language} is not supported")
        
        intents = tribal_nlu_model.recognize_intent(request.text, language_code)
        
        return {
            "intents": intents,
            "language": language_code
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Intent recognition error: {str(e)}")

@router.post("/entities", response_model=EntityResponse)
async def extract_entities(request: TextRequest):
    """
    Extract entities from text in tribal languages
    """
    try:
        language_code = get_language_code(request.language)
        
        if not is_tribal_language(language_code):
            raise HTTPException(status_code=400, detail=f"Language {request.language} is not supported")
        
        entities = tribal_nlu_model.extract_entities(request.text, language_code)
        
        return {
            "entities": entities,
            "language": language_code
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Entity extraction error: {str(e)}")

@router.post("/sentiment", response_model=SentimentResponse)
async def analyze_sentiment(request: TextRequest):
    """
    Analyze sentiment of text in tribal languages
    """
    try:
        language_code = get_language_code(request.language)
        
        if not is_tribal_language(language_code):
            raise HTTPException(status_code=400, detail=f"Language {request.language} is not supported")
        
        sentiment = tribal_nlu_model.analyze_sentiment(request.text, language_code)
        
        return {
            "sentiment": sentiment,
            "language": language_code
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Sentiment analysis error: {str(e)}")

@router.post("/analyze", response_model=FullAnalysisResponse)
async def full_analysis(request: TextRequest):
    """
    Perform full analysis (intent, entities, sentiment) on text in tribal languages
    """
    try:
        language_code = get_language_code(request.language)
        
        if not is_tribal_language(language_code):
            raise HTTPException(status_code=400, detail=f"Language {request.language} is not supported")
        
        intents = tribal_nlu_model.recognize_intent(request.text, language_code)
        entities = tribal_nlu_model.extract_entities(request.text, language_code)
        sentiment = tribal_nlu_model.analyze_sentiment(request.text, language_code)
        
        return {
            "intents": intents,
            "entities": entities,
            "sentiment": sentiment,
            "language": language_code
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis error: {str(e)}")

@router.get("/supported-languages")
async def get_supported_languages():
    """
    Get list of supported tribal languages
    """
    return tribal_nlu_model.get_supported_languages()
