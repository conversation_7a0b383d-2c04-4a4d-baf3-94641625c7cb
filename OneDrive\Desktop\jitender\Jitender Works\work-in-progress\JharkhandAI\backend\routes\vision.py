from fastapi import APIRouter, File, UploadFile, Form, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Dict, Optional
import os
import tempfile
import shutil
import logging
from models.vision_model import vision_model

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/vision",
    tags=["vision"],
    responses={404: {"description": "Not found"}},
)

class FineTuneRequest(BaseModel):
    domain: str  # "agriculture" or "tourism"
    task: str  # "classification" or "detection"
    dataset_path: str
    epochs: Optional[int] = 3
    batch_size: Optional[int] = 8

class FineTuneResponse(BaseModel):
    model_dir: str
    domain: str
    task: str

@router.post("/classify")
async def classify_image(
    domain: str = Form("general"),
    file: UploadFile = File(...)
):
    """
    Classify an image.
    """
    try:
        # Create temporary file
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, file.filename)
        
        # Save uploaded file
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Classify image
        predictions = vision_model.classify_image(
            image_path=temp_file_path,
            domain=domain
        )
        
        # Clean up
        shutil.rmtree(temp_dir)
        
        return {
            "predictions": predictions,
            "domain": domain
        }
    except Exception as e:
        logger.error(f"Image classification error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Image classification error: {str(e)}")

@router.post("/detect")
async def detect_objects(
    domain: str = Form("general"),
    file: UploadFile = File(...)
):
    """
    Detect objects in an image.
    """
    try:
        # Create temporary file
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, file.filename)
        
        # Save uploaded file
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Detect objects
        detections = vision_model.detect_objects(
            image_path=temp_file_path,
            domain=domain
        )
        
        # Clean up
        shutil.rmtree(temp_dir)
        
        return {
            "detections": detections,
            "domain": domain
        }
    except Exception as e:
        logger.error(f"Object detection error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Object detection error: {str(e)}")

@router.post("/agriculture/crop-health")
async def analyze_crop_health(
    file: UploadFile = File(...)
):
    """
    Analyze crop health from an image.
    """
    try:
        # Create temporary file
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, file.filename)
        
        # Save uploaded file
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Analyze crop health
        analysis = vision_model.analyze_crop_health(
            image_path=temp_file_path
        )
        
        # Clean up
        shutil.rmtree(temp_dir)
        
        return analysis
    except Exception as e:
        logger.error(f"Crop health analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Crop health analysis error: {str(e)}")

@router.post("/agriculture/soil")
async def analyze_soil(
    file: UploadFile = File(...)
):
    """
    Analyze soil from an image.
    """
    try:
        # Create temporary file
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, file.filename)
        
        # Save uploaded file
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Analyze soil
        analysis = vision_model.analyze_soil(
            image_path=temp_file_path
        )
        
        # Clean up
        shutil.rmtree(temp_dir)
        
        return analysis
    except Exception as e:
        logger.error(f"Soil analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Soil analysis error: {str(e)}")

@router.post("/tourism/landmark")
async def identify_landmark(
    file: UploadFile = File(...)
):
    """
    Identify a landmark from an image.
    """
    try:
        # Create temporary file
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, file.filename)
        
        # Save uploaded file
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Identify landmark
        landmark_info = vision_model.identify_landmark(
            image_path=temp_file_path
        )
        
        # Clean up
        shutil.rmtree(temp_dir)
        
        return landmark_info
    except Exception as e:
        logger.error(f"Landmark identification error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Landmark identification error: {str(e)}")

@router.post("/fine-tune", response_model=FineTuneResponse)
async def fine_tune_model(request: FineTuneRequest, background_tasks: BackgroundTasks):
    """
    Fine-tune a vision model for a specific domain and task.
    This is a long-running task, so it runs in the background.
    """
    try:
        # Check if dataset exists
        if not os.path.exists(request.dataset_path):
            raise HTTPException(status_code=404, detail=f"Dataset not found at {request.dataset_path}")
        
        # Check if domain is valid
        if request.domain not in ["agriculture", "tourism"]:
            raise HTTPException(status_code=400, detail=f"Invalid domain: {request.domain}. Must be 'agriculture' or 'tourism'")
        
        # Check if task is valid
        if request.task not in ["classification", "detection"]:
            raise HTTPException(status_code=400, detail=f"Invalid task: {request.task}. Must be 'classification' or 'detection'")
        
        # Start fine-tuning in the background
        def fine_tune_task():
            try:
                result = vision_model.fine_tune(
                    domain=request.domain,
                    task=request.task,
                    dataset_path=request.dataset_path,
                    epochs=request.epochs,
                    batch_size=request.batch_size
                )
                logger.info(f"Fine-tuning completed: {result}")
            except Exception as e:
                logger.error(f"Fine-tuning error: {str(e)}")
        
        background_tasks.add_task(fine_tune_task)
        
        return {
            "model_dir": f"./models/fine_tuned/{request.domain}-{request.task}",
            "domain": request.domain,
            "task": request.task
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Fine-tuning setup error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Fine-tuning setup error: {str(e)}")

@router.post("/upload-image-dataset")
async def upload_image_dataset(
    domain: str = Form(...),  # "agriculture" or "tourism"
    task: str = Form(...),  # "classification" or "detection"
    files: List[UploadFile] = File(...),
    annotations: Optional[UploadFile] = File(None)
):
    """
    Upload images for fine-tuning.
    For classification, upload images organized by class.
    For detection, upload images with corresponding annotations.
    """
    try:
        # Check if domain is valid
        if domain not in ["agriculture", "tourism"]:
            raise HTTPException(status_code=400, detail=f"Invalid domain: {domain}. Must be 'agriculture' or 'tourism'")
        
        # Check if task is valid
        if task not in ["classification", "detection"]:
            raise HTTPException(status_code=400, detail=f"Invalid task: {task}. Must be 'classification' or 'detection'")
        
        # Create dataset directory
        dataset_dir = f"./data/vision_datasets/{domain}/{task}"
        os.makedirs(dataset_dir, exist_ok=True)
        
        # Save uploaded files
        saved_files = []
        for file in files:
            file_path = os.path.join(dataset_dir, file.filename)
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            saved_files.append(file_path)
        
        # Save annotations if provided
        annotations_path = None
        if annotations:
            annotations_path = os.path.join(dataset_dir, annotations.filename)
            with open(annotations_path, "wb") as buffer:
                shutil.copyfileobj(annotations.file, buffer)
        
        return {
            "files": saved_files,
            "annotations": annotations_path,
            "dataset_path": dataset_dir,
            "domain": domain,
            "task": task
        }
    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Upload error: {str(e)}")

@router.get("/models")
async def get_models():
    """
    Get a list of available fine-tuned models.
    """
    return {
        "models": list(vision_model.fine_tuned_models.keys())
    }
