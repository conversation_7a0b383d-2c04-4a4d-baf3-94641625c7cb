from fastapi import APIRouter, UploadFile, File, HTTPException
from typing import Optional
from ..models.enhanced_speech_model import EnhancedSpeechModel
from ..models.enhanced_language_model import JharkhandLanguageModel
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

# Initialize models
speech_model = EnhancedSpeechModel()
language_model = JharkhandLanguageModel()

@router.post("/speech-to-text")
async def speech_to_text(
    audio: UploadFile = File(...),
    language: str = "en",
    real_time: bool = False
):
    """Convert speech to text in the specified language"""
    try:
        # Validate language code
        if language not in speech_model.speech_to_text_models:
            raise HTTPException(
                status_code=400,
                detail=f"Language {language} is not supported"
            )

        # Save uploaded audio file temporarily
        audio_path = f"temp_{audio.filename}"
        with open(audio_path, "wb") as buffer:
            content = await audio.read()
            buffer.write(content)

        # Process audio using speech model
        result = speech_model.speech_to_text(
            audio_file=audio_path,
            language=language,
            real_time=real_time
        )

        # Clean up temporary file
        import os
        os.remove(audio_path)

        return {
            "text": result.get("text", ""),
            "confidence": result.get("confidence", 0),
            "language": language
        }

    except Exception as e:
        logger.error(f"Error processing speech to text: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error processing audio: {str(e)}"
        )

@router.post("/text-to-speech")
async def text_to_speech(
    text: str,
    language: str = "en",
    voice_id: Optional[str] = None
):
    """Convert text to speech in the specified language"""
    try:
        # Validate language code
        if language not in speech_model.text_to_speech_models:
            raise HTTPException(
                status_code=400,
                detail=f"Language {language} is not supported"
            )

        # Generate speech using model
        audio_data = speech_model.text_to_speech(
            text=text,
            language=language,
            voice_id=voice_id
        )

        return {
            "audio_data": audio_data,
            "format": "wav",
            "language": language
        }

    except Exception as e:
        logger.error(f"Error processing text to speech: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating speech: {str(e)}"
        )