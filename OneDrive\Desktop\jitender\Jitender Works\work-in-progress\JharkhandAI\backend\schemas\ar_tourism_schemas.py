from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

class CulturalElementBase(BaseModel):
    name: str
    description: str
    interaction_type: str = Field(default="view", description="Type of interaction: view, interact, or animate")
    position: Dict[str, float] = Field(default_factory=dict, description="3D position coordinates")
    scale: Dict[str, float] = Field(default_factory=dict, description="3D scale factors")
    rotation: Dict[str, float] = Field(default_factory=dict, description="3D rotation angles")

class ARExperienceRequest(BaseModel):
    location_name: str
    cultural_elements: List[CulturalElementBase]
    language: str = Field(default="en", description="Language code for translations")

class ARExperienceResponse(BaseModel):
    location: str
    elements: List[Dict[str, Any]]
    language: str

class TourismPreferences(BaseModel):
    interests: List[str] = Field(default_factory=list, description="List of tourist interests")
    duration: int = Field(default=3, description="Number of days for the trip")
    include_tribal: bool = Field(default=True, description="Include tribal destinations")
    budget: str = Field(default="medium", description="Budget category: low, medium, high")
    language: str = Field(default="en", description="Preferred language for content")

class VirtualExhibitBase(BaseModel):
    name: str
    description: str
    tribe: Optional[str] = None
    cultural_significance: Optional[str] = None
    artifact_type: str
    creation_date: Optional[datetime] = None
    preservation_status: Optional[str] = None
    digital_model_url: Optional[str] = None
    images: List[str] = Field(default_factory=list)
    audio_narration_url: Optional[str] = None
    interactive_features: List[str] = Field(default_factory=list)

class VirtualExhibitCreate(VirtualExhibitBase):
    pass

class VirtualExhibitResponse(VirtualExhibitBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True