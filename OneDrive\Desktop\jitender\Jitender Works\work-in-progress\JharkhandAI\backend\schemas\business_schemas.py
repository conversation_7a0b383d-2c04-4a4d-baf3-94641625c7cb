from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional

# Business Advisory Schemas
class BusinessAdvisoryRequest(BaseModel):
    business_name: str
    business_type: str
    location: str
    years_in_operation: Optional[int] = None
    number_of_employees: Optional[int] = None
    monthly_revenue: Optional[float] = None
    monthly_expenses: Optional[float] = None
    business_goals: Optional[List[str]] = None
    challenges: Optional[List[str]] = None

class BusinessRecommendation(BaseModel):
    title: str
    description: str
    impact: str
    implementation_difficulty: str
    estimated_cost: str

class GrowthOpportunity(BaseModel):
    title: str
    description: str
    potential_impact: str

class BusinessAdvisoryResponse(BaseModel):
    recommendations: List[BusinessRecommendation]
    market_insights: List[str]
    growth_opportunities: List[GrowthOpportunity]

# Financial Planning Schemas
class FinancialDataRequest(BaseModel):
    business_name: str
    business_type: str
    monthly_revenue: float
    monthly_expenses: float
    fixed_costs: Optional[float] = None
    variable_costs: Optional[float] = None
    current_assets: Optional[float] = None
    current_liabilities: Optional[float] = None
    projection_months: int = 12
    growth_rate: float = 5.0
    seasonal_factors: Optional[bool] = False
    investment_plans: Optional[str] = None
    expansion_plans: Optional[str] = None

class CashFlowResponse(BaseModel):
    revenue_projections: List[float]
    expense_projections: List[float]
    profit_projections: List[float]
    insights: List[str]

class InvestmentNeeds(BaseModel):
    expansion: float
    technology: float
    marketing: float
    other: Optional[float] = 0
    total: float

class RiskAssessment(BaseModel):
    name: str
    description: str
    mitigation: str

class FinancialPlanResponse(BaseModel):
    investment_needs: InvestmentNeeds
    break_even_point: int
    roi: float
    cash_reserve_needed: float
    debt_service_ratio: float
    financing_options: List[str]
    recommended_actions: List[str]
    risk_assessment: List[RiskAssessment]

# Loan Assistance Schemas
class LoanEligibilityRequest(BaseModel):
    business_name: str
    business_type: str
    registration_number: Optional[str] = None
    year_established: Optional[int] = None
    annual_revenue: float
    monthly_profit: float
    existing_loans: Optional[float] = None
    purpose: str
    amount_required: float
    repayment_period: int

class LoanEligibilityResponse(BaseModel):
    eligible: bool
    eligible_amount: Optional[float] = None
    suggestions: List[str]

class LoanApplicationRequest(BaseModel):
    business_name: str
    business_type: str
    registration_number: Optional[str] = None
    year_established: Optional[int] = None
    annual_revenue: float
    monthly_profit: float
    existing_loans: Optional[float] = None
    purpose: str
    amount_required: float
    repayment_period: int
    scheme_id: str

class LoanApplicationResponse(BaseModel):
    application_id: str
    date_submitted: str
    scheme_name: str
    amount_requested: float
    status: str
    required_documents: List[str]
    submission_process: str
    timeline: str

class LoanScheme(BaseModel):
    id: str
    name: str
    max_amount: float
    interest_rate: float
    eligibility: str
    subsidy: str

# Marketing Schemas
class MarketingStrategyRequest(BaseModel):
    business_name: str
    industry: str
    business_description: str
    target_audience: str
    location: str
    usp: Optional[str] = None
    goals: Optional[List[str]] = []
    budget: Optional[str] = None
    existing_channels: Optional[List[str]] = []

class ContentIdea(BaseModel):
    type: str
    topics: List[str]

class MarketingStrategyResponse(BaseModel):
    strategy_summary: str
    target_channels: List[str]
    content_ideas: List[ContentIdea]
    budget_allocation: Dict[str, str]
    timeline: Dict[str, str]

class MarketingContentRequest(BaseModel):
    business_profile: MarketingStrategyRequest
    content_type: str
    platform: str
    count: int = 3

class ContentItem(BaseModel):
    text: str
    hashtags: Optional[List[str]] = None

class MarketingContentResponse(BaseModel):
    content: List[ContentItem]
    tips: List[str]

class CompetitorAnalysisRequest(BaseModel):
    business_name: str
    industry: str
    business_description: str
    target_audience: str
    location: str
    usp: Optional[str] = None

class Competitor(BaseModel):
    name: str
    description: str

class CompetitorAnalysisResponse(BaseModel):
    top_competitors: List[Competitor]
    competitive_advantages: List[str]
    competitive_disadvantages: List[str]
    market_gaps: List[str]
    recommendations: List[str]
