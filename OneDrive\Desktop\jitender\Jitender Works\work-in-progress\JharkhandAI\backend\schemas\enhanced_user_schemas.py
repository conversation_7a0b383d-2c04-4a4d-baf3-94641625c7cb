from pydantic import BaseModel, EmailStr, constr, validator
from typing import Optional, List, Dict
from datetime import datetime

class AadhaarVerification(BaseModel):
    aadhaar_number: str
    verification_status: bool = False
    verified_at: Optional[datetime] = None
    verification_method: str = "OTP"

    @validator('aadhaar_number')
    def validate_aadhaar(cls, v):
        if not v.isdigit() or len(v) != 12:
            raise ValueError('Aadhaar number must be 12 digits')
        return v

class UserPreferences(BaseModel):
    language: str = "en"
    notifications_enabled: bool = True
    theme: str = "light"
    accessibility_settings: Dict = {
        "font_size": "medium",
        "high_contrast": False,
        "screen_reader": False
    }

class UserActivity(BaseModel):
    last_login: Optional[datetime] = None
    login_count: int = 0
    visited_sections: List[str] = []
    search_history: List[str] = []

class EnhancedUserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    district: Optional[str] = None
    phone_number: Optional[str] = None
    preferences: Optional[UserPreferences] = None
    activity: Optional[UserActivity] = None

    @validator('phone_number')
    def validate_phone(cls, v):
        if v is not None:
            if not v.isdigit() or len(v) != 10:
                raise ValueError('Phone number must be 10 digits')
        return v

class EnhancedUserCreate(EnhancedUserBase):
    password: str
    aadhaar_verification: AadhaarVerification

class EnhancedUserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    district: Optional[str] = None
    phone_number: Optional[str] = None
    password: Optional[str] = None
    preferences: Optional[UserPreferences] = None

class EnhancedUserResponse(EnhancedUserBase):
    id: int
    is_active: bool
    is_admin: bool
    aadhaar_verified: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class TokenBase(BaseModel):
    access_token: str
    token_type: str
    expires_at: datetime
    user: EnhancedUserResponse

class TokenData(BaseModel):
    username: Optional[str] = None
    exp: Optional[datetime] = None
    is_admin: bool
    aadhaar_verified: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class EnhancedToken(TokenBase):
    pass

class EnhancedTokenData(TokenData):
    pass