from pydantic import BaseModel
from typing import Dict, List, Optional
from datetime import datetime

class FeedbackBase(BaseModel):
    user_id: Optional[str]
    feature_id: str
    rating: int
    comment: Optional[str]
    sentiment: Optional[str]
    tags: Optional[List[str]]
    metadata: Optional[Dict]
    created_at: datetime = datetime.now()

class FeedbackCreate(FeedbackBase):
    pass

class FeedbackResponse(FeedbackBase):
    id: int
    
    class Config:
        orm_mode = True

class FeatureUsageBase(BaseModel):
    feature_id: str
    user_id: Optional[str]
    action: str
    duration: Optional[float]
    success: bool
    error_type: Optional[str]
    metadata: Optional[Dict]
    timestamp: datetime = datetime.now()

class FeatureUsageCreate(FeatureUsageBase):
    pass

class FeatureUsageResponse(FeatureUsageBase):
    id: int
    
    class Config:
        orm_mode = True

class UserSatisfactionMetrics(BaseModel):
    overall_rating: float
    feature_ratings: Dict[str, float]
    sentiment_distribution: Dict[str, int]
    total_feedback_count: int
    period: str  # daily, weekly, monthly
    start_date: datetime
    end_date: datetime