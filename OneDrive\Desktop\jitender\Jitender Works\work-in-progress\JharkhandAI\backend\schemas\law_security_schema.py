from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

# Enums
class CrimeTypeEnum(str, Enum):
    THEFT = "theft"
    ASSAULT = "assault"
    CYBERCRIME = "cybercrime"
    FRAUD = "fraud"
    DOMESTIC_VIOLENCE = "domestic_violence"
    LAND_DISPUTE = "land_dispute"
    OTHER = "other"

class CaseStatusEnum(str, Enum):
    REPORTED = "reported"
    INVESTIGATING = "investigating"
    RESOLVED = "resolved"
    CLOSED = "closed"
    PENDING = "pending"

# Crime Report Schemas
class CrimeReportBase(BaseModel):
    crime_type: str
    description: str
    location: str
    district: str
    date_of_incident: Optional[datetime] = None
    is_anonymous: bool = False
    evidence_urls: Optional[List[str]] = None

class CrimeReportCreate(CrimeReportBase):
    pass

class CrimeReportResponse(CrimeReportBase):
    id: int
    user_id: Optional[int] = None
    status: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class CrimeReportUpdateBase(BaseModel):
    update_text: str
    updated_by: str
    status: Optional[str] = None

class CrimeReportUpdateCreate(CrimeReportUpdateBase):
    pass

class CrimeReportUpdateResponse(CrimeReportUpdateBase):
    id: int
    crime_report_id: int
    created_at: datetime
    
    class Config:
        orm_mode = True

# Traffic Incident Schemas
class TrafficIncidentBase(BaseModel):
    incident_type: str
    description: str
    location: str
    district: str
    severity: int = Field(..., ge=1, le=5)

class TrafficIncidentCreate(TrafficIncidentBase):
    pass

class TrafficIncidentResponse(TrafficIncidentBase):
    id: int
    status: str
    reported_by: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

# Legal Assistance Schemas
class LegalAssistanceBase(BaseModel):
    case_type: str
    description: str
    district: str

class LegalAssistanceCreate(LegalAssistanceBase):
    pass

class LegalAssistanceResponse(LegalAssistanceBase):
    id: int
    user_id: int
    status: str
    assigned_to: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class LegalDocumentBase(BaseModel):
    document_type: str
    document_url: str
    description: Optional[str] = None

class LegalDocumentCreate(LegalDocumentBase):
    pass

class LegalDocumentResponse(LegalDocumentBase):
    id: int
    legal_case_id: int
    created_at: datetime
    
    class Config:
        orm_mode = True

class LegalCaseUpdateBase(BaseModel):
    update_text: str
    updated_by: str
    status: Optional[str] = None

class LegalCaseUpdateCreate(LegalCaseUpdateBase):
    pass

class LegalCaseUpdateResponse(LegalCaseUpdateBase):
    id: int
    legal_case_id: int
    created_at: datetime
    
    class Config:
        orm_mode = True

# Crime Analytics Schemas
class CrimeHotspotBase(BaseModel):
    district: str
    location: str
    crime_types: List[str]
    risk_level: int = Field(..., ge=1, le=5)
    description: str
    recommendations: str

class CrimeHotspotResponse(CrimeHotspotBase):
    id: int
    last_updated: datetime
    
    class Config:
        orm_mode = True

class CrimeAnalyticsResponse(BaseModel):
    total_crimes: int
    crime_types_distribution: Dict[str, int]
    location_distribution: Dict[str, int]
    hotspots: List[CrimeHotspotResponse]
    time_period: str
    district: Optional[str] = None
