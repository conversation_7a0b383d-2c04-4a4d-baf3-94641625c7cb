from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

# Destination Schemas
class DestinationBase(BaseModel):
    name: str
    description: str
    location: str
    category: str
    attractions: Optional[List[str]] = None
    best_time: Optional[str] = None
    how_to_reach: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    image_url: Optional[str] = None
    gallery_urls: Optional[List[str]] = None
    is_featured: Optional[bool] = False

class DestinationCreate(DestinationBase):
    pass

class DestinationUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    category: Optional[str] = None
    attractions: Optional[List[str]] = None
    best_time: Optional[str] = None
    how_to_reach: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    image_url: Optional[str] = None
    gallery_urls: Optional[List[str]] = None
    is_featured: Optional[bool] = None

class DestinationInDB(DestinationBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class DestinationResponse(BaseModel):
    destination: DestinationInDB

class DestinationList(BaseModel):
    destinations: List[DestinationInDB]

# Tribal Heritage Site Schemas
class PointOfInterestBase(BaseModel):
    name: str
    description: str
    cultural_significance: Optional[str] = None
    historical_context: Optional[str] = None
    image_url: Optional[str] = None
    model_url: Optional[str] = None
    has_3d_model: Optional[bool] = False
    position_x: Optional[float] = None
    position_y: Optional[float] = None
    position_z: Optional[float] = None

class PointOfInterestCreate(PointOfInterestBase):
    site_id: int

class PointOfInterestInDB(PointOfInterestBase):
    id: int
    site_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class TribalHeritageSiteBase(BaseModel):
    name: str
    description: str
    tribe: str
    location: str
    cultural_significance: Optional[str] = None
    historical_context: Optional[str] = None
    visiting_hours: Optional[str] = None
    entry_fee: Optional[str] = None
    contact_info: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    image_url: Optional[str] = None
    panoramic_image_url: Optional[str] = None
    has_ar_tour: Optional[bool] = False

class TribalHeritageSiteCreate(TribalHeritageSiteBase):
    pass

class TribalHeritageSiteUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    tribe: Optional[str] = None
    location: Optional[str] = None
    cultural_significance: Optional[str] = None
    historical_context: Optional[str] = None
    visiting_hours: Optional[str] = None
    entry_fee: Optional[str] = None
    contact_info: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    image_url: Optional[str] = None
    panoramic_image_url: Optional[str] = None
    has_ar_tour: Optional[bool] = None

class TribalHeritageSiteInDB(TribalHeritageSiteBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class TribalHeritageSiteWithPOI(TribalHeritageSiteInDB):
    points_of_interest: List[PointOfInterestInDB] = []

class TribalHeritageSiteResponse(BaseModel):
    site: Union[TribalHeritageSiteInDB, TribalHeritageSiteWithPOI, Dict[str, Any]]

class TribalHeritageSiteList(BaseModel):
    sites: List[TribalHeritageSiteInDB]

# Tourism Event Schemas
class TourismEventBase(BaseModel):
    name: str
    description: str
    event_type: str
    location: str
    destination_id: Optional[int] = None
    start_date: datetime
    end_date: datetime
    image_url: Optional[str] = None
    website: Optional[str] = None
    contact_info: Optional[str] = None
    is_recurring: Optional[bool] = False
    recurrence_pattern: Optional[str] = None

class TourismEventCreate(TourismEventBase):
    pass

class TourismEventUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    event_type: Optional[str] = None
    location: Optional[str] = None
    destination_id: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    image_url: Optional[str] = None
    website: Optional[str] = None
    contact_info: Optional[str] = None
    is_recurring: Optional[bool] = None
    recurrence_pattern: Optional[str] = None

class TourismEventInDB(TourismEventBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class TourismEventResponse(BaseModel):
    event: TourismEventInDB

class TourismEventList(BaseModel):
    events: List[TourismEventInDB]

# AR Tour Schemas
class ARTourBase(BaseModel):
    name: str
    description: str
    tour_type: str
    duration_minutes: Optional[int] = None
    instructions: Optional[str] = None
    ar_marker_url: Optional[str] = None
    scene_data: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = True

class ARTourCreate(ARTourBase):
    site_id: int

class ARTourUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    tour_type: Optional[str] = None
    duration_minutes: Optional[int] = None
    instructions: Optional[str] = None
    ar_marker_url: Optional[str] = None
    scene_data: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class ARTourInDB(ARTourBase):
    id: int
    site_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class ARTourWithDetails(ARTourInDB):
    site: TribalHeritageSiteInDB
    points_of_interest: List[PointOfInterestInDB] = []

class ARTourResponse(BaseModel):
    tour: Union[ARTourInDB, ARTourWithDetails, Dict[str, Any]]

class ARTourList(BaseModel):
    tours: List[ARTourInDB]

# AI-Powered Tourism Feature Schemas
class ChatbotQuery(BaseModel):
    query: str = Field(..., description="User's question or message for the tourism chatbot")

class ChatbotResponse(BaseModel):
    response: str = Field(..., description="Chatbot's response to the user's query")

class ItineraryRequest(BaseModel):
    days: int = Field(..., description="Number of days for the itinerary")
    interests: List[str] = Field(..., description="List of traveler's interests")
    budget: str = Field(..., description="Budget range (budget, medium, luxury)")
    travelMode: str = Field(..., description="Preferred mode of travel")
    includeTribal: bool = Field(..., description="Whether to include tribal experiences")
    startLocation: str = Field(..., description="Starting location for the itinerary")

class ItineraryDay(BaseModel):
    title: str
    activities: List[Dict[str, Any]]
    accommodation: Optional[Dict[str, Any]] = None
    tips: Optional[str] = None

class ItineraryData(BaseModel):
    days: List[ItineraryDay]
    summary: Dict[str, Any]

class ItineraryResponse(BaseModel):
    itinerary: ItineraryData

class MarketingRequest(BaseModel):
    destination: Optional[str] = None
    targetAudience: str
    contentType: str
    tone: str
    includeImages: bool
    highlightCulture: bool
    highlightNature: bool
    highlightAdventure: bool
    customFocus: Optional[str] = None

class MarketingContent(BaseModel):
    contentType: str
    text: str
    title: Optional[str] = None
    tagline: Optional[str] = None
    imageUrls: Optional[List[str]] = None
    hashtags: Optional[List[str]] = None
    sections: Optional[List[Dict[str, str]]] = None

class MarketingResponse(BaseModel):
    content: MarketingContent

class TourismStatistics(BaseModel):
    total_destinations: int
    total_tribal_sites: int
    total_events: int
    total_ar_tours: int
    visitor_statistics: Dict[str, Any]
    economic_impact: Dict[str, Any]
