# Test configuration and setup
from pathlib import Path
import sys

# Add the backend directory to Python path for imports
BACKEND_DIR = Path(__file__).parent.parent
sys.path.append(str(BACKEND_DIR))

# Test constants
TEST_DATA_DIR = Path(__file__).parent / 'test_data'
TEST_TIMEOUT = 30  # seconds
LOW_BANDWIDTH_THRESHOLD = 1000  # kbps

# Ensure test data directory exists
TEST_DATA_DIR.mkdir(exist_ok=True)