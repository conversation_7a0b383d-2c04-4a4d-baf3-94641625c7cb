import pytest
from fastapi.testclient import TestClient
from pathlib import Path
from main import app
from models.language_model import LanguageModel
from models.translation_model import TranslationModel
from . import TEST_DATA_DIR

client = TestClient(app)

@pytest.fixture
def test_languages():
    return ['en', 'hi', 'ho', 'sa']  # English, Hindi, Ho, Santhali

@pytest.fixture
def sample_text():
    return {
        'en': 'Hello, how are you?',
        'hi': 'नमस्ते, आप कैसे हैं?',
        'ho': '<PERSON><PERSON>, am okayam?',
        'sa': '<PERSON><PERSON>, चेत् काणा?'
    }

def test_language_switching(test_languages):
    """Test language switching functionality"""
    for lang in test_languages:
        response = client.post(
            "/api/language/switch",
            json={"language": lang}
        )
        assert response.status_code == 200
        assert response.json()["language"] == lang

def test_translation_api(sample_text):
    """Test translation API endpoints"""
    for source_lang in sample_text.keys():
        for target_lang in sample_text.keys():
            if source_lang != target_lang:
                response = client.post(
                    "/api/translation/translate",
                    json={
                        "text": sample_text[source_lang],
                        "source_language": source_lang,
                        "target_language": target_lang
                    }
                )
                assert response.status_code == 200
                assert "translated_text" in response.json()

def test_missing_translations():
    """Test handling of missing translations"""
    response = client.get("/api/translation/missing")
    assert response.status_code == 200
    missing_translations = response.json()
    assert isinstance(missing_translations, list)

def test_translation_feedback():
    """Test translation feedback submission"""
    feedback_data = {
        "original_text": "Hello",
        "translated_text": "नमस्ते",
        "source_language": "en",
        "target_language": "hi",
        "rating": 5,
        "comment": "Accurate translation"
    }
    response = client.post(
        "/api/translation/feedback",
        json=feedback_data
    )
    assert response.status_code == 200
    assert response.json()["status"] == "success"

def test_language_model_performance():
    """Test language model performance and accuracy"""
    model = LanguageModel()
    test_text = "This is a test sentence"
    
    # Test language detection
    detected_lang = model.detect_language(test_text)
    assert detected_lang in ['en', 'hi', 'ho', 'sa']
    
    # Test sentiment analysis
    sentiment = model.analyze_sentiment(test_text)
    assert "sentiment" in sentiment
    assert "confidence" in sentiment

def test_translation_model_performance():
    """Test translation model performance"""
    model = TranslationModel()
    source_text = "Hello World"
    source_lang = "en"
    target_lang = "hi"
    
    translation = model.translate(source_text, source_lang, target_lang)
    assert translation is not None
    assert len(translation) > 0