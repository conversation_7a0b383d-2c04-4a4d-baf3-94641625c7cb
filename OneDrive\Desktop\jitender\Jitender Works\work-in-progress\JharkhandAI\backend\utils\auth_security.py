from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
from jose import JW<PERSON><PERSON>r, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session
from sqlalchemy import Column, String, DateTime, Boolean
import os
import secrets
import re
from typing import Dict

# Import database and models
from database_enhanced import get_db
from models.database_models import User

# Configure password hashing with stronger algorithm
pwd_context = CryptContext(schemes=["argon2"], deprecated="auto")

# Configure JWT with stronger settings
SECRET_KEY = os.getenv("SECRET_KEY")
if not SECRET_KEY:
    raise ValueError("SECRET_KEY environment variable is not set")

REFRESH_SECRET_KEY = os.getenv("REFRESH_SECRET_KEY")
if not REFRESH_SECRET_KEY:
    raise ValueError("REFRESH_SECRET_KEY environment variable is not set")

ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 15  # Shorter expiration for access tokens
REFRESH_TOKEN_EXPIRE_DAYS = 7

# Configure OAuth2
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/token")

# Password policy configuration
PASSWORD_MIN_LENGTH = 12
PASSWORD_PATTERN = re.compile(
    r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{12,}$"
)

# Session management
active_sessions: Dict[str, dict] = {}
MAX_SESSIONS_PER_USER = 5

def validate_password(password: str) -> bool:
    """Validate password against security policy"""
    if len(password) < PASSWORD_MIN_LENGTH:
        return False
    if not PASSWORD_PATTERN.match(password):
        return False
    return True

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash using Argon2"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate a password hash using Argon2"""
    if not validate_password(password):
        raise ValueError(
            "Password must be at least 12 characters long and contain uppercase, "
            "lowercase, numbers, and special characters"
        )
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token with enhanced security"""
    to_encode = data.copy()
    expire = datetime.utcnow() + (
        expires_delta if expires_delta else timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    to_encode.update({"exp": expire, "type": "access"})
    # Add additional claims for security
    to_encode.update({
        "iat": datetime.utcnow(),  # Issued at
        "jti": secrets.token_hex(32)  # Unique token ID
    })
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def create_refresh_token(data: dict) -> str:
    """Create a JWT refresh token"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({
        "exp": expire,
        "type": "refresh",
        "iat": datetime.utcnow(),
        "jti": secrets.token_hex(32)
    })
    return jwt.encode(to_encode, REFRESH_SECRET_KEY, algorithm=ALGORITHM)

def verify_token(token: str, secret_key: str, token_type: str) -> dict:
    """Verify and decode a JWT token"""
    try:
        payload = jwt.decode(token, secret_key, algorithms=[ALGORITHM])
        if payload.get("type") != token_type:
            raise JWTError("Invalid token type")
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def manage_user_session(user_id: str, session_id: str):
    """Manage user sessions with limits"""
    if user_id not in active_sessions:
        active_sessions[user_id] = {}
    
    # Remove expired sessions
    current_time = datetime.utcnow()
    active_sessions[user_id] = {
        sid: data for sid, data in active_sessions[user_id].items()
        if data["expires_at"] > current_time
    }
    
    # Check session limit
    if len(active_sessions[user_id]) >= MAX_SESSIONS_PER_USER:
        # Remove oldest session
        oldest_session = min(
            active_sessions[user_id].items(),
            key=lambda x: x[1]["created_at"]
        )[0]
        del active_sessions[user_id][oldest_session]
    
    # Add new session
    active_sessions[user_id][session_id] = {
        "created_at": datetime.utcnow(),
        "expires_at": datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    }

async def authenticate_user(db: Session, username: str, password: str):
    """Authenticate a user with enhanced security"""
    user = db.query(User).filter(User.username == username).first()
    if not user:
        # Use constant time comparison to prevent timing attacks
        pwd_context.dummy_verify()
        return False
    
    if not verify_password(password, user.hashed_password):
        return False
    
    if not user.is_active:
        raise HTTPException(status_code=400, detail="User account is disabled")
    
    return user

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """Get the current user from the JWT token with enhanced validation"""
    try:
        payload = verify_token(token, SECRET_KEY, "access")
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    # Check if session is still valid
    session_id = payload.get("jti")
    if not session_id or session_id not in active_sessions.get(str(user.id), {}):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Session has expired"
        )
    
    return user

async def refresh_access_token(refresh_token: str, db: Session):
    """Create a new access token using a refresh token"""
    try:
        payload = verify_token(refresh_token, REFRESH_SECRET_KEY, "refresh")
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
            
        user = db.query(User).filter(User.username == username).first()
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
            
        # Create new access token
        access_token = create_access_token(data={"sub": username})
        return {"access_token": access_token, "token_type": "bearer"}
        
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )