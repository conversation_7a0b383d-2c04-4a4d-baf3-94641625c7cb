from typing import Dict, List, Optional, Union

# Define language mappings
LANGUAGE_CODES = {
    # Standard languages
    "english": "en",
    "hindi": "hi",
    "bengali": "bn",
    
    # Tribal languages of Jharkhand
    "santhali": "sa",
    "santali": "sa",
    "santhal": "sa",
    "ho": "ho",
    "mundari": "mun",
    "munda": "mun",
    "kurukh": "kru",
    "oraon": "kru",
    "kharia": "khr",
    
    # Keep the codes themselves as valid keys
    "en": "en",
    "hi": "hi",
    "bn": "bn",
    "sa": "sa",
    "ho": "ho",
    "mun": "mun",
    "kru": "kru",
    "khr": "khr"
}

LANGUAGE_NAMES = {
    "en": "English",
    "hi": "Hindi",
    "bn": "Bengali",
    "sa": "Santhali",
    "ho": "Ho",
    "mun": "Mu<PERSON>ri",
    "kru": "<PERSON><PERSON><PERSON>",
    "khr": "Kharia"
}

# Define tribal languages
TRIBAL_LANGUAGES = ["sa", "ho", "mun", "kru", "khr"]

def get_language_code(language: Optional[Union[str, None]]) -> str:
    """
    Get standardized language code from language name or code
    
    Args:
        language: Language name or code
        
    Returns:
        Standardized language code
    """
    if not language:
        return "en"  # Default to English
    
    language_str = str(language).lower().strip()
    
    # Return the language code if it exists in our mapping
    return LANGUAGE_CODES.get(language_str, "en")

def get_language_name(language_code: str) -> str:
    """
    Get language name from language code
    
    Args:
        language_code: Language code
        
    Returns:
        Language name
    """
    return LANGUAGE_NAMES.get(language_code, "English")

def is_tribal_language(language_code: str) -> bool:
    """
    Check if a language is a tribal language of Jharkhand
    
    Args:
        language_code: Language code
        
    Returns:
        True if the language is a tribal language, False otherwise
    """
    return language_code in TRIBAL_LANGUAGES

def get_all_languages() -> Dict[str, str]:
    """
    Get all supported languages
    
    Returns:
        Dictionary of language codes and names
    """
    return LANGUAGE_NAMES

def get_tribal_languages() -> Dict[str, str]:
    """
    Get all supported tribal languages
    
    Returns:
        Dictionary of tribal language codes and names
    """
    return {code: name for code, name in LANGUAGE_NAMES.items() if code in TRIBAL_LANGUAGES}
